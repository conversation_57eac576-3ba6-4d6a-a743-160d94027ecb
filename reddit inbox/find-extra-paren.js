const fs = require('fs');

const content = fs.readFileSync('scripts/content-new.js', 'utf8');
const lines = content.split('\n');

let balance = 0;
let foundProblem = false;

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  let lineBalance = 0;
  
  for (let char of line) {
    if (char === '(') {
      balance++;
      lineBalance++;
    }
    if (char === ')') {
      balance--;
      lineBalance--;
    }
  }
  
  // Si la balance devient négative, on a trouvé une parenthèse fermante en trop
  if (balance < 0 && !foundProblem) {
    console.log(`❌ PROBLÈME TROUVÉ à la ligne ${i + 1}:`);
    console.log(`   Balance: ${lineBalance} (total: ${balance})`);
    console.log(`   Ligne: ${line.trim()}`);
    console.log('');
    
    // Montrer le contexte (lignes précédentes et suivantes)
    console.log('Contexte:');
    for (let j = Math.max(0, i - 2); j <= Math.min(lines.length - 1, i + 2); j++) {
      const marker = j === i ? '>>> ' : '    ';
      console.log(`${marker}${j + 1}: ${lines[j].trim()}`);
    }
    
    foundProblem = true;
    break;
  }
}

if (!foundProblem) {
  console.log('Aucune parenthèse fermante en trop trouvée.');
  console.log(`Balance finale: ${balance}`);
}