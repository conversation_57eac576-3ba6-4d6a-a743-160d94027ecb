# Configuration OAuth2 Reddit pour l'Extension

## 📋 Étapes pour configurer l'authentification Reddit

### 1. <PERSON><PERSON><PERSON> une application Reddit

⚠️ **Important** : Pour les extensions Chrome, utilisez l'ancienne interface OAuth2, PAS Devvit !

1. **Allez sur Reddit Apps** : https://www.reddit.com/prefs/apps
   - ❌ **Ne pas utiliser** : https://developers.reddit.com (Devvit - pour apps intégrées)
   - ✅ **Utiliser** : https://www.reddit.com/prefs/apps (OAuth2 - pour extensions)
2. **Connectez-vous** avec votre compte Reddit
3. **Cliquez sur "Create App"** ou "Create Another App"
4. **Remplissez le formulaire** :
   - **Name** : `Front Page Mini Inbox`
   - **App type** : Sélectionnez `installed app`
   - **Description** : `Chrome extension for restoring mini inbox popup`
   - **About URL** : (optionnel) Votre site web ou GitHub
   - **Redirect URI** : `https://YOUR_EXTENSION_ID.chromiumapp.org/`
   - **Permissions** : Co<PERSON>z `read` et `privatemessages`

5. **Cliquez sur "Create app"**

### 2. Récupérer les informations d'authentification

Après création, vous verrez :
- **Client ID** : Une chaîne sous le nom de votre app (ex: `abc123def456`)
- **Client Secret** : Une chaîne plus longue (ex: `xyz789uvw456rst123`)

### 3. Configurer l'extension

1. **Ouvrez** `scripts/background.js`
2. **Remplacez** `YOUR_REDDIT_CLIENT_ID` par votre vrai Client ID
3. **Ajoutez** votre Client Secret dans la configuration

### 4. Obtenir l'ID d'extension Chrome

#### **📍 Étape 4a : Obtenir l'ID d'extension temporaire (mode développeur)**

1. **Allez sur** `chrome://extensions/`
2. **Activez** le "Mode développeur" (coin supérieur droit)
3. **Trouvez** votre extension "Reddit Mini Inbox"
4. **Copiez l'ID d'extension** (chaîne comme `abcdefghijklmnopqrstuvwxyz123456`)

#### **📍 Étape 4b : Configurer l'app Reddit avec l'ID temporaire**

1. **Retournez sur Reddit** → https://www.reddit.com/prefs/apps → Votre app
2. **Cliquez** sur "edit" à côté de votre app
3. **Modifiez** le Redirect URI avec votre ID d'extension temporaire :
   `https://VOTRE_ID_TEMPORAIRE.chromiumapp.org/`
4. **Sauvegardez** les modifications

#### **📍 Étape 4c : Après publication sur Chrome Web Store (futur)**

Quand vous publierez l'extension sur le Chrome Web Store :
1. **L'ID d'extension changera** (ID permanent du store)
2. **Mettez à jour** le Redirect URI avec le nouvel ID
3. **Republiez** une mise à jour si nécessaire

> **💡 Astuce** : L'ID temporaire fonctionne parfaitement pour les tests et le développement !

## 🔒 Sécurité

- **Ne partagez jamais** votre Client Secret publiquement
- **Utilisez** des variables d'environnement pour le développement
- **Considérez** un serveur proxy pour la production

## 🧪 Test

Une fois configuré :
1. **Rechargez** l'extension
2. **Ouvrez** le popup de l'extension
3. **Cliquez** sur "Se connecter à Reddit"
4. **Autorisez** l'application sur Reddit
5. **Vérifiez** que l'authentification fonctionne

## 📝 Notes

- L'authentification Reddit utilise OAuth2
- Les tokens expirent après 1 heure
- L'extension gère automatiquement le rafraîchissement
- Les permissions demandées sont minimales (lecture seule)