// Background script pour l'extension Reddit Mini Inbox
// Service worker pour gérer les requêtes API et le cache

console.log('Reddit Mini Inbox background script loaded');

// Configuration OAuth2 Reddit
const REDDIT_CONFIG = {
  clientId: 'vXHz1sdLF4lOZrTYbWcjBg', // Client ID Reddit
  clientSecret: '', // Vide pour les applications "installed app"
  redirectUri: chrome.identity.getRedirectURL(),
  scope: 'identity read privatemessages',
  responseType: 'code',
  duration: 'permanent',
  userAgent: 'chrome-extension:front-page-mini-inbox:v1.0.0 (by /u/Yougetwhat)' // Votre username Reddit
};

// URLs de l'API Reddit
const REDDIT_API = {
  authorize: 'https://www.reddit.com/api/v1/authorize',
  token: 'https://www.reddit.com/api/v1/access_token',
  inbox: 'https://oauth.reddit.com/message/inbox',
  unread: 'https://oauth.reddit.com/message/unread',
  markRead: 'https://oauth.reddit.com/api/read_message',
  userInfo: 'https://oauth.reddit.com/api/v1/me'
};

// Cache simple des notifications
let notificationsCache = {
  data: [],
  lastFetch: 0,
  expiry: 5 * 60 * 1000, // 5 minutes
  unreadCount: 0
};

// Initialisation de l'extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Reddit Mini Inbox extension installed');
  initializeExtension();
});

// Gestionnaire de messages depuis le content script et popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case 'ping':
      // Health check pour vérifier que le background script répond
      console.log('Ping received, responding with pong');
      sendResponse({ pong: true, timestamp: Date.now() });
      return false; // Réponse synchrone
    
    case 'authenticate':
      handleAuthenticate(request, sendResponse);
      return true;
    
    case 'getNotifications':
      handleGetNotifications(request, sendResponse);
      return true;
    
    case 'markAsRead':
      handleMarkAsRead(request, sendResponse);
      return true;
    
    case 'getUserInfo':
      handleGetUserInfo(request, sendResponse);
      return true;
    
    case 'refreshToken':
      handleRefreshToken(request, sendResponse);
      return true;
    
    default:
      console.warn('Unknown action:', request.action);
      sendResponse({ error: 'Unknown action' });
      return false;
  }
});

// Initialisation simple
async function initializeExtension() {
  try {
    console.log('Extension initialized');
    
    // Mettre à jour le badge
    await updateExtensionBadge(0);
  } catch (error) {
    console.error('Error during initialization:', error);
  }
}

// Gestionnaire d'authentification OAuth2 Reddit
async function handleAuthenticate(request, sendResponse) {
  try {
    console.log('Starting Reddit OAuth2 authentication...');
    
    // Vérifier si les credentials sont configurés
    if (REDDIT_CONFIG.clientId === 'YOUR_REDDIT_CLIENT_ID') {
      sendResponse({ 
        success: false, 
        error: 'Reddit OAuth2 not configured. Please see REDDIT_OAUTH_SETUP.md for setup instructions.' 
      });
      return;
    }
    
    // Construire l'URL d'autorisation Reddit
    const state = generateRandomState();
    const authUrl = buildAuthUrl(state);
    
    console.log('Launching OAuth2 flow...');
    
    // Lancer le flux OAuth2 avec chrome.identity
    chrome.identity.launchWebAuthFlow({
      url: authUrl,
      interactive: true
    }, async (responseUrl) => {
      if (chrome.runtime.lastError) {
        console.error('OAuth2 error:', chrome.runtime.lastError);
        sendResponse({ 
          success: false, 
          error: chrome.runtime.lastError.message 
        });
        return;
      }
      
      if (!responseUrl) {
        sendResponse({ 
          success: false, 
          error: 'Authentication cancelled by user' 
        });
        return;
      }
      
      try {
        // Extraire le code d'autorisation de l'URL de réponse
        const urlParams = new URLSearchParams(new URL(responseUrl).search);
        const code = urlParams.get('code');
        const returnedState = urlParams.get('state');
        
        if (!code) {
          throw new Error('No authorization code received');
        }
        
        if (returnedState !== state) {
          throw new Error('State mismatch - possible CSRF attack');
        }
        
        console.log('Authorization code received, exchanging for tokens...');
        
        // Échanger le code contre un token d'accès
        const tokenData = await exchangeCodeForToken(code);
        
        // Récupérer les informations utilisateur
        const userInfo = await fetchUserInfo(tokenData.access_token);
        
        // Sauvegarder les données d'authentification
        await chrome.storage.local.set({
          authenticated: true,
          accessToken: tokenData.access_token,
          refreshToken: tokenData.refresh_token,
          tokenExpiry: Date.now() + (tokenData.expires_in * 1000),
          username: userInfo.name,
          authTime: Date.now()
        });
        
        console.log('Authentication successful for user:', userInfo.name);
        
        sendResponse({ 
          success: true, 
          username: userInfo.name,
          userInfo: userInfo
        });
        
      } catch (error) {
        console.error('Token exchange error:', error);
        sendResponse({ 
          success: false, 
          error: error.message 
        });
      }
    });
    
  } catch (error) {
    console.error('Authentication error:', error);
    sendResponse({ 
      success: false, 
      error: error.message 
    });
  }
}

// Construire l'URL d'autorisation Reddit
function buildAuthUrl(state) {
  const params = new URLSearchParams({
    client_id: REDDIT_CONFIG.clientId,
    response_type: REDDIT_CONFIG.responseType,
    state: state,
    redirect_uri: REDDIT_CONFIG.redirectUri,
    duration: REDDIT_CONFIG.duration,
    scope: REDDIT_CONFIG.scope
  });
  
  return `${REDDIT_API.authorize}?${params.toString()}`;
}

// Générer un état aléatoire pour la sécurité OAuth2
function generateRandomState() {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

// Échanger le code d'autorisation contre un token d'accès
async function exchangeCodeForToken(code) {
  const response = await fetch(REDDIT_API.token, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${btoa(`${REDDIT_CONFIG.clientId}:${REDDIT_CONFIG.clientSecret}`)}`,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': REDDIT_CONFIG.userAgent
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: REDDIT_CONFIG.redirectUri
    })
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
  }
  
  const tokenData = await response.json();
  
  if (tokenData.error) {
    throw new Error(`Reddit API error: ${tokenData.error}`);
  }
  
  return tokenData;
}

// Récupérer les informations utilisateur depuis l'API Reddit
async function fetchUserInfo(accessToken) {
  const response = await fetch(REDDIT_API.userInfo, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'User-Agent': REDDIT_CONFIG.userAgent
    }
  });
  
  if (!response.ok) {
    throw new Error(`Failed to fetch user info: ${response.status}`);
  }
  
  const userData = await response.json();
  return userData;
}

// Gestionnaire pour récupérer les notifications
async function handleGetNotifications(request, sendResponse) {
  try {
    console.log('Getting notifications...');
    
    // Vérifier le cache d'abord
    if (!request.force && isCacheValid()) {
      console.log('Returning cached notifications');
      sendResponse({ 
        success: true, 
        notifications: notificationsCache.data,
        unreadCount: notificationsCache.unreadCount,
        fromCache: true
      });
      return;
    }
    
    // Vérifier l'authentification
    const authData = await chrome.storage.local.get(['authenticated', 'accessToken', 'tokenExpiry']);
    
    if (!authData.authenticated || !authData.accessToken) {
      console.log('User not authenticated, returning mock notifications');
      const mockNotifications = generateMockNotifications();
      
      // Mettre à jour le cache avec les données factices
      notificationsCache.data = mockNotifications;
      notificationsCache.lastFetch = Date.now();
      notificationsCache.unreadCount = mockNotifications.filter(n => n.new).length;
      
      await updateExtensionBadge(notificationsCache.unreadCount);
      
      sendResponse({ 
        success: true, 
        notifications: mockNotifications,
        unreadCount: notificationsCache.unreadCount,
        fromCache: false,
        isMockData: true
      });
      return;
    }
    
    // Vérifier si le token a expiré
    if (Date.now() >= authData.tokenExpiry) {
      console.log('Access token expired, need to refresh');
      sendResponse({ 
        success: false, 
        error: 'Access token expired. Please re-authenticate.',
        needsAuth: true
      });
      return;
    }
    
    console.log('Fetching real notifications from Reddit API...');
    
    // Récupérer les vraies notifications depuis Reddit
    const notifications = await fetchRedditNotifications(authData.accessToken);
    
    // Mettre à jour le cache
    notificationsCache.data = notifications;
    notificationsCache.lastFetch = Date.now();
    notificationsCache.unreadCount = notifications.filter(n => n.new).length;
    
    // Mettre à jour le badge
    await updateExtensionBadge(notificationsCache.unreadCount);
    
    console.log(`Fetched ${notifications.length} notifications (${notificationsCache.unreadCount} unread)`);
    
    sendResponse({ 
      success: true, 
      notifications: notifications,
      unreadCount: notificationsCache.unreadCount,
      fromCache: false,
      isMockData: false
    });
    
  } catch (error) {
    console.error('Error getting notifications:', error);
    
    // En cas d'erreur, retourner les données en cache si disponibles
    if (notificationsCache.data.length > 0) {
      console.log('Returning cached data due to error');
      sendResponse({ 
        success: true, 
        notifications: notificationsCache.data,
        unreadCount: notificationsCache.unreadCount,
        fromCache: true,
        error: error.message
      });
    } else {
      sendResponse({ 
        success: false, 
        error: error.message 
      });
    }
  }
}

// Récupérer les notifications depuis l'API Reddit
async function fetchRedditNotifications(accessToken) {
  const response = await fetch(`${REDDIT_API.inbox}.json?limit=25`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'User-Agent': REDDIT_CONFIG.userAgent
    }
  });
  
  if (!response.ok) {
    if (response.status === 401) {
      throw new Error('Access token expired or invalid');
    }
    throw new Error(`Reddit API error: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  
  if (data.error) {
    throw new Error(`Reddit API error: ${data.error}`);
  }
  
  // Transformer les données Reddit en format utilisable
  const notifications = data.data.children.map(child => {
    const message = child.data;
    
    return {
      id: message.id,
      type: determineMessageType(message),
      author: message.author,
      subject: message.subject,
      body: message.body,
      created_utc: message.created_utc,
      new: message.new,
      permalink: message.context || message.permalink,
      subreddit: message.subreddit,
      context: message.context,
      link_title: message.link_title,
      parent_id: message.parent_id,
      was_comment: message.was_comment
    };
  });
  
  return notifications;
}

// Déterminer le type de message Reddit
function determineMessageType(message) {
  if (message.was_comment) {
    if (message.type === 'username_mention') {
      return 'username_mention';
    }
    return 'comment_reply';
  } else if (message.subreddit) {
    return 'post_reply';
  } else {
    return 'private_message';
  }
}

// Générer des notifications factices pour les tests
function generateMockNotifications() {
  const mockData = [
    {
      id: 'mock1',
      type: 'comment_reply',
      author: 'test_user',
      subject: 'Reply to your comment',
      body: 'Great post! Thanks for sharing this information.',
      created_utc: Math.floor(Date.now() / 1000) - 3600, // 1 heure ago
      new: true,
      permalink: '/r/programming/comments/abc/test/def/',
      subreddit: 'programming',
      context: '/r/programming/comments/abc/test/def/?context=3'
    },
    {
      id: 'mock2',
      type: 'username_mention',
      author: 'another_user',
      subject: 'Username mention',
      body: 'Hey u/testuser, you might find this interesting!',
      created_utc: Math.floor(Date.now() / 1000) - 7200, // 2 heures ago
      new: true,
      permalink: '/r/javascript/comments/xyz/question/ghi/',
      subreddit: 'javascript'
    },
    {
      id: 'mock3',
      type: 'private_message',
      author: 'friend_user',
      subject: 'Quick question',
      body: 'Hi! I saw your post about React and had a quick question...',
      created_utc: Math.floor(Date.now() / 1000) - 10800, // 3 heures ago
      new: false,
      permalink: '/message/messages/mock3'
    },
    {
      id: 'mock4',
      type: 'comment_reply',
      author: 'helpful_user',
      subject: 'Re: Your question about APIs',
      body: 'I think you should try using fetch() instead of XMLHttpRequest for modern browsers.',
      created_utc: Math.floor(Date.now() / 1000) - 14400, // 4 heures ago
      new: false,
      permalink: '/r/webdev/comments/def/api/jkl/',
      subreddit: 'webdev',
      context: '/r/webdev/comments/def/api/jkl/?context=3'
    }
  ];
  
  return mockData;
}

// Gestionnaire pour marquer comme lu
async function handleMarkAsRead(request, sendResponse) {
  try {
    console.log('Marking notification as read:', request.notificationId);
    
    // Vérifier l'authentification
    const authData = await chrome.storage.local.get(['authenticated', 'accessToken', 'tokenExpiry']);
    
    if (!authData.authenticated || !authData.accessToken) {
      // Si pas authentifié, juste marquer localement
      const notification = notificationsCache.data.find(n => n.id === request.notificationId);
      if (notification) {
        notification.new = false;
        notificationsCache.unreadCount = Math.max(0, notificationsCache.unreadCount - 1);
        await updateExtensionBadge(notificationsCache.unreadCount);
      }
      
      sendResponse({ success: true, local: true });
      return;
    }
    
    // Vérifier si le token a expiré
    if (Date.now() >= authData.tokenExpiry) {
      sendResponse({ 
        success: false, 
        error: 'Access token expired. Please re-authenticate.',
        needsAuth: true
      });
      return;
    }
    
    // Marquer comme lu via l'API Reddit
    await markNotificationAsReadOnReddit(authData.accessToken, request.notificationId);
    
    // Mettre à jour le cache local
    const notification = notificationsCache.data.find(n => n.id === request.notificationId);
    if (notification) {
      notification.new = false;
      notificationsCache.unreadCount = Math.max(0, notificationsCache.unreadCount - 1);
      await updateExtensionBadge(notificationsCache.unreadCount);
    }
    
    sendResponse({ success: true, local: false });
  } catch (error) {
    console.error('Error marking as read:', error);
    
    // En cas d'erreur API, marquer quand même localement
    const notification = notificationsCache.data.find(n => n.id === request.notificationId);
    if (notification) {
      notification.new = false;
      notificationsCache.unreadCount = Math.max(0, notificationsCache.unreadCount - 1);
      await updateExtensionBadge(notificationsCache.unreadCount);
    }
    
    sendResponse({ 
      success: true, 
      local: true,
      error: error.message 
    });
  }
}

// Marquer une notification comme lue sur Reddit
async function markNotificationAsReadOnReddit(accessToken, messageId) {
  const response = await fetch(REDDIT_API.markRead, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': REDDIT_CONFIG.userAgent
    },
    body: new URLSearchParams({
      id: `t4_${messageId}` // Reddit utilise le préfixe t4_ pour les messages
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to mark as read: ${response.status} ${response.statusText}`);
  }
  
  const result = await response.json();
  
  if (result.error) {
    throw new Error(`Reddit API error: ${result.error}`);
  }
  
  return result;
}

// Gestionnaire pour récupérer les informations utilisateur
async function handleGetUserInfo(request, sendResponse) {
  try {
    console.log('Getting user info...');
    
    // Vérifier l'authentification
    const authData = await chrome.storage.local.get(['authenticated', 'accessToken', 'tokenExpiry']);
    
    if (!authData.authenticated || !authData.accessToken) {
      // Pour les tests, simuler des infos utilisateur factices
      const mockUserInfo = {
        name: 'test_user',
        id: 'mock_user_id',
        created_utc: Math.floor(Date.now() / 1000) - (365 * 24 * 3600) // 1 an ago
      };
      
      sendResponse({ 
        success: true, 
        userInfo: mockUserInfo,
        isMockData: true
      });
      return;
    }
    
    // Vérifier si le token a expiré
    if (Date.now() >= authData.tokenExpiry) {
      sendResponse({ 
        success: false, 
        error: 'Access token expired. Please re-authenticate.',
        needsAuth: true
      });
      return;
    }
    
    // Récupérer les vraies informations utilisateur
    const userInfo = await fetchUserInfo(authData.accessToken);
    
    sendResponse({ 
      success: true, 
      userInfo: userInfo,
      isMockData: false
    });
  } catch (error) {
    console.error('Error getting user info:', error);
    sendResponse({ 
      success: false, 
      error: error.message 
    });
  }
}

// Gestionnaire pour rafraîchir le token d'accès
async function handleRefreshToken(request, sendResponse) {
  try {
    console.log('Refreshing access token...');
    
    // Récupérer le refresh token
    const authData = await chrome.storage.local.get(['refreshToken']);
    
    if (!authData.refreshToken) {
      sendResponse({ 
        success: false, 
        error: 'No refresh token available. Please re-authenticate.',
        needsAuth: true
      });
      return;
    }
    
    // Rafraîchir le token via l'API Reddit
    const tokenData = await refreshAccessToken(authData.refreshToken);
    
    // Mettre à jour les données d'authentification
    await chrome.storage.local.set({
      accessToken: tokenData.access_token,
      tokenExpiry: Date.now() + (tokenData.expires_in * 1000),
      authTime: Date.now()
    });
    
    console.log('Token refreshed successfully');
    
    sendResponse({ success: true });
  } catch (error) {
    console.error('Error refreshing token:', error);
    
    // Si le refresh token est invalide, demander une nouvelle authentification
    if (error.message.includes('invalid_grant') || error.message.includes('401')) {
      await chrome.storage.local.remove([
        'authenticated',
        'accessToken',
        'refreshToken',
        'tokenExpiry'
      ]);
      
      sendResponse({ 
        success: false, 
        error: 'Refresh token expired. Please re-authenticate.',
        needsAuth: true
      });
    } else {
      sendResponse({ 
        success: false, 
        error: error.message 
      });
    }
  }
}

// Rafraîchir le token d'accès avec le refresh token
async function refreshAccessToken(refreshToken) {
  const response = await fetch(REDDIT_API.token, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${btoa(`${REDDIT_CONFIG.clientId}:${REDDIT_CONFIG.clientSecret}`)}`,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': REDDIT_CONFIG.userAgent
    },
    body: new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken
    })
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Token refresh failed: ${response.status} ${errorText}`);
  }
  
  const tokenData = await response.json();
  
  if (tokenData.error) {
    throw new Error(`Reddit API error: ${tokenData.error}`);
  }
  
  return tokenData;
}

// Fonctions utilitaires
function isCacheValid() {
  return notificationsCache.lastFetch > 0 && 
         (Date.now() - notificationsCache.lastFetch) < notificationsCache.expiry;
}

// Mettre à jour le badge de l'extension
async function updateExtensionBadge(unreadCount) {
  try {
    const badgeText = unreadCount > 0 ? unreadCount.toString() : '';
    const badgeColor = unreadCount > 0 ? '#FF4500' : '#808080';
    
    await chrome.action.setBadgeText({ text: badgeText });
    await chrome.action.setBadgeBackgroundColor({ color: badgeColor });
    
    console.log(`Badge updated: ${badgeText} (${unreadCount} unread)`);
  } catch (error) {
    console.error('Error updating extension badge:', error);
  }
}

// Fonction utilitaire pour attendre
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

console.log('Background script setup complete');