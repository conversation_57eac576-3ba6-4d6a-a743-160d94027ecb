// Reddit Mini Inbox Extension - Content Script
// Version: 2.0.0 - Complete rewrite
console.log('🚀 Reddit Mini Inbox v2.0.0 loaded - DEBUG MODE');
console.log('🔍 DEBUG: URL actuelle:', window.location.href);
console.log('🔍 DEBUG: User agent:', navigator.userAgent);

// Global state
let miniInboxOpen = false;
let miniInboxElement = null;

// Notification selectors for different Reddit versions
const REDDIT_SELECTORS = {
  old: {
    icons: ['#mail', '.mail', 'a[href*="/message/inbox"]'],
    containers: ['#header-bottom-right', '.user']
  },
  new: {
    icons: ['[aria-label*="notification"]', '[aria-label*="inbox"]', 'a[href*="/message/inbox"]'],
    containers: ['[data-testid="header"]', 'header']
  },
  'new-react': {
    icons: ['button[aria-label*="notification"]', '[data-testid="notification-bell"]', 'a[href*="/message/inbox"]'],
    containers: ['shreddit-app', 'reddit-header-large']
  }
};

// Initialize extension
function init() {
  console.log('Initializing Reddit Mini Inbox...');
  
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setup);
  } else {
    setup();
  }
}

// Setup notification interception
function setup() {
  console.log('Setting up notification interception...');

  // Add click listener
  document.addEventListener('click', handleClick, true);
  document.addEventListener('keydown', handleKeyPress);

  console.log('✅ Reddit Mini Inbox setup complete');
}



// Handle clicks
function handleClick(event) {
  const target = event.target;

  // Debug clics seulement si nécessaire
  if (target.closest('.mini-inbox-container') || target.getAttribute('aria-label')?.includes('notification')) {
    console.log('🖱️ DEBUG: Relevant click detected', {
      target: target,
      tagName: target.tagName,
      className: target.className,
      ariaLabel: target.getAttribute('aria-label'),
      isInsideMiniInbox: !!target.closest('.mini-inbox-container')
    });
  }

  // Debug all clicks when mini inbox is open
  if (miniInboxOpen) {
    console.log('🖱️ DEBUG: Global click detected', {
      target: target,
      tagName: target.tagName,
      className: target.className,
      textContent: target.textContent?.substring(0, 50),
      closest_mini_inbox: !!target.closest('.mini-inbox-container'),
      closest_notification_action: !!target.closest('.notification-action'),
      closest_refresh_button: !!target.closest('.refresh-button'),
      closest_close_btn: !!target.closest('.close-btn')
    });
  }

  // IMPORTANT: Ne pas intercepter les clics à l'intérieur de la mini-inbox !
  if (target.closest('.mini-inbox-container')) {
    console.log('🔍 DEBUG: Click inside mini-inbox, ignoring...');
    return; // Laisser l'événement se propager normalement
  }

  // Check if click is on notification icon
  if (isNotificationIcon(target)) {
    console.log('🎯 Notification icon clicked, intercepting...');
    event.preventDefault();
    event.stopPropagation();

    const iconPosition = getIconPosition(target);
    toggleMiniInbox(iconPosition);
    return false;
  }

  // FALLBACK CONSERVATEUR : Seulement les liens inbox directs
  const href = target.getAttribute('href') || target.closest('a')?.getAttribute('href');
  if (href && href === '/message/inbox') {
    console.log('🔧 FALLBACK: Intercepting direct inbox link...', href);
    event.preventDefault();
    event.stopPropagation();

    const iconPosition = getIconPosition(target);
    toggleMiniInbox(iconPosition);
    return false;
  }

  // Check if click is outside mini inbox
  if (miniInboxOpen && !target.closest('.mini-inbox-container')) {
    console.log('🌍 DEBUG: Click outside mini inbox, closing');
    closeMiniInbox();
  }
}

// Handle keyboard
function handleKeyPress(event) {
  if (event.key === 'Escape' && miniInboxOpen) {
    closeMiniInbox();
  }
}

// Toggle mini inbox
function toggleMiniInbox(iconPosition = null) {
  console.log('🔄 DEBUG: toggleMiniInbox called', { miniInboxOpen, iconPosition });
  if (miniInboxOpen) {
    console.log('📤 DEBUG: Closing mini inbox');
    closeMiniInbox();
  } else {
    console.log('📥 DEBUG: Opening mini inbox');
    openMiniInbox(iconPosition);
  }
}

// Open mini inbox
async function openMiniInbox(iconPosition = null) {
  console.log('🚀 DEBUG: openMiniInbox START', { iconPosition });
  console.log('🔍 DEBUG: Current state', { miniInboxElement, miniInboxOpen });

  if (miniInboxElement) {
    console.log('🗑️ DEBUG: Removing existing miniInboxElement');
    miniInboxElement.remove();
  }

  console.log('🏗️ DEBUG: Creating mini inbox element');
  createMiniInboxElement(iconPosition);
  console.log('⏳ DEBUG: Setting loading state');
  setLoadingState('Loading notifications...');
  
  try {
    console.log('🔐 DEBUG: Checking authentication');
    // First check if user is authenticated
    const authData = await chrome.storage.local.get(['authenticated']);
    console.log('🔐 DEBUG: Auth data', authData);

    console.log('📡 DEBUG: Sending message to background script');
    console.log('📡 DEBUG: chrome.runtime available?', !!chrome.runtime);
    console.log('📡 DEBUG: chrome.runtime.sendMessage available?', !!chrome.runtime.sendMessage);

    const response = await chrome.runtime.sendMessage({
      action: 'getNotifications',
      force: false
    });

    console.log('📡 DEBUG: Response received', response);
    
    if (response && response.success) {
      console.log('✅ DEBUG: Response successful', {
        notificationCount: response.notifications?.length,
        isMockData: response.isMockData
      });

      if (response.notifications && response.notifications.length > 0) {
        console.log('📋 DEBUG: Displaying notifications', response.notifications);
        displayNotifications(response.notifications);
        if (response.isMockData) {
          console.log('🎭 DEBUG: Showing mock data indicator');
          showMockDataIndicator();
        }
      } else {
        console.log('📭 DEBUG: No notifications, showing empty state');
        displayNoNotifications();
      }
    } else {
      console.log('❌ DEBUG: Response failed', response);
      if (response && response.needsAuth) {
        console.log('🔐 DEBUG: Auth required, showing auth screen');
        displayAuthRequired();
      } else {
        console.log('💥 DEBUG: Error occurred, showing error');
        displayError(response?.error || 'Unknown error');
      }
    }
  } catch (error) {
    console.error('💥 DEBUG: Error getting notifications:', error);
    console.error('💥 DEBUG: Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    // FALLBACK: Use hardcoded test data if chrome.runtime fails
    console.log('🔄 DEBUG: Attempting fallback with test data');
    try {
      const testNotifications = getTestNotifications();
      console.log('🧪 DEBUG: Using test notifications', testNotifications);
      displayNotifications(testNotifications);
      showMockDataIndicator();
    } catch (fallbackError) {
      console.error('💥 DEBUG: Fallback also failed:', fallbackError);
      displayError('Error loading notifications - both main and fallback failed');
    }
  }
  
  console.log('✅ DEBUG: openMiniInbox completed', { miniInboxOpen: true });
  miniInboxOpen = true;
}

// Test notifications for fallback
function getTestNotifications() {
  console.log('🧪 DEBUG: Generating test notifications');
  return [
    {
      id: 'test1',
      type: 'comment_reply',
      author: 'test_user',
      subject: 'Test Reply',
      body: 'This is a test notification to verify button functionality.',
      created_utc: Math.floor(Date.now() / 1000) - 3600,
      new: true,
      permalink: '/r/test/comments/abc/test/',
      subreddit: 'test',
      context: '/r/test/comments/abc/test/?context=3'
    },
    {
      id: 'test2',
      type: 'private_message',
      author: 'another_user',
      subject: 'Test Message',
      body: 'Another test notification with different type.',
      created_utc: Math.floor(Date.now() / 1000) - 7200,
      new: false,
      permalink: '/message/messages/test2'
    }
  ];
}

// Close mini inbox
function closeMiniInbox() {
  console.log('Closing mini inbox...');
  
  if (miniInboxElement) {
    miniInboxElement.classList.remove('show');
    const container = miniInboxElement.querySelector('.mini-inbox-container');
    if (container) {
      container.classList.add('closing');
    }
    
    setTimeout(() => {
      if (miniInboxElement && miniInboxElement.parentNode) {
        miniInboxElement.remove();
      }
      miniInboxElement = null;
    }, 150);
  }
  
  miniInboxOpen = false;
}

// Create mini inbox element
function createMiniInboxElement(iconPosition = null) {
  miniInboxElement = document.createElement('div');
  miniInboxElement.id = 'reddit-mini-inbox-overlay';
  
  const isDarkTheme = detectDarkTheme();
  const themeClass = isDarkTheme ? 'dark-theme' : '';
  
  miniInboxElement.innerHTML = `
    <div class="mini-inbox-container opening ${themeClass}">
      <div class="mini-inbox-header">
        <div class="mini-inbox-title">
          <span class="mini-inbox-icon">[INBOX]</span>
          <span>inbox</span>
        </div>
        <div class="mini-inbox-actions">
          <button class="refresh-button" type="button" aria-label="Refresh" title="Refresh">↻</button>
          <button class="close-btn" type="button" aria-label="Close" title="Close">×</button>
        </div>
      </div>
      <div class="mini-inbox-stats" style="display: none;">
        <span class="unread-count">0 unread</span>
        <span class="total-count">0 total</span>
      </div>
      <div class="mini-inbox-content loading-state">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading notifications...</div>
        </div>
      </div>
    </div>
  `;
  
  if (iconPosition) {
    positionMiniInbox(miniInboxElement, iconPosition);
  }
  
  // Add event listeners
  const closeBtn = miniInboxElement.querySelector('.close-btn');
  console.log('❌ DEBUG: Close button found:', !!closeBtn);
  if (closeBtn) {
    // Visual indicator for debugging
    closeBtn.style.border = '2px solid green';
    closeBtn.style.backgroundColor = 'rgba(0, 255, 0, 0.1)';
    closeBtn.title = 'DEBUG: Close button - Click to test';

    closeBtn.addEventListener('click', (event) => {
      console.log('❌ DEBUG: ===== CLOSE BUTTON CLICKED =====');
      console.log('❌ DEBUG: Event:', event);
      event.preventDefault();
      event.stopPropagation();
      console.log('❌ DEBUG: Calling closeMiniInbox()');
      closeMiniInbox();
    });
    console.log('✅ DEBUG: Close button event listener attached');
  } else {
    console.error('❌ DEBUG: Close button not found!');
  }

  // Add refresh button event listener
  const refreshBtn = miniInboxElement.querySelector('.refresh-button');
  console.log('🔄 DEBUG: Refresh button found:', !!refreshBtn);
  if (refreshBtn) {
    // Visual indicator for debugging
    refreshBtn.style.border = '2px solid blue';
    refreshBtn.style.backgroundColor = 'rgba(0, 0, 255, 0.1)';
    refreshBtn.title = 'DEBUG: Refresh button - Click to test';

    refreshBtn.addEventListener('click', (event) => {
      console.log('🔄 DEBUG: ===== REFRESH BUTTON CLICKED =====');
      console.log('🔄 DEBUG: Event:', event);
      event.preventDefault();
      event.stopPropagation();
      console.log('🔄 DEBUG: Calling refreshNotifications()');
      refreshNotifications();
    });
    console.log('✅ DEBUG: Refresh button event listener attached');
  } else {
    console.error('❌ DEBUG: Refresh button not found!');
  }
  
  miniInboxElement.addEventListener('click', (event) => {
    if (event.target === miniInboxElement) {
      closeMiniInbox();
    }
  });
  
  miniInboxElement.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      event.preventDefault();
      closeMiniInbox();
    }
  });
  
  document.body.appendChild(miniInboxElement);
  
  setTimeout(() => {
    miniInboxElement.classList.add('show');
    const container = miniInboxElement.querySelector('.mini-inbox-container');
    // Mettre à jour le thème avant d'afficher
    updateMiniInboxTheme();
    container.classList.add('show');
  }, 10);
}

// Display notifications
function displayNotifications(notifications) {
  console.log('📋 DEBUG: displayNotifications START', {
    notificationCount: notifications?.length,
    miniInboxElement: !!miniInboxElement
  });

  if (!miniInboxElement) {
    console.error('❌ DEBUG: miniInboxElement is null!');
    return;
  }

  console.log('✅ DEBUG: Setting loading state to success');
  setLoadingState('success');

  const content = miniInboxElement.querySelector('.mini-inbox-content');
  console.log('🔍 DEBUG: Content element found', !!content);
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  
  const unreadCount = notifications.filter(n => n.new).length;
  const totalCount = notifications.length;
  
  if (statsElement) {
    statsElement.style.display = 'flex';
    statsElement.querySelector('.unread-count').textContent = `${unreadCount} unread`;
    statsElement.querySelector('.total-count').textContent = `${totalCount} total`;
  }
  
  if (notifications.length === 0) {
    console.log('📭 DEBUG: No notifications to display');
    displayNoNotifications();
    return;
  }

  console.log('🏗️ DEBUG: Building notifications HTML for', notifications.length, 'notifications');
  const notificationsHtml = notifications.map((notification, index) => {
    console.log(`🔍 DEBUG: Processing notification ${index}:`, {
      id: notification.id,
      type: notification.type,
      author: notification.author,
      subject: notification.subject
    });
    const typeClass = notification.type ? notification.type.replace('_', '-') : 'message';
    const contextText = getNotificationContext(notification);
    const actionsHtml = getNotificationActions(notification);
    
    return `
      <div class="notification-item ${notification.new ? 'unread' : 'read'} ${typeClass}" 
           data-id="${notification.id}" 
           data-type="${notification.type || 'message'}"
           tabindex="0"
           role="button"
           aria-label="Notification from ${escapeHtml(notification.author)}">
        <div class="notification-header">
          <a href="/user/${escapeHtml(notification.author)}" class="notification-author" target="_blank">
            ${escapeHtml(notification.author)}
          </a>
          <span class="notification-time" title="${formatFullTime(notification.created_utc)}">
            ${formatTime(notification.created_utc)}
          </span>
        </div>
        <div class="notification-subject">${escapeHtml(notification.subject)}</div>
        ${contextText ? `<div class="notification-context">${contextText}</div>` : ''}
        <div class="notification-body">${formatNotificationBody(notification.body)}</div>
        <div class="notification-actions">
          ${actionsHtml}
        </div>
      </div>
    `;
  }).join('');
  
  console.log('📝 DEBUG: Setting content innerHTML');
  content.innerHTML = notificationsHtml;
  console.log('✅ DEBUG: HTML inserted, content.innerHTML length:', content.innerHTML.length);

  // CRITICAL: Attach event listeners immediately after HTML insertion
  console.log('🔗 DEBUG: About to attach event listeners');
  attachEventListeners(content);
  console.log('✅ DEBUG: displayNotifications COMPLETED');
}

// Attach event listeners - THIS IS THE KEY FUNCTION
function attachEventListeners(content) {
  console.log('🔧 DEBUG: attachEventListeners START');
  console.log('🔍 DEBUG: Content element:', content);
  console.log('🔍 DEBUG: Content innerHTML preview:', content.innerHTML.substring(0, 200) + '...');

  // Use setTimeout to ensure DOM is fully rendered
  setTimeout(() => {
    console.log('⏰ DEBUG: Timeout executed, DOM should be ready');
    attachEventListenersImmediate(content);
  }, 50);
}

function attachEventListenersImmediate(content) {
  console.log('🔧 DEBUG: attachEventListenersImmediate START');
  console.log('🔍 DEBUG: Content element:', content);
  
  // Attach to notification items
  const notificationItems = content.querySelectorAll('.notification-item');
  console.log(`🔍 DEBUG: Found ${notificationItems.length} notification items`);
  
  notificationItems.forEach((item, index) => {
    console.log(`🔍 DEBUG: Attaching to notification item ${index}:`, item);
    
    item.addEventListener('click', (event) => {
      console.log('🖱️ DEBUG: Notification item clicked');
      if (!event.target.closest('.notification-actions') && 
          !event.target.closest('.quick-reply-interface')) {
        handleNotificationClick(event);
      }
    });
    
    setTimeout(() => item.classList.add('new-notification'), index * 50);
  });
  
  // Attach to action buttons - MOST IMPORTANT
  const actionButtons = content.querySelectorAll('.notification-action');
  console.log(`🔍 DEBUG: Found ${actionButtons.length} action buttons`);
  console.log('🔍 DEBUG: Action buttons details:', Array.from(actionButtons).map(btn => ({
    textContent: btn.textContent,
    dataAction: btn.getAttribute('data-action'),
    dataId: btn.getAttribute('data-id'),
    className: btn.className,
    tagName: btn.tagName
  })));
  
  actionButtons.forEach((action, index) => {
    console.log(`🔍 DEBUG: Attaching to action ${index}:`, {
      element: action,
      textContent: action.textContent,
      dataAction: action.getAttribute('data-action'),
      dataId: action.getAttribute('data-id'),
      tagName: action.tagName,
      className: action.className
    });
    
    action.addEventListener('click', function(event) {
      console.log('🎯 DEBUG: ===== ACTION BUTTON CLICKED =====');
      console.log('🎯 DEBUG: Button element:', this);
      console.log('🎯 DEBUG: Event:', event);

      const actionType = this.getAttribute('data-action');
      const notificationId = this.getAttribute('data-id');
      const notificationItem = this.closest('.notification-item');
      const href = this.getAttribute('href');

      console.log('🎯 DEBUG: Action details:', {
        actionType,
        notificationId,
        notificationItem: !!notificationItem,
        element: this,
        textContent: this.textContent,
        href: href
      });

      // GESTION SPÉCIALE : Liens externes (comme "context")
      if (!actionType && href) {
        const textContent = this.textContent.trim().toLowerCase();
        console.log('🔗 DEBUG: External link detected:', {
          href: href,
          textContent: textContent,
          hasTargetBlank: this.getAttribute('target') === '_blank'
        });

        // Si c'est un lien externe (context, permalink, etc.)
        if (textContent.includes('context') ||
            href.startsWith('http') ||
            href.includes('/r/') ||
            href.includes('/comments/') ||
            this.getAttribute('target') === '_blank') {
          console.log('🔗 DEBUG: Allowing external link to open normally');
          // Ne pas empêcher l'événement pour les liens externes
          return; // Laisser le navigateur gérer le lien normalement
        }
      }

      // Pour les autres actions, empêcher l'événement par défaut
      event.preventDefault();
      event.stopPropagation();
      console.log('🎯 DEBUG: Event prevented and stopped');

      if (!actionType) {
        console.error('❌ DEBUG: No actionType found!', {
          element: this,
          textContent: this.textContent,
          href: href,
          className: this.className,
          outerHTML: this.outerHTML.substring(0, 200)
        });
        return;
      }

      if (!notificationId) {
        console.error('❌ DEBUG: No notificationId found!');
        return;
      }

      console.log('🚀 DEBUG: Calling handleNotificationAction');
      handleNotificationAction(actionType, notificationId, notificationItem, this);
    });
    
    // Visual indicators for debugging
    action.style.border = '2px solid red';
    action.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
    action.title = `DEBUG: Action ${index} - ${action.getAttribute('data-action')} - Click to test`;
    console.log(`🔍 DEBUG: Added visual indicators to action ${index} for testing`);
    console.log(`🔍 DEBUG: Action ${index} ready for manual testing`);
  });
  
  // Attach to user links
  const userLinks = content.querySelectorAll('.notification-author');
  console.log(`🔍 DEBUG: Found ${userLinks.length} user links`);
  
  userLinks.forEach((link, index) => {
    link.addEventListener('click', (event) => {
      event.stopPropagation();
      console.log('👤 DEBUG: User link clicked:', link.textContent);
    });
  });
  
  console.log('✅ DEBUG: Event listeners attached successfully!');
  console.log('🔍 DEBUG: Summary:', {
    notificationItems: notificationItems.length,
    actionButtons: actionButtons.length,
    userLinks: userLinks.length
  });
}

// Handle notification actions
function handleNotificationAction(actionType, notificationId, notificationItem, actionElement) {
  console.log('🎬 DEBUG: ===== HANDLE NOTIFICATION ACTION =====');
  console.log('🎬 DEBUG: Action type:', actionType);
  console.log('🎬 DEBUG: Notification ID:', notificationId);
  console.log('🎬 DEBUG: Notification item:', !!notificationItem);
  console.log('🎬 DEBUG: Action element:', !!actionElement);

  switch (actionType) {
    case 'reply':
      console.log('💬 DEBUG: Handling reply action');
      handleReplyAction(notificationId, notificationItem);
      break;

    case 'mark-read':
      console.log('✅ DEBUG: Handling mark-read action');
      handleMarkAsReadAction(notificationId, notificationItem, actionElement);
      break;

    case 'mark-unread':
      console.log('👁️ DEBUG: Handling mark-unread action');
      handleMarkAsUnreadAction(notificationId, notificationItem, actionElement);
      break;

    default:
      console.warn('❌ DEBUG: Unknown action:', actionType);
      console.warn('❌ DEBUG: Available actions: reply, mark-read, mark-unread');
  }

  console.log('✅ DEBUG: handleNotificationAction completed');
}

// Handle reply action
function handleReplyAction(notificationId, notificationItem) {
  console.log('💬 DEBUG: ===== HANDLE REPLY ACTION =====');
  console.log('💬 DEBUG: Notification ID:', notificationId);
  console.log('💬 DEBUG: Notification item:', !!notificationItem);

  const existingReply = notificationItem.querySelector('.quick-reply-interface');
  if (existingReply) {
    console.log('💬 DEBUG: Existing reply interface found, removing it');
    existingReply.remove();
    return;
  }

  console.log('💬 DEBUG: Creating quick reply interface');
  const replyInterface = createQuickReplyInterface(notificationId, notificationItem);
  console.log('💬 DEBUG: Reply interface created:', !!replyInterface);

  const notificationBody = notificationItem.querySelector('.notification-body');
  console.log('💬 DEBUG: Notification body found:', !!notificationBody);

  if (notificationBody && replyInterface) {
    notificationBody.insertAdjacentElement('afterend', replyInterface);
    console.log('💬 DEBUG: Reply interface inserted into DOM');

    const textarea = replyInterface.querySelector('.reply-textarea');
    if (textarea) {
      textarea.focus();
      console.log('💬 DEBUG: Textarea focused');
    } else {
      console.error('❌ DEBUG: Textarea not found in reply interface');
    }

    // Mark as read since user is interacting
    console.log('💬 DEBUG: Marking notification as read');
    handleMarkAsReadAction(notificationId, notificationItem);
  } else {
    console.error('❌ DEBUG: Could not insert reply interface - missing elements');
  }

  console.log('✅ DEBUG: handleReplyAction completed');
}

// Create quick reply interface
function createQuickReplyInterface(notificationId, notificationItem) {
  const replyDiv = document.createElement('div');
  replyDiv.className = 'quick-reply-interface';
  replyDiv.style.cssText = `
    background: #f8f9fa; border: 1px solid #e5e5e5; border-radius: 4px;
    margin: 8px 0; padding: 0; animation: slideDown 0.2s ease-out;
  `;
  
  replyDiv.innerHTML = `
    <div style="background: #ff4500; color: white; padding: 6px 10px; display: flex; justify-content: space-between; border-radius: 4px 4px 0 0;">
      <span style="font-weight: bold; font-size: 11px;">Quick Reply</span>
      <button class="reply-close" style="background: none; border: none; color: white; cursor: pointer; font-size: 16px;">×</button>
    </div>
    <div style="padding: 10px;">
      <textarea class="reply-textarea" placeholder="Write your reply..." rows="3" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-size: 11px; box-sizing: border-box; margin-bottom: 8px; resize: vertical;"></textarea>
      <div style="display: flex; gap: 6px; flex-wrap: wrap;">
        <button class="reply-send" style="background: #ff4500; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer; font-weight: bold;">Send Reply</button>
        <button class="reply-cancel" style="background: #f5f5f5; color: #666; border: 1px solid #ddd; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Cancel</button>
        <button class="reply-full" style="background: #0079d3; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Open Full Editor</button>
      </div>
    </div>
  `;
  
  // Add event listeners
  const closeBtn = replyDiv.querySelector('.reply-close');
  const cancelBtn = replyDiv.querySelector('.reply-cancel');
  const sendBtn = replyDiv.querySelector('.reply-send');
  const fullBtn = replyDiv.querySelector('.reply-full');
  const textarea = replyDiv.querySelector('.reply-textarea');
  
  const closeReply = () => replyDiv.remove();
  closeBtn.addEventListener('click', closeReply);
  cancelBtn.addEventListener('click', closeReply);
  
  sendBtn.addEventListener('click', () => {
    const text = textarea.value.trim();
    if (text) {
      sendQuickReply(notificationId, text, replyDiv);
    } else {
      textarea.focus();
      textarea.style.borderColor = '#ff4500';
      setTimeout(() => textarea.style.borderColor = '', 2000);
    }
  });
  
  fullBtn.addEventListener('click', () => {
    const text = textarea.value.trim();
    let url = '/message/compose';
    if (text) {
      url += '?message=' + encodeURIComponent(text);
    }
    
    const tempLink = document.createElement('a');
    tempLink.href = url;
    tempLink.target = '_blank';
    tempLink.rel = 'noopener noreferrer';
    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
    
    closeReply();
  });
  
  textarea.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      sendBtn.click();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      closeReply();
    }
  });
  
  return replyDiv;
}

// Send quick reply
async function sendQuickReply(notificationId, replyText, replyInterface) {
  const sendBtn = replyInterface.querySelector('.reply-send');
  const textarea = replyInterface.querySelector('.reply-textarea');
  
  sendBtn.disabled = true;
  sendBtn.textContent = 'Sending...';
  textarea.disabled = true;
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    replyInterface.innerHTML = `
      <div style="padding: 15px; text-align: center; background: #f0f8ff; border-radius: 4px;">
        <div style="font-size: 24px; margin-bottom: 8px;">✅</div>
        <div style="color: #0079d3; font-weight: bold; margin-bottom: 10px;">Reply sent successfully!</div>
        <button onclick="this.closest('.quick-reply-interface').remove()" style="background: #0079d3; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Close</button>
      </div>
    `;
    
    setTimeout(() => {
      if (replyInterface.parentNode) {
        replyInterface.remove();
      }
    }, 3000);
    
  } catch (error) {
    console.error('Error sending reply:', error);
    sendBtn.disabled = false;
    sendBtn.textContent = 'Send Reply';
    textarea.disabled = false;
    textarea.focus();
  }
}

// Handle mark as read
function handleMarkAsReadAction(notificationId, notificationItem, actionElement = null) {
  console.log('✅ Mark as read:', notificationId);
  
  chrome.runtime.sendMessage({
    action: 'markAsRead',
    notificationId: notificationId
  }).then(response => {
    if (response && response.success) {
      notificationItem.classList.remove('unread');
      notificationItem.classList.add('read');
      
      if (actionElement) {
        actionElement.setAttribute('data-action', 'mark-unread');
        actionElement.textContent = 'mark as unread';
      }
      
      updateUnreadCount();
    }
  }).catch(error => {
    console.error('Error marking as read:', error);
  });
}

// Handle mark as unread
function handleMarkAsUnreadAction(notificationId, notificationItem, actionElement) {
  console.log('👁️ Mark as unread:', notificationId);

  chrome.runtime.sendMessage({
    action: 'markAsUnread',
    notificationId: notificationId
  }).then(response => {
    if (response && response.success) {
      console.log('✅ Successfully marked as unread on backend');
      notificationItem.classList.remove('read');
      notificationItem.classList.add('unread');

      if (actionElement) {
        actionElement.setAttribute('data-action', 'mark-read');
        actionElement.textContent = 'mark as read';
      }

      updateUnreadCount();
    } else {
      console.error('❌ Failed to mark as unread on backend:', response);
      // Fallback : changer quand même l'apparence locale
      notificationItem.classList.remove('read');
      notificationItem.classList.add('unread');

      if (actionElement) {
        actionElement.setAttribute('data-action', 'mark-read');
        actionElement.textContent = 'mark as read';
      }

      updateUnreadCount();
    }
  }).catch(error => {
    console.error('Error marking as unread:', error);
    // Fallback : changer quand même l'apparence locale
    notificationItem.classList.remove('read');
    notificationItem.classList.add('unread');

    if (actionElement) {
      actionElement.setAttribute('data-action', 'mark-read');
      actionElement.textContent = 'mark as read';
    }

    updateUnreadCount();
  });
}

// Handle notification click
function handleNotificationClick(event) {
  const notificationItem = event.currentTarget;
  const notificationId = notificationItem.dataset.id;
  
  console.log('Notification clicked:', notificationId);
  
  chrome.runtime.sendMessage({
    action: 'markAsRead',
    notificationId: notificationId
  }).then(response => {
    if (response && response.success) {
      notificationItem.classList.remove('unread');
      notificationItem.classList.add('read');
      updateUnreadCount();
    }
  }).catch(error => {
    console.error('Error marking as read:', error);
  });
}

// Update unread count
function updateUnreadCount() {
  if (!miniInboxElement) return;
  
  const unreadItems = miniInboxElement.querySelectorAll('.notification-item.unread');
  const unreadCount = unreadItems.length;
  
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  if (statsElement) {
    const unreadCountElement = statsElement.querySelector('.unread-count');
    if (unreadCountElement) {
      unreadCountElement.textContent = `${unreadCount} unread`;
    }
  }
}

// Utility functions
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function formatTime(timestamp) {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) return 'now';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
  return `${Math.floor(diff / 86400000)}d`;
}

function formatFullTime(timestamp) {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

function formatNotificationBody(body) {
  if (!body) return '';

  let cleanBody = escapeHtml(body);
  // Convert markdown links - fixed regex escaping
  cleanBody = cleanBody.replace(/\/?u\/([a-zA-Z0-9_-]+)/g, '<a href="/user/$1" target="_blank">u/$1</a>');
  cleanBody = cleanBody.replace(/\/?r\/([a-zA-Z0-9_-]+)/g, '<a href="/r/$1" target="_blank">r/$1</a>');
  cleanBody = cleanBody.replace(/\\n/g, '<br>');

  return cleanBody;
}

function getNotificationContext(notification) {
  if (!notification) return '';
  
  let context = '';
  
  if (notification.type === 'comment_reply' || notification.type === 'username_mention') {
    if (notification.subreddit) {
      context = `in <a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>`;
    }
  }
  
  if (notification.type === 'post_reply') {
    if (notification.subreddit) {
      context = `in <a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>`;
    }
  }
  
  return context;
}

function getNotificationActions(notification) {
  if (!notification) return '';
  
  let actions = [];
  
  if (notification.type === 'comment_reply' || notification.type === 'username_mention' || notification.type === 'private_message') {
    actions.push(`<a href="#" class="notification-action" data-action="reply" data-id="${notification.id}">reply</a>`);
  }
  
  if (notification.permalink) {
    const contextUrl = notification.context || notification.permalink;
    actions.push(`<a href="${escapeHtml(contextUrl)}" class="notification-action" target="_blank">context</a>`);
  }
  
  if (notification.new) {
    actions.push(`<a href="#" class="notification-action" data-action="mark-read" data-id="${notification.id}">mark as read</a>`);
  } else {
    actions.push(`<a href="#" class="notification-action" data-action="mark-unread" data-id="${notification.id}">mark as unread</a>`);
  }
  
  return actions.join(' • ');
}

// Display states
function setLoadingState(message = 'Loading...') {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.className = 'mini-inbox-content loading-state';
  content.innerHTML = `
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">${message}</div>
    </div>
  `;
}

function displayError(error) {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.innerHTML = `<div class="error">Error: ${escapeHtml(error)}</div>`;
}

function displayNoNotifications() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.innerHTML = `
    <div class="no-notifications">
      <div class="no-notifications-icon">[EMPTY]</div>
      <div class="no-notifications-title">No notifications</div>
      <div class="no-notifications-subtitle">You're all caught up!</div>
    </div>
  `;
}

function displayAuthRequired() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.innerHTML = `
    <div class="auth-required">
      <div class="auth-required-icon">[LOCK]</div>
      <div class="auth-required-title">Authentication required</div>
      <div class="auth-required-subtitle">Connect to Reddit to see your real notifications</div>
      <button class="auth-required-button" onclick="chrome.runtime.sendMessage({action: 'openPopup'})">Connect to Reddit</button>
    </div>
  `;
}

function showMockDataIndicator() {
  if (!miniInboxElement) return;
  
  const header = miniInboxElement.querySelector('.mini-inbox-header');
  if (header) {
    const indicator = document.createElement('div');
    indicator.className = 'mock-data-indicator';
    indicator.innerHTML = `<span>[TEST]</span><span>Test data</span>`;
    indicator.style.cssText = `
      display: flex; align-items: center; gap: 4px; background: rgba(255, 193, 7, 0.1);
      border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 12px; padding: 4px 8px;
      font-size: 10px; color: #856404; margin-left: 8px;
    `;
    header.appendChild(indicator);
  }
}

// Detection functions
function isNotificationIcon(element) {
  if (!element) return false;
  
  // Check element and parents
  for (let el = element; el && el !== document; el = el.parentElement) {
    if (isNotificationIconByAttributes(el)) {
      return true;
    }
  }
  
  // Check if it's a link to inbox
  const href = element.getAttribute('href') || element.closest('a')?.getAttribute('href');
  if (href && (href.includes('/message/inbox') || href.includes('/notifications'))) {
    return true;
  }
  
  return false;
}

function isNotificationIconByAttributes(element) {
  console.log('🔍 DEBUG: Testing element for notification icon:', {
    tagName: element.tagName,
    className: element.className,
    id: element.id,
    ariaLabel: element.getAttribute('aria-label'),
    dataTestId: element.getAttribute('data-testid'),
    href: element.getAttribute('href')
  });

  // IMPORTANT: Exclure les éléments de la mini-inbox
  if (element.closest('.mini-inbox-container')) {
    console.log('❌ DEBUG: Element is inside mini-inbox, excluding');
    return false;
  }

  // IMPORTANT: Exclure les boutons d'action spécifiques
  const className = element.className;
  if (typeof className === 'string' &&
      (className.includes('notification-action') ||
       className.includes('refresh-button') ||
       className.includes('close-btn') ||
       className.includes('reply-') ||
       className.includes('quick-reply'))) {
    console.log('❌ DEBUG: Element is a mini-inbox action button, excluding');
    return false;
  }

  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel) {
    const lowerLabel = ariaLabel.toLowerCase();
    // TRÈS STRICTE : Seulement les aria-labels très spécifiques
    if (lowerLabel === 'notifications' ||
        lowerLabel === 'inbox' ||
        lowerLabel === 'notification bell' ||
        lowerLabel.startsWith('notifications ') ||
        lowerLabel.startsWith('inbox ')) {
      console.log('✅ DEBUG: Matched by specific aria-label:', ariaLabel);
      return true;
    }
  }

  const id = element.id;
  if (id === 'mail' || id === 'inbox' || id === 'messages' ||
      id === 'notification' || id === 'notifications') {
    console.log('✅ DEBUG: Matched by id:', id);
    return true;
  }

  // Détection par className - TRÈS STRICTE
  if (typeof className === 'string') {
    // SEULEMENT les classes très spécifiques aux notifications
    if (className.includes('notification-bell') ||
        className.includes('inbox-icon') ||
        className.includes('mail-icon') ||
        (className.includes('bell') && !className.includes('button') && !className.includes('action'))) {
      console.log('✅ DEBUG: Matched by specific className:', className);
      return true;
    }
  }

  const tagName = element.tagName?.toLowerCase();
  if (tagName === 'shreddit-notification-bell' || tagName === 'reddit-notification-bell' ||
      tagName === 'notification-bell' || tagName === 'reddit-header-action-button') {
    console.log('✅ DEBUG: Matched by tagName:', tagName);
    return true;
  }

  const dataTestId = element.getAttribute('data-testid');
  if (dataTestId && (dataTestId.includes('notification') ||
      dataTestId.includes('inbox') ||
      dataTestId.includes('message'))) {
    console.log('✅ DEBUG: Matched by data-testid:', dataTestId);
    return true;
  }

  // DÉTECTION HREF - TRÈS STRICTE
  const href = element.getAttribute('href');
  if (href && href === '/message/inbox') {
    console.log('✅ DEBUG: Matched by exact href:', href);
    return true;
  }

  // Détection par SVG - TRÈS STRICTE
  const svg = element.querySelector('svg') || (element.tagName === 'SVG' ? element : null);
  if (svg) {
    const svgClass = svg.className?.baseVal || svg.className;
    if (typeof svgClass === 'string' &&
        (svgClass.includes('notification-bell') || svgClass.includes('inbox-icon'))) {
      console.log('✅ DEBUG: Matched by specific SVG class:', svgClass);
      return true;
    }
  }

  console.log('❌ DEBUG: No match found for element');
  return false;
}

function getIconPosition(element) {
  const rect = element.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  
  return {
    top: rect.bottom + scrollTop + 5,
    left: rect.left + scrollLeft,
    right: rect.right + scrollLeft,
    width: rect.width,
    height: rect.height,
    element: element
  };
}

function positionMiniInbox(overlayElement, iconPosition) {
  const container = overlayElement.querySelector('.mini-inbox-container');
  if (!container) return;
  
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  
  const inboxWidth = 400;
  const inboxHeight = 500;
  
  let top = iconPosition.top;
  let left = iconPosition.left;
  
  if (left + inboxWidth > windowWidth) {
    left = iconPosition.right - inboxWidth;
  }
  
  if (top + inboxHeight > windowHeight + scrollTop) {
    top = iconPosition.top - iconPosition.height - inboxHeight - 10;
    if (top < scrollTop) {
      top = scrollTop + (windowHeight - inboxHeight) / 2;
    }
  }
  
  left = Math.max(10, left);
  top = Math.max(scrollTop + 10, top);
  
  overlayElement.style.position = 'absolute';
  overlayElement.style.top = '0';
  overlayElement.style.left = '0';
  overlayElement.style.width = '100%';
  overlayElement.style.height = '100%';
  overlayElement.style.backgroundColor = 'transparent';
  overlayElement.style.pointerEvents = 'none';
  
  container.style.position = 'absolute';
  container.style.top = `${top}px`;
  container.style.left = `${left}px`;
  container.style.pointerEvents = 'auto';
  container.style.zIndex = '10001';
}

function detectDarkTheme() {
  // 1. Vérifier les préférences système
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return true;
  }

  const body = document.body;
  const html = document.documentElement;

  // 2. Vérifier les classes CSS Reddit
  if (body.classList.contains('dark') || body.classList.contains('dark-theme') ||
      html.classList.contains('dark') || html.classList.contains('dark-theme') ||
      body.classList.contains('theme-dark') || html.classList.contains('theme-dark') ||
      body.classList.contains('theme--nightmode') || html.classList.contains('theme--nightmode')) {
    return true;
  }

  // 3. Vérifier les attributs data Reddit modernes
  if (body.getAttribute('data-theme') === 'dark' ||
      html.getAttribute('data-theme') === 'dark' ||
      body.getAttribute('data-colorscheme') === 'dark' ||
      html.getAttribute('data-colorscheme') === 'dark' ||
      body.getAttribute('data-nightmode') === 'true' ||
      html.getAttribute('data-nightmode') === 'true') {
    return true;
  }

  // 4. Vérifier Reddit React (shreddit-app)
  const shredditApp = document.querySelector('shreddit-app');
  if (shredditApp && shredditApp.getAttribute('theme') === 'dark') {
    return true;
  }

  // 5. Vérifier les sélecteurs spécifiques Reddit
  if (document.querySelector('[data-theme="dark"]') ||
      document.querySelector('[data-colorscheme="dark"]') ||
      document.querySelector('.theme--nightmode')) {
    return true;
  }

  // 6. Vérifier les styles computed pour détecter le thème sombre
  const computedStyle = window.getComputedStyle(body);
  const backgroundColor = computedStyle.backgroundColor;
  if (backgroundColor && backgroundColor.includes('rgb')) {
    const rgb = backgroundColor.match(/\d+/g);
    if (rgb && rgb.length >= 3) {
      const r = parseInt(rgb[0]);
      const g = parseInt(rgb[1]);
      const b = parseInt(rgb[2]);
      // Si la couleur de fond est sombre (luminosité < 128)
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      if (brightness < 128) {
        return true;
      }
    }
  }

  return false;
}

// Fonction pour mettre à jour le thème de la mini-inbox
function updateMiniInboxTheme() {
  if (!miniInboxElement) return;

  const container = miniInboxElement.querySelector('.mini-inbox-container');
  if (!container) return;

  const isDarkTheme = detectDarkTheme();

  if (isDarkTheme) {
    container.classList.add('dark-theme');
  } else {
    container.classList.remove('dark-theme');
  }
}

// Observer pour détecter les changements de thème
function setupThemeObserver() {
  // Observer les changements d'attributs sur html et body
  const observer = new MutationObserver((mutations) => {
    let themeChanged = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes') {
        const attrName = mutation.attributeName;
        if (attrName === 'data-theme' ||
            attrName === 'data-colorscheme' ||
            attrName === 'data-nightmode' ||
            attrName === 'class') {
          themeChanged = true;
        }
      }
    });

    if (themeChanged) {
      updateMiniInboxTheme();
    }
  });

  // Observer html et body
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-theme', 'data-colorscheme', 'data-nightmode', 'class']
  });

  observer.observe(document.body, {
    attributes: true,
    attributeFilter: ['data-theme', 'data-colorscheme', 'data-nightmode', 'class']
  });

  // Observer shreddit-app si présent
  const shredditApp = document.querySelector('shreddit-app');
  if (shredditApp) {
    observer.observe(shredditApp, {
      attributes: true,
      attributeFilter: ['theme']
    });
  }

  // Écouter les changements de préférences système
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addListener(() => {
      updateMiniInboxTheme();
    });
  }
}

// Fonction pour rafraîchir les notifications
async function refreshNotifications() {
  if (!miniInboxElement) return;

  console.log('Refreshing notifications...');

  const refreshButton = miniInboxElement.querySelector('.refresh-button');

  // Ajouter un indicateur de chargement
  if (refreshButton) {
    refreshButton.classList.add('loading');
    refreshButton.disabled = true;
    refreshButton.innerHTML = '⟳';
  }

  setLoadingState('Refreshing notifications...');

  try {
    const response = await chrome.runtime.sendMessage({
      action: 'getNotifications',
      force: true
    });

    if (response && response.success) {
      if (response.notifications && response.notifications.length > 0) {
        displayNotifications(response.notifications);
        if (response.isMockData) {
          showMockDataIndicator();
        }
      } else {
        displayNoNotifications();
      }
    } else {
      if (response && response.needsAuth) {
        displayAuthRequired();
      } else {
        displayError(response.error || 'Error refreshing notifications');
      }
    }
  } catch (error) {
    console.error('Error refreshing notifications:', error);
    displayError('Error refreshing notifications');
  } finally {
    // Restaurer le bouton refresh
    if (refreshButton) {
      refreshButton.classList.remove('loading');
      refreshButton.disabled = false;
      refreshButton.innerHTML = '↻';
    }
  }
}

// Initialize extension
init();
setupThemeObserver();