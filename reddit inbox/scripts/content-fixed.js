// Reddit Mini Inbox Extension - Content Script FIXED
// Version: 2.1.0 - Corrections critiques appliquées
console.log('🚀 Reddit Mini Inbox v2.1.0 FIXED loaded');

// Global state
let miniInboxOpen = false;
let miniInboxElement = null;

// Error recovery system
class ErrorRecovery {
  static async retryOperation(operation, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        console.warn(`Attempt ${i + 1} failed:`, error);
        if (i === maxRetries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }

  static async safeChromeSendMessage(message) {
    return this.retryOperation(async () => {
      if (!chrome.runtime) throw new Error('Chrome runtime not available');
      return await chrome.runtime.sendMessage(message);
    });
  }
}

// Background script health check
async function checkBackgroundScript() {
  try {
    console.log('🔍 DEBUG: Checking background script health...');
    const response = await chrome.runtime.sendMessage({ action: 'ping' });
    const isHealthy = response && response.pong === true;
    console.log('🔍 DEBUG: Background script health:', isHealthy ? 'HEALTHY' : 'UNHEALTHY');
    return isHealthy;
  } catch (error) {
    console.error('💥 DEBUG: Background script not responding:', error);
    return false;
  }
}

// Notification selectors for different Reddit versions
const REDDIT_SELECTORS = {
  old: {
    icons: ['#mail', '.mail', 'a[href*="/message/inbox"]'],
    containers: ['#header-bottom-right', '.user']
  },
  new: {
    icons: ['[aria-label*="notification"]', '[aria-label*="inbox"]', 'a[href*="/message/inbox"]'],
    containers: ['[data-testid="header"]', 'header']
  },
  'new-react': {
    icons: ['button[aria-label*="notification"]', '[data-testid="notification-bell"]', 'a[href*="/message/inbox"]'],
    containers: ['shreddit-app', 'reddit-header-large']
  }
};

// Initialize extension
function init() {
  console.log('🚀 DEBUG: Initializing Reddit Mini Inbox...');
  
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setup);
  } else {
    setup();
  }
}

// Setup notification interception
function setup() {
  console.log('🔧 DEBUG: Setting up notification interception...');
  
  // Add click listener with event delegation
  document.addEventListener('click', handleClick, true);
  document.addEventListener('keydown', handleKeyPress);
  
  console.log('✅ DEBUG: Reddit Mini Inbox setup complete');
}

// Handle clicks with improved logic
function handleClick(event) {
  const target = event.target;

  // Debug clicks when mini inbox is open
  if (miniInboxOpen) {
    console.log('🖱️ DEBUG: Click detected', {
      target: target.tagName,
      className: target.className,
      isInMiniInbox: !!target.closest('.mini-inbox-container')
    });
  }

  // Check if click is on notification icon
  if (isNotificationIcon(target)) {
    console.log('🎯 DEBUG: Notification icon clicked, intercepting...');
    event.preventDefault();
    event.stopPropagation();

    const iconPosition = getIconPosition(target);
    toggleMiniInbox(iconPosition);
    return false;
  }

  // Intercept inbox links for testing
  const href = target.getAttribute('href') || target.closest('a')?.getAttribute('href');
  if (href && href.includes('/message/inbox')) {
    console.log('🔧 DEBUG: Intercepting inbox link...');
    event.preventDefault();
    event.stopPropagation();

    const iconPosition = getIconPosition(target);
    toggleMiniInbox(iconPosition);
    return false;
  }

  // Check if click is outside mini inbox
  if (miniInboxOpen && !target.closest('.mini-inbox-container')) {
    console.log('🌍 DEBUG: Click outside mini inbox, closing');
    closeMiniInbox();
  }
}

// Handle keyboard
function handleKeyPress(event) {
  if (event.key === 'Escape' && miniInboxOpen) {
    closeMiniInbox();
  }
}

// Toggle mini inbox
function toggleMiniInbox(iconPosition = null) {
  console.log('🔄 DEBUG: toggleMiniInbox called', { miniInboxOpen, iconPosition });
  if (miniInboxOpen) {
    closeMiniInbox();
  } else {
    openMiniInbox(iconPosition);
  }
}

// Open mini inbox with improved error handling
async function openMiniInbox(iconPosition = null) {
  console.log('🚀 DEBUG: openMiniInbox START');

  if (miniInboxElement) {
    console.log('🗑️ DEBUG: Removing existing miniInboxElement');
    miniInboxElement.remove();
  }

  console.log('🏗️ DEBUG: Creating mini inbox element');
  createMiniInboxElement(iconPosition);
  setLoadingState('Loading notifications...');
  
  try {
    // CRITICAL FIX: Check background script health first
    console.log('🔍 DEBUG: Checking background script health...');
    const isBackgroundHealthy = await checkBackgroundScript();
    
    if (!isBackgroundHealthy) {
      console.warn('⚠️ DEBUG: Background script unhealthy, using fallback');
      const testNotifications = getTestNotifications();
      displayNotifications(testNotifications);
      showMockDataIndicator();
      miniInboxOpen = true;
      return;
    }

    console.log('📡 DEBUG: Sending getNotifications message...');
    const response = await ErrorRecovery.safeChromeSendMessage({
      action: 'getNotifications',
      force: false
    });

    console.log('📡 DEBUG: Response received', response);
    
    if (response && response.success) {
      console.log('✅ DEBUG: Response successful');

      if (response.notifications && response.notifications.length > 0) {
        displayNotifications(response.notifications);
        if (response.isMockData) {
          showMockDataIndicator();
        }
      } else {
        displayNoNotifications();
      }
    } else {
      console.log('❌ DEBUG: Response failed', response);
      if (response && response.needsAuth) {
        displayAuthRequired();
      } else {
        displayError(response?.error || 'Unknown error');
      }
    }
  } catch (error) {
    console.error('💥 DEBUG: Error getting notifications:', error);

    // FALLBACK: Use hardcoded test data
    console.log('🔄 DEBUG: Using fallback test data');
    try {
      const testNotifications = getTestNotifications();
      displayNotifications(testNotifications);
      showMockDataIndicator();
    } catch (fallbackError) {
      console.error('💥 DEBUG: Fallback failed:', fallbackError);
      displayError('Error loading notifications');
    }
  }
  
  miniInboxOpen = true;
  console.log('✅ DEBUG: openMiniInbox completed');
}

// Test notifications for fallback
function getTestNotifications() {
  return [
    {
      id: 'test1',
      type: 'comment_reply',
      author: 'test_user',
      subject: 'Test Reply - Click buttons to test',
      body: 'This is a test notification to verify button functionality. All buttons should work.',
      created_utc: Math.floor(Date.now() / 1000) - 3600,
      new: true,
      permalink: '/r/test/comments/abc/test/',
      subreddit: 'test',
      context: '/r/test/comments/abc/test/?context=3'
    },
    {
      id: 'test2',
      type: 'private_message',
      author: 'debug_user',
      subject: 'Debug Test Message',
      body: 'Another test notification. Try the reply and mark read buttons.',
      created_utc: Math.floor(Date.now() / 1000) - 7200,
      new: false,
      permalink: '/message/messages/test2'
    }
  ];
}

// Close mini inbox
function closeMiniInbox() {
  console.log('❌ DEBUG: Closing mini inbox...');
  
  if (miniInboxElement) {
    miniInboxElement.classList.remove('show');
    const container = miniInboxElement.querySelector('.mini-inbox-container');
    if (container) {
      container.classList.add('closing');
    }
    
    setTimeout(() => {
      if (miniInboxElement && miniInboxElement.parentNode) {
        miniInboxElement.remove();
      }
      miniInboxElement = null;
    }, 150);
  }
  
  miniInboxOpen = false;
  console.log('✅ DEBUG: Mini inbox closed');
}

// Create mini inbox element with event delegation
function createMiniInboxElement(iconPosition = null) {
  console.log('🏗️ DEBUG: Creating mini inbox element...');
  
  miniInboxElement = document.createElement('div');
  miniInboxElement.id = 'reddit-mini-inbox-overlay';
  
  const isDarkTheme = detectDarkTheme();
  const themeClass = isDarkTheme ? 'dark-theme' : '';
  
  miniInboxElement.innerHTML = `
    <div class="mini-inbox-container opening ${themeClass}">
      <div class="mini-inbox-header">
        <div class="mini-inbox-title">
          <span class="mini-inbox-icon">[INBOX]</span>
          <span>inbox</span>
        </div>
        <div class="mini-inbox-actions">
          <button class="refresh-button" type="button" aria-label="Refresh" title="DEBUG: Refresh - Click to test">↻</button>
          <button class="close-btn" type="button" aria-label="Close" title="DEBUG: Close - Click to test">×</button>
        </div>
      </div>
      <div class="mini-inbox-stats" style="display: none;">
        <span class="unread-count">0 unread</span>
        <span class="total-count">0 total</span>
      </div>
      <div class="mini-inbox-content loading-state">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading notifications...</div>
        </div>
      </div>
    </div>
  `;
  
  if (iconPosition) {
    positionMiniInbox(miniInboxElement, iconPosition);
  }
  
  // CRITICAL FIX: Use event delegation instead of individual listeners
  setupEventDelegation(miniInboxElement);
  
  // Handle clicks outside
  miniInboxElement.addEventListener('click', (event) => {
    if (event.target === miniInboxElement) {
      closeMiniInbox();
    }
  });
  
  // Handle escape key
  miniInboxElement.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      event.preventDefault();
      closeMiniInbox();
    }
  });
  
  document.body.appendChild(miniInboxElement);
  
  setTimeout(() => {
    miniInboxElement.classList.add('show');
    const container = miniInboxElement.querySelector('.mini-inbox-container');
    updateMiniInboxTheme();
    container.classList.add('show');
  }, 10);
  
  console.log('✅ DEBUG: Mini inbox element created with event delegation');
}

// CRITICAL FIX: Event delegation system
function setupEventDelegation(container) {
  console.log('🔗 DEBUG: Setting up event delegation...');
  
  container.addEventListener('click', (event) => {
    const target = event.target;
    console.log('🖱️ DEBUG: Delegated click on:', target.tagName, target.className);

    // Handle refresh button
    if (target.matches('.refresh-button')) {
      console.log('🔄 DEBUG: ===== REFRESH BUTTON CLICKED =====');
      event.preventDefault();
      event.stopPropagation();
      refreshNotifications();
      return;
    }

    // Handle close button
    if (target.matches('.close-btn')) {
      console.log('❌ DEBUG: ===== CLOSE BUTTON CLICKED =====');
      event.preventDefault();
      event.stopPropagation();
      closeMiniInbox();
      return;
    }

    // Handle notification action buttons
    if (target.matches('.notification-action')) {
      console.log('🎯 DEBUG: ===== ACTION BUTTON CLICKED =====');
      event.preventDefault();
      event.stopPropagation();

      const actionType = target.getAttribute('data-action');
      const notificationId = target.getAttribute('data-id');
      const notificationItem = target.closest('.notification-item');

      console.log('🎯 DEBUG: Action details:', {
        actionType,
        notificationId,
        hasNotificationItem: !!notificationItem
      });

      if (actionType && notificationId) {
        handleNotificationAction(actionType, notificationId, notificationItem, target);
      } else {
        console.error('❌ DEBUG: Missing action data');
      }
      return;
    }

    // Handle notification item clicks
    if (target.matches('.notification-item') || target.closest('.notification-item')) {
      const notificationItem = target.matches('.notification-item') ? target : target.closest('.notification-item');
      if (!target.closest('.notification-actions') && !target.closest('.quick-reply-interface')) {
        handleNotificationClick(event, notificationItem);
      }
      return;
    }
  });
  
  console.log('✅ DEBUG: Event delegation setup complete');
}

// Display notifications with DOM ready detection
function displayNotifications(notifications) {
  console.log('📋 DEBUG: displayNotifications START', {
    count: notifications?.length
  });

  if (!miniInboxElement) {
    console.error('❌ DEBUG: miniInboxElement is null!');
    return;
  }

  setLoadingState('success');

  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  
  const unreadCount = notifications.filter(n => n.new).length;
  const totalCount = notifications.length;
  
  if (statsElement) {
    statsElement.style.display = 'flex';
    statsElement.querySelector('.unread-count').textContent = `${unreadCount} unread`;
    statsElement.querySelector('.total-count').textContent = `${totalCount} total`;
  }
  
  if (notifications.length === 0) {
    displayNoNotifications();
    return;
  }

  const notificationsHtml = notifications.map((notification, index) => {
    const typeClass = notification.type ? notification.type.replace('_', '-') : 'message';
    const contextText = getNotificationContext(notification);
    const actionsHtml = getNotificationActions(notification);
    
    return `
      <div class="notification-item ${notification.new ? 'unread' : 'read'} ${typeClass}" 
           data-id="${notification.id}" 
           data-type="${notification.type || 'message'}"
           tabindex="0"
           role="button"
           aria-label="Notification from ${escapeHtml(notification.author)}">
        <div class="notification-header">
          <a href="/user/${escapeHtml(notification.author)}" class="notification-author" target="_blank">
            ${escapeHtml(notification.author)}
          </a>
          <span class="notification-time" title="${formatFullTime(notification.created_utc)}">
            ${formatTime(notification.created_utc)}
          </span>
        </div>
        <div class="notification-subject">${escapeHtml(notification.subject)}</div>
        ${contextText ? `<div class="notification-context">${contextText}</div>` : ''}
        <div class="notification-body">${formatNotificationBody(notification.body)}</div>
        <div class="notification-actions">
          ${actionsHtml}
        </div>
      </div>
    `;
  }).join('');
  
  console.log('📝 DEBUG: Setting content innerHTML');
  content.innerHTML = notificationsHtml;

  // CRITICAL FIX: Use MutationObserver to detect when DOM is ready
  waitForButtonsReady(content, () => {
    console.log('✅ DEBUG: Buttons are ready, adding visual indicators');
    addVisualIndicators(content);
    console.log('✅ DEBUG: displayNotifications COMPLETED');
  });
}

// CRITICAL FIX: Wait for buttons to be ready using MutationObserver
function waitForButtonsReady(container, callback) {
  const buttons = container.querySelectorAll('.notification-action');
  if (buttons.length > 0) {
    // Buttons already exist
    callback();
    return;
  }

  // Use MutationObserver to detect when buttons are added
  const observer = new MutationObserver((mutations) => {
    const buttons = container.querySelectorAll('.notification-action');
    if (buttons.length > 0) {
      callback();
      observer.disconnect();
    }
  });

  observer.observe(container, {
    childList: true,
    subtree: true
  });

  // Safety timeout
  setTimeout(() => {
    observer.disconnect();
    callback(); // Try anyway
  }, 1000);
}

// Add visual indicators for debugging
function addVisualIndicators(content) {
  const actionButtons = content.querySelectorAll('.notification-action');
  console.log(`🎨 DEBUG: Adding visual indicators to ${actionButtons.length} buttons`);
  
  actionButtons.forEach((button, index) => {
    button.style.border = '2px solid red';
    button.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
    button.title = `DEBUG: ${button.getAttribute('data-action')} - Click to test`;
    console.log(`🎨 DEBUG: Button ${index} styled for testing`);
  });

  // Style refresh and close buttons
  const refreshBtn = miniInboxElement.querySelector('.refresh-button');
  const closeBtn = miniInboxElement.querySelector('.close-btn');
  
  if (refreshBtn) {
    refreshBtn.style.border = '2px solid blue';
    refreshBtn.style.backgroundColor = 'rgba(0, 0, 255, 0.1)';
  }
  
  if (closeBtn) {
    closeBtn.style.border = '2px solid green';
    closeBtn.style.backgroundColor = 'rgba(0, 255, 0, 0.1)';
  }
}

// Handle notification actions
function handleNotificationAction(actionType, notificationId, notificationItem, actionElement) {
  console.log('🎬 DEBUG: ===== HANDLE NOTIFICATION ACTION =====');
  console.log('🎬 DEBUG: Action:', actionType, 'ID:', notificationId);

  switch (actionType) {
    case 'reply':
      handleReplyAction(notificationId, notificationItem);
      break;
    case 'mark-read':
      handleMarkAsReadAction(notificationId, notificationItem, actionElement);
      break;
    case 'mark-unread':
      handleMarkAsUnreadAction(notificationId, notificationItem, actionElement);
      break;
    default:
      console.warn('❌ DEBUG: Unknown action:', actionType);
  }
}

// Handle reply action
function handleReplyAction(notificationId, notificationItem) {
  console.log('💬 DEBUG: ===== HANDLE REPLY ACTION =====');

  const existingReply = notificationItem.querySelector('.quick-reply-interface');
  if (existingReply) {
    existingReply.remove();
    return;
  }

  const replyInterface = createQuickReplyInterface(notificationId, notificationItem);
  const notificationBody = notificationItem.querySelector('.notification-body');

  if (notificationBody && replyInterface) {
    notificationBody.insertAdjacentElement('afterend', replyInterface);
    const textarea = replyInterface.querySelector('.reply-textarea');
    if (textarea) {
      textarea.focus();
    }
    // Mark as read since user is interacting
    handleMarkAsReadAction(notificationId, notificationItem);
  }
}

// Create quick reply interface
function createQuickReplyInterface(notificationId, notificationItem) {
  const replyDiv = document.createElement('div');
  replyDiv.className = 'quick-reply-interface';
  replyDiv.style.cssText = `
    background: #f8f9fa; border: 1px solid #e5e5e5; border-radius: 4px;
    margin: 8px 0; padding: 0; animation: slideDown 0.2s ease-out;
  `;
  
  replyDiv.innerHTML = `
    <div style="background: #ff4500; color: white; padding: 6px 10px; display: flex; justify-content: space-between; border-radius: 4px 4px 0 0;">
      <span style="font-weight: bold; font-size: 11px;">Quick Reply</span>
      <button class="reply-close" style="background: none; border: none; color: white; cursor: pointer; font-size: 16px;">×</button>
    </div>
    <div style="padding: 10px;">
      <textarea class="reply-textarea" placeholder="Write your reply..." rows="3" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-size: 11px; box-sizing: border-box; margin-bottom: 8px; resize: vertical;"></textarea>
      <div style="display: flex; gap: 6px; flex-wrap: wrap;">
        <button class="reply-send" style="background: #ff4500; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer; font-weight: bold;">Send Reply</button>
        <button class="reply-cancel" style="background: #f5f5f5; color: #666; border: 1px solid #ddd; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Cancel</button>
        <button class="reply-full" style="background: #0079d3; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Open Full Editor</button>
      </div>
    </div>
  `;
  
  // Add event listeners for reply interface
  const closeBtn = replyDiv.querySelector('.reply-close');
  const cancelBtn = replyDiv.querySelector('.reply-cancel');
  const sendBtn = replyDiv.querySelector('.reply-send');
  const fullBtn = replyDiv.querySelector('.reply-full');
  const textarea = replyDiv.querySelector('.reply-textarea');
  
  const closeReply = () => replyDiv.remove();
  closeBtn.addEventListener('click', closeReply);
  cancelBtn.addEventListener('click', closeReply);
  
  sendBtn.addEventListener('click', () => {
    const text = textarea.value.trim();
    if (text) {
      sendQuickReply(notificationId, text, replyDiv);
    } else {
      textarea.focus();
      textarea.style.borderColor = '#ff4500';
      setTimeout(() => textarea.style.borderColor = '', 2000);
    }
  });
  
  fullBtn.addEventListener('click', () => {
    const text = textarea.value.trim();
    let url = '/message/compose';
    if (text) {
      url += '?message=' + encodeURIComponent(text);
    }
    
    const tempLink = document.createElement('a');
    tempLink.href = url;
    tempLink.target = '_blank';
    tempLink.rel = 'noopener noreferrer';
    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
    
    closeReply();
  });
  
  textarea.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      sendBtn.click();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      closeReply();
    }
  });
  
  return replyDiv;
}

// Send quick reply
async function sendQuickReply(notificationId, replyText, replyInterface) {
  const sendBtn = replyInterface.querySelector('.reply-send');
  const textarea = replyInterface.querySelector('.reply-textarea');
  
  sendBtn.disabled = true;
  sendBtn.textContent = 'Sending...';
  textarea.disabled = true;
  
  try {
    // Simulate sending (replace with actual API call)
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    replyInterface.innerHTML = `
      <div style="padding: 15px; text-align: center; background: #f0f8ff; border-radius: 4px;">
        <div style="font-size: 24px; margin-bottom: 8px;">✅</div>
        <div style="color: #0079d3; font-weight: bold; margin-bottom: 10px;">Reply sent successfully!</div>
        <button onclick="this.closest('.quick-reply-interface').remove()" style="background: #0079d3; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Close</button>
      </div>
    `;
    
    setTimeout(() => {
      if (replyInterface.parentNode) {
        replyInterface.remove();
      }
    }, 3000);
    
  } catch (error) {
    console.error('Error sending reply:', error);
    sendBtn.disabled = false;
    sendBtn.textContent = 'Send Reply';
    textarea.disabled = false;
    textarea.focus();
  }
}

// Handle mark as read with improved error handling
async function handleMarkAsReadAction(notificationId, notificationItem, actionElement = null) {
  console.log('✅ DEBUG: Mark as read:', notificationId);
  
  try {
    const response = await ErrorRecovery.safeChromeSendMessage({
      action: 'markAsRead',
      notificationId: notificationId
    });

    if (response && response.success) {
      notificationItem.classList.remove('unread');
      notificationItem.classList.add('read');
      
      if (actionElement) {
        actionElement.setAttribute('data-action', 'mark-unread');
        actionElement.textContent = 'mark as unread';
      }
      
      updateUnreadCount();
      console.log('✅ DEBUG: Successfully marked as read');
    } else {
      console.warn('⚠️ DEBUG: Mark as read failed:', response);
    }
  } catch (error) {
    console.error('💥 DEBUG: Error marking as read:', error);
    // Still update UI optimistically
    notificationItem.classList.remove('unread');
    notificationItem.classList.add('read');
    updateUnreadCount();
  }
}

// Handle mark as unread
function handleMarkAsUnreadAction(notificationId, notificationItem, actionElement) {
  console.log('👁️ DEBUG: Mark as unread:', notificationId);
  
  notificationItem.classList.remove('read');
  notificationItem.classList.add('unread');
  
  actionElement.setAttribute('data-action', 'mark-read');
  actionElement.textContent = 'mark as read';
  
  updateUnreadCount();
}

// Handle notification click
function handleNotificationClick(event, notificationItem) {
  const notificationId = notificationItem.dataset.id;
  console.log('🖱️ DEBUG: Notification clicked:', notificationId);
  
  handleMarkAsReadAction(notificationId, notificationItem);
}

// Refresh notifications
async function refreshNotifications() {
  console.log('🔄 DEBUG: ===== REFRESH NOTIFICATIONS =====');
  
  if (!miniInboxElement) {
    console.error('❌ DEBUG: No mini inbox element');
    return;
  }

  setLoadingState('Refreshing...');
  
  try {
    const isBackgroundHealthy = await checkBackgroundScript();
    
    if (!isBackgroundHealthy) {
      console.warn('⚠️ DEBUG: Background unhealthy during refresh, using fallback');
      const testNotifications = getTestNotifications();
      displayNotifications(testNotifications);
      showMockDataIndicator();
      return;
    }

    const response = await ErrorRecovery.safeChromeSendMessage({
      action: 'getNotifications',
      force: true // Force refresh
    });

    if (response && response.success) {
      displayNotifications(response.notifications);
      if (response.isMockData) {
        showMockDataIndicator();
      }
    } else {
      displayError(response?.error || 'Refresh failed');
    }
  } catch (error) {
    console.error('💥 DEBUG: Refresh error:', error);
    displayError('Refresh failed');
  }
}

// Update unread count
function updateUnreadCount() {
  if (!miniInboxElement) return;
  
  const unreadItems = miniInboxElement.querySelectorAll('.notification-item.unread');
  const unreadCount = unreadItems.length;
  
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  if (statsElement) {
    const unreadCountElement = statsElement.querySelector('.unread-count');
    if (unreadCountElement) {
      unreadCountElement.textContent = `${unreadCount} unread`;
    }
  }
}

// Utility functions
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function formatTime(timestamp) {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) return 'now';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
  return `${Math.floor(diff / 86400000)}d`;
}

function formatFullTime(timestamp) {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

function formatNotificationBody(body) {
  if (!body) return '';
  
  let cleanBody = escapeHtml(body);
  cleanBody = cleanBody.replace(/\/?u\/([a-zA-Z0-9_-]+)/g, '<a href="/user/$1" target="_blank">u/$1</a>');
  cleanBody = cleanBody.replace(/\/?r\/([a-zA-Z0-9_-]+)/g, '<a href="/r/$1" target="_blank">r/$1</a>');
  cleanBody = cleanBody.replace(/\n/g, '<br>');
  
  return cleanBody;
}

function getNotificationContext(notification) {
  if (notification.subreddit) {
    return `in <a href="/r/${notification.subreddit}" target="_blank">r/${notification.subreddit}</a>`;
  }
  return '';
}

function getNotificationActions(notification) {
  const actions = [];
  
  // Reply action for all types
  actions.push(`<button class="notification-action" data-action="reply" data-id="${notification.id}" title="Reply to this notification">reply</button>`);
  
  // Mark read/unread action
  if (notification.new) {
    actions.push(`<button class="notification-action" data-action="mark-read" data-id="${notification.id}" title="Mark as read">mark as read</button>`);
  } else {
    actions.push(`<button class="notification-action" data-action="mark-unread" data-id="${notification.id}" title="Mark as unread">mark as unread</button>`);
  }
  
  return actions.join(' | ');
}

// Icon detection functions
function isNotificationIcon(element) {
  if (!element) return false;
  
  // Check for common notification icon patterns
  const href = element.getAttribute('href') || element.closest('a')?.getAttribute('href');
  if (href && href.includes('/message/inbox')) return true;
  
  // Check for aria-labels
  const ariaLabel = element.getAttribute('aria-label') || element.closest('[aria-label]')?.getAttribute('aria-label');
  if (ariaLabel && (ariaLabel.toLowerCase().includes('notification') || ariaLabel.toLowerCase().includes('inbox'))) return true;
  
  // Check for common selectors
  const selectors = [
    '#mail', '.mail',
    '[data-testid="notification-bell"]',
    'button[aria-label*="notification"]',
    'a[href*="/message/inbox"]'
  ];
  
  return selectors.some(selector => element.matches(selector) || element.closest(selector));
}

function getIconPosition(element) {
  const rect = element.getBoundingClientRect();
  return {
    top: rect.bottom + 5,
    left: rect.left,
    width: rect.width
  };
}

function positionMiniInbox(miniInboxElement, iconPosition) {
  const container = miniInboxElement.querySelector('.mini-inbox-container');
  if (!container || !iconPosition) return;
  
  container.style.position = 'fixed';
  container.style.top = `${iconPosition.top}px`;
  container.style.left = `${iconPosition.left}px`;
  container.style.zIndex = '10000';
}

// Theme detection
function detectDarkTheme() {
  // Check for dark theme indicators
  const body = document.body;
  const html = document.documentElement;
  
  // Check for common dark theme classes
  const darkClasses = ['dark', 'dark-theme', 'night-mode'];
  const hasDarkClass = darkClasses.some(cls => 
    body.classList.contains(cls) || html.classList.contains(cls)
  );
  
  if (hasDarkClass) return true;
  
  // Check background color
  const bodyStyle = window.getComputedStyle(body);
  const bgColor = bodyStyle.backgroundColor;
  
  // Parse RGB values
  const rgbMatch = bgColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (rgbMatch) {
    const [, r, g, b] = rgbMatch.map(Number);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness < 128; // Dark if brightness is low
  }
  
  return false;
}

function updateMiniInboxTheme() {
  if (!miniInboxElement) return;
  
  const isDark = detectDarkTheme();
  const container = miniInboxElement.querySelector('.mini-inbox-container');
  
  if (container) {
    if (isDark) {
      container.classList.add('dark-theme');
    } else {
      container.classList.remove('dark-theme');
    }
  }
}

// Loading states
function setLoadingState(message) {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  if (!content) return;
  
  if (message === 'success') {
    content.classList.remove('loading-state');
    return;
  }
  
  content.classList.add('loading-state');
  content.innerHTML = `
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">${message}</div>
    </div>
  `;
}

function displayNoNotifications() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.classList.remove('loading-state');
  content.innerHTML = `
    <div class="empty-state">
      <div class="empty-icon">📭</div>
      <div class="empty-title">No notifications</div>
      <div class="empty-subtitle">You're all caught up!</div>
    </div>
  `;
}

function displayError(errorMessage) {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.classList.remove('loading-state');
  content.innerHTML = `
    <div class="error-state">
      <div class="error-icon">⚠️</div>
      <div class="error-title">Error loading notifications</div>
      <div class="error-message">${escapeHtml(errorMessage)}</div>
      <button class="retry-button" onclick="refreshNotifications()">Retry</button>
    </div>
  `;
}

function displayAuthRequired() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.classList.remove('loading-state');
  content.innerHTML = `
    <div class="auth-required">
      <div class="auth-icon">🔐</div>
      <div class="auth-title">Authentication Required</div>
      <div class="auth-message">Please authenticate with Reddit to view your notifications.</div>
      <button class="auth-button" onclick="authenticateWithReddit()">Authenticate</button>
    </div>
  `;
}

function showMockDataIndicator() {
  if (!miniInboxElement) return;
  
  const header = miniInboxElement.querySelector('.mini-inbox-header .mini-inbox-title');
  if (header) {
    const indicator = document.createElement('span');
    indicator.className = 'mock-data-indicator';
    indicator.textContent = '[TEST DATA]';
    indicator.style.cssText = `
      background: #ff4500; color: white; padding: 2px 6px; border-radius: 3px;
      font-size: 9px; font-weight: bold; margin-left: 8px;
    `;
    header.appendChild(indicator);
  }
}

// Authentication function
async function authenticateWithReddit() {
  try {
    const response = await ErrorRecovery.safeChromeSendMessage({
      action: 'authenticate'
    });
    
    if (response && response.success) {
      console.log('Authentication successful');
      refreshNotifications();
    } else {
      console.error('Authentication failed:', response?.error);
    }
  } catch (error) {
    console.error('Authentication error:', error);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}

console.log('✅ Reddit Mini Inbox v2.1.0 FIXED - All corrections applied');