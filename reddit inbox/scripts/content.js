// Content script pour l'extension Reddit Mini Inbox
// Injecté dans les pages Reddit pour intercepter les interactions
// Version: 1.0.1 - Fixed syntax errors

console.log('Reddit Mini Inbox content script loaded');

// État de l'extension
let miniInboxOpen = false;
let miniInboxElement = null;

// Configuration des sélecteurs pour différentes versions de Reddit
let notificationSelectors = {
  icons: [],
  containers: [],
  version: null
};

// Observer pour les changements de DOM
let domObserver = null;

// Initialisation du content script
function init() {
  console.log('Initializing Reddit Mini Inbox...');
  
  // Attendre que le DOM soit prêt
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupNotificationInterception);
  } else {
    setupNotificationInterception();
  }
}

// Configuration de l'interception des notifications
function setupNotificationInterception() {
  console.log('Setting up notification interception...');
  
  // Détecter la version de Reddit et configurer les sélecteurs appropriés
  const redditVersion = detectRedditVersion();
  console.log('Detected Reddit version:', redditVersion);
  
  // Configurer les sélecteurs pour cette version
  setupNotificationSelectors(redditVersion);
  
  // Ajouter les event listeners
  document.addEventListener('click', handleClick, true);
  document.addEventListener('keydown', handleKeyPress);
  
  // Observer les changements de DOM pour les pages SPA
  setupDOMObserver();
}

// Gestionnaire de clics pour intercepter les notifications
function handleClick(event) {
  const target = event.target;
  
  // Debug: Log tous les clics pour voir ce qui est cliqué
  console.log('Click detected on:', target, target.tagName, target.className, target.getAttribute('aria-label'));
  
  // Vérifier si le clic est sur l'icône de notification
  if (isNotificationIcon(target)) {
    console.log('🎯 Notification icon clicked, intercepting...', target);
    event.preventDefault();
    event.stopPropagation();
    
    // Positionner la mini-inbox près de l'icône cliquée
    const iconPosition = getNotificationIconPosition(target);
    toggleMiniInbox(iconPosition);
    return false; // Empêcher complètement la navigation
  }
  
  // DÉTECTION TEMPORAIRE AGRESSIVE - pour tester
  // Intercepter tous les liens vers /message/inbox
  const href = target.getAttribute('href') || target.closest('a')?.getAttribute('href');
  if (href && href.includes('/message/inbox')) {
    console.log('🔧 TEMP: Intercepting inbox link for testing...', target);
    event.preventDefault();
    event.stopPropagation();
    
    const iconPosition = getNotificationIconPosition(target);
    toggleMiniInbox(iconPosition);
    return false;
  }
}

// Gestionnaire de touches clavier
function handleKeyPress(event) {
  if (event.key === 'Escape' && miniInboxOpen) {
    closeMiniInbox();
  }
}

// Toggle de la mini-inbox
function toggleMiniInbox(iconPosition = null) {
  if (miniInboxOpen) {
    closeMiniInbox();
  } else {
    openMiniInbox(iconPosition);
  }
}

// Ouverture de la mini-inbox
async function openMiniInbox(iconPosition = null) {
  console.log('Opening mini inbox...');
  
  if (miniInboxElement) {
    miniInboxElement.remove();
  }
  
  // Créer l'élément de la mini-inbox
  createMiniInboxElement(iconPosition);
  
  // Démarrer l'état de chargement
  setLoadingState(LoadingStates.LOADING, 'Chargement des notifications...');
  
  // Récupérer les notifications
  try {
    const response = await chrome.runtime.sendMessage({
      action: 'getNotifications',
      force: false
    });
    
    if (response && response.success) {
      if (response.notifications && response.notifications.length > 0) {
        displayNotifications(response.notifications);
      } else {
        displayNoNotifications();
      }
    } else {
      displayError(response.error || 'Erreur inconnue');
    }
  } catch (error) {
    console.error('Error getting notifications:', error);
    displayError('Erreur lors du chargement des notifications');
  }
  
  miniInboxOpen = true;
}

// Fermeture de la mini-inbox
function closeMiniInbox() {
  console.log('Closing mini inbox...');
  
  if (miniInboxElement) {
    const container = miniInboxElement.querySelector('.mini-inbox-container');
    
    // Déclencher l'animation de fermeture
    miniInboxElement.classList.remove('show');
    if (container) {
      container.classList.remove('opening', 'show');
      container.classList.add('closing');
    }
    
    // Supprimer l'élément après l'animation
    setTimeout(() => {
      if (miniInboxElement && miniInboxElement.parentNode) {
        miniInboxElement.remove();
      }
      miniInboxElement = null;
    }, 150);
  }
  
  miniInboxOpen = false;
}

// Création de l'élément DOM de la mini-inbox
function createMiniInboxElement(iconPosition = null) {
  miniInboxElement = document.createElement('div');
  miniInboxElement.id = 'reddit-mini-inbox-overlay';
  
  // Détecter le thème
  const isDarkTheme = detectDarkTheme();
  const themeClass = isDarkTheme ? 'dark-theme' : '';
  
  miniInboxElement.innerHTML = `
    <div class="mini-inbox-container opening ${themeClass}">
      <div class="mini-inbox-header">
        <div class="mini-inbox-title">
          <span class="mini-inbox-icon">[INBOX]</span>
          <span>inbox</span>
        </div>
        <div class="mini-inbox-actions">
          <button class="refresh-button" type="button" aria-label="Actualiser" title="Actualiser" onclick="refreshNotifications()">↻</button>
          <button class="close-btn" type="button" aria-label="Fermer" title="Fermer">×</button>
        </div>
      </div>
      <div class="mini-inbox-stats" style="display: none;">
        <span class="unread-count">0 non lues</span>
        <span class="total-count">0 total</span>
      </div>
      <div class="mini-inbox-content loading-state">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <div class="loading-text">Chargement des notifications...</div>
        </div>
      </div>
    </div>
  `;
  
  // Positionner la mini-inbox près de l'icône si la position est fournie
  if (iconPosition) {
    positionMiniInbox(miniInboxElement, iconPosition);
  }
  
  // Ajouter les event listeners
  const closeBtn = miniInboxElement.querySelector('.close-btn');
  closeBtn.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();
    closeMiniInbox();
  });
  
  // Fermer au clic en dehors (local)
  miniInboxElement.addEventListener('click', (event) => {
    if (event.target === miniInboxElement) {
      closeMiniInbox();
    }
  });
  
  // Support clavier local
  miniInboxElement.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      event.preventDefault();
      closeMiniInbox();
    }
  });
  
  // Ajouter les gestionnaires globaux pour une fermeture plus robuste
  setTimeout(() => {
    document.addEventListener('click', handleGlobalClick, true);
    document.addEventListener('keydown', handleGlobalKeydown, true);
  }, 100);
  
  // Fonction de nettoyage pour cet élément
  miniInboxElement.cleanup = function() {
    document.removeEventListener('click', handleGlobalClick, true);
    document.removeEventListener('keydown', handleGlobalKeydown, true);
    
    // Nettoyer les timers spécifiques à cet élément
    if (this.animationTimer) {
      clearTimeout(this.animationTimer);
    }
    if (this.positionTimer) {
      clearTimeout(this.positionTimer);
    }
  };
  
  // Ajouter au DOM
  document.body.appendChild(miniInboxElement);
  
  // Déclencher l'animation d'ouverture
  setTimeout(() => {
    miniInboxElement.classList.add('show');
    const container = miniInboxElement.querySelector('.mini-inbox-container');
    container.classList.add('show');
  }, 10);
}

// Affichage des notifications amélioré
function displayNotifications(notifications) {
  if (!miniInboxElement) return;
  
  setLoadingState(LoadingStates.SUCCESS);
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  
  // Mettre à jour les statistiques
  const unreadCount = notifications.filter(n => n.new).length;
  const totalCount = notifications.length;
  
  if (statsElement) {
    statsElement.style.display = 'flex';
    statsElement.querySelector('.unread-count').textContent = `${unreadCount} non lue${unreadCount !== 1 ? 's' : ''}`;
    statsElement.querySelector('.total-count').textContent = `${totalCount} total`;
  }
  
  // Mettre à jour le compteur de notifications dans l'icône Reddit
  updateRedditNotificationBadge(unreadCount);
  
  if (notifications.length === 0) {
    displayNoNotifications();
    return;
  }
  
  const notificationsHtml = notifications.map(notification => {
    const typeClass = notification.type ? notification.type.replace('_', '-') : 'message';
    const contextText = getNotificationContext(notification);
    const actionsHtml = getNotificationActions(notification);
    
    return `
      <div class="notification-item ${notification.new ? 'unread' : 'read'} ${typeClass}" 
           data-id="${notification.id}" 
           data-type="${notification.type || 'message'}"
           tabindex="0"
           role="button"
           aria-label="Notification de ${escapeHtml(notification.author)}">
        <div class="notification-header">
          <a href="/user/${escapeHtml(notification.author)}" class="notification-author" target="_blank">
            ${escapeHtml(notification.author)}
          </a>
          <span class="notification-time" title="${formatFullTime(notification.created_utc)}">
            ${formatTime(notification.created_utc)}
          </span>
        </div>
        <div class="notification-subject">${escapeHtml(notification.subject)}</div>
        ${contextText ? `<div class="notification-context">${contextText}</div>` : ''}
        <div class="notification-body">${formatNotificationBody(notification.body)}</div>
        <div class="notification-actions">
          ${actionsHtml}
        </div>
      </div>
    `;
  }).join('');
  
  content.innerHTML = notificationsHtml;
  
  // Ajouter les event listeners pour les notifications
  content.querySelectorAll('.notification-item').forEach((item, index) => {
    item.addEventListener('click', handleNotificationClick);
    item.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        handleNotificationClick(event);
      }
    });
    
    // Animation d'apparition échelonnée
    setTimeout(() => {
      item.classList.add('new-notification');
    }, index * 50);
  });
}

// Affichage d'erreur
function displayError(error) {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.innerHTML = `<div class="error">Erreur: ${escapeHtml(error)}</div>`;
}

// Gestionnaire de clic sur une notification
function handleNotificationClick(event) {
  const notificationItem = event.currentTarget;
  const notificationId = notificationItem.dataset.id;
  
  console.log('Notification clicked:', notificationId);
  
  // TODO: Ouvrir dans un nouvel onglet et marquer comme lu
  // Pour l'instant, juste marquer comme lu
  chrome.runtime.sendMessage({
    action: 'markAsRead',
    notificationId: notificationId
  });
  
  // Marquer visuellement comme lu
  notificationItem.classList.remove('unread');
  notificationItem.classList.add('read');
}

// Utilitaires
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function formatTime(timestamp) {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) return 'À l\'instant';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
  return `${Math.floor(diff / 86400000)}j`;
}

// Fonction pour attacher les event listeners aux boutons d'action
function attachActionListeners() {
  console.log('🔧 Attaching action listeners...');
  
  // Attacher aux boutons d'action
  document.querySelectorAll('.notification-action').forEach((action, index) => {
    console.log(`Attaching to action ${index}:`, action.textContent, action.getAttribute('data-action'));
    
    action.onclick = function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const actionType = this.getAttribute('data-action');
      const notificationId = this.getAttribute('data-id');
      const notificationItem = this.closest('.notification-item');
      
      console.log('🎯 Action clicked:', actionType, 'for notification:', notificationId);
      
      switch(actionType) {
        case 'reply':
          console.log('💬 Reply action');
          
          // Vérifier si interface de réponse existe déjà
          const existingReply = notificationItem.querySelector('.quick-reply-interface');
          if (existingReply) {
            existingReply.remove();
            return;
          }
          
          // Créer interface de réponse simple
          const replyDiv = document.createElement('div');
          replyDiv.className = 'quick-reply-interface';
          replyDiv.style.cssText = `
            background: #f8f9fa;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            margin: 8px 0;
            padding: 10px;
            animation: slideDown 0.2s ease-out;
          `;
          
          replyDiv.innerHTML = `
            <div style="background: #ff4500; color: white; padding: 6px 10px; margin: -10px -10px 10px -10px; border-radius: 4px 4px 0 0; display: flex; justify-content: space-between;">
              <span style="font-weight: bold;">Quick Reply</span>
              <button class="reply-close" style="background: none; border: none; color: white; cursor: pointer; font-size: 16px;">×</button>
            </div>
            <textarea placeholder="Write your reply..." rows="3" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-size: 11px; box-sizing: border-box; margin-bottom: 8px; resize: vertical;"></textarea>
            <div style="display: flex; gap: 6px; flex-wrap: wrap;">
              <button class="reply-send" style="background: #ff4500; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer; font-weight: bold;">Send Reply</button>
              <button class="reply-cancel" style="background: #f5f5f5; color: #666; border: 1px solid #ddd; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Cancel</button>
              <button class="reply-full" style="background: #0079d3; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Open Full Editor</button>
            </div>
          `;
          
          // Insérer après le body de la notification
          const notificationBody = notificationItem.querySelector('.notification-body');
          notificationBody.insertAdjacentElement('afterend', replyDiv);
          
          // Event listeners pour l'interface de réponse
          const textarea = replyDiv.querySelector('textarea');
          const closeBtn = replyDiv.querySelector('.reply-close');
          const cancelBtn = replyDiv.querySelector('.reply-cancel');
          const sendBtn = replyDiv.querySelector('.reply-send');
          const fullBtn = replyDiv.querySelector('.reply-full');
          
          closeBtn.onclick = cancelBtn.onclick = () => replyDiv.remove();
          
          sendBtn.onclick = () => {
            const text = textarea.value.trim();
            if (text) {
              // Simuler l'envoi
              replyDiv.innerHTML = `
                <div style="padding: 15px; text-align: center; background: #f0f8ff; border-radius: 4px;">
                  <div style="font-size: 24px; margin-bottom: 8px;">✅</div>
                  <div style="color: #0079d3; font-weight: bold; margin-bottom: 10px;">Reply sent successfully!</div>
                  <button onclick="this.closest('.quick-reply-interface').remove()" style="background: #0079d3; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Close</button>
                </div>
              `;
              setTimeout(() => replyDiv.remove(), 3000);
            } else {
              textarea.focus();
              textarea.style.borderColor = '#ff4500';
              setTimeout(() => textarea.style.borderColor = '', 2000);
            }
          };
          
          fullBtn.onclick = () => {
            const text = textarea.value.trim();
            let url = '/message/compose';
            if (text) {
              url += '?message=' + encodeURIComponent(text);
            }
            // Contourner les bloqueurs de pub
            const tempLink = document.createElement('a');
            tempLink.href = url;
            tempLink.target = '_blank';
            tempLink.rel = 'noopener noreferrer';
            document.body.appendChild(tempLink);
            tempLink.click();
            document.body.removeChild(tempLink);
            replyDiv.remove();
          };
          
          // Support clavier
          textarea.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
              event.preventDefault();
              sendBtn.click();
            } else if (event.key === 'Escape') {
              event.preventDefault();
              replyDiv.remove();
            }
          });
          
          textarea.focus();
          break;
          
        case 'mark-read':
          console.log('✅ Mark as read');
          notificationItem.classList.remove('unread');
          notificationItem.classList.add('read');
          this.textContent = 'mark as unread';
          this.setAttribute('data-action', 'mark-unread');
          
          // Synchroniser avec Reddit
          chrome.runtime.sendMessage({
            action: 'markAsRead',
            notificationId: notificationId
          });
          break;
          
        case 'mark-unread':
          console.log('👁️ Mark as unread');
          notificationItem.classList.remove('read');
          notificationItem.classList.add('unread');
          this.textContent = 'mark as read';
          this.setAttribute('data-action', 'mark-read');
          break;
      }
    };
  });
  
  // Attacher aux liens utilisateur
  document.querySelectorAll('.notification-author').forEach((link, index) => {
    console.log(`Attaching to user link ${index}:`, link.textContent);
    
    link.onclick = function(e) {
      e.stopPropagation();
      console.log('🎯 User link clicked:', this.textContent);
      // Le lien fonctionne normalement
    };
  });
  
  console.log('✅ Action listeners attached!');
}

// Rendre la fonction disponible globalement
window.attachActionListeners = attachActionListeners;

// Initialiser l'extension
init();

// === DÉTECTION DE L'ICÔNE DE NOTIFICATION REDDIT ===

// Détecter la version de Reddit basée sur les éléments DOM et l'URL
function detectRedditVersion() {
  const hostname = window.location.hostname;
  const pathname = window.location.pathname;
  
  // Vérifier old.reddit.com
  if (hostname === 'old.reddit.com') {
    return 'old';
  }
  
  // Vérifier www.reddit.com (nouveau design)
  if (hostname === 'www.reddit.com') {
    // Vérifier si c'est le nouveau design React
    if (document.querySelector('[data-testid]') || document.querySelector('shreddit-app')) {
      return 'new-react';
    }
    // Sinon c'est probablement le design intermédiaire
    return 'new';
  }
  
  // Par défaut, traiter comme nouveau Reddit
  return 'new';
}

// Configurer les sélecteurs de notification pour la version détectée
function setupNotificationSelectors(version) {
  notificationSelectors.version = version;
  
  switch (version) {
    case 'old':
      notificationSelectors.icons = [
        '#mail',
        '.mail',
        'a[href*="/message/inbox"]',
        'a[href="/message/inbox/"]'
      ];
      notificationSelectors.containers = [
        '#header-bottom-right',
        '.user'
      ];
      break;
      
    case 'new':
      notificationSelectors.icons = [
        '[aria-label*="notification"]',
        '[aria-label*="inbox"]',
        '[data-testid*="notification"]',
        '[data-testid*="inbox"]',
        'a[href*="/message/inbox"]',
        'button[aria-label*="notification"]',
        '.icon-notification',
        '.notification-icon'
      ];
      notificationSelectors.containers = [
        '[data-testid="header"]',
        'header',
        '.Header',
        '#SHORTCUT_FOCUSABLE_DIV'
      ];
      break;
      
    case 'new-react':
      notificationSelectors.icons = [
        'button[aria-label*="notification"]',
        'button[aria-label*="inbox"]',
        'button[aria-label*="Open inbox"]',
        '[data-testid="notification-bell"]',
        '[data-testid="inbox-button"]',
        'a[href*="/message/inbox"]',
        'shreddit-notification-bell',
        'reddit-header-action-button[aria-label*="notification"]'
      ];
      notificationSelectors.containers = [
        'shreddit-app',
        'reddit-header-large',
        'reddit-header-small',
        '[slot="header"]',
        'header'
      ];
      break;
      
    default:
      // Sélecteurs génériques comme fallback
      notificationSelectors.icons = [
        '[aria-label*="notification"]',
        '[aria-label*="inbox"]',
        'a[href*="/message/inbox"]',
        'button[aria-label*="notification"]'
      ];
      notificationSelectors.containers = [
        'header',
        '[data-testid="header"]'
      ];
  }
  
  console.log('Configured selectors for', version, ':', notificationSelectors);
}

// Vérifier si un élément est l'icône de notification
function isNotificationIcon(element) {
  if (!element) return false;
  
  // Vérifier l'élément lui-même et ses parents
  for (let el = element; el && el !== document; el = el.parentElement) {
    // Vérifier contre tous les sélecteurs d'icônes
    for (const selector of notificationSelectors.icons) {
      try {
        if (el.matches && el.matches(selector)) {
          console.log('Matched notification icon with selector:', selector);
          return true;
        }
      } catch (e) {
        // Ignorer les erreurs de sélecteur invalide
        continue;
      }
    }
    
    // Vérifications spécifiques par attributs et classes
    if (isNotificationIconByAttributes(el)) {
      return true;
    }
    
    // Vérifications spécifiques par contenu
    if (isNotificationIconByContent(el)) {
      return true;
    }
  }
  
  return false;
}

// Vérifier par attributs et classes spécifiques
function isNotificationIconByAttributes(element) {
  // Vérifier aria-label
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel) {
    const lowerLabel = ariaLabel.toLowerCase();
    if (lowerLabel.includes('notification') || 
        lowerLabel.includes('inbox') || 
        lowerLabel.includes('message') ||
        lowerLabel.includes('mail')) {
      return true;
    }
  }
  
  // Vérifier data-testid
  const testId = element.getAttribute('data-testid');
  if (testId) {
    const lowerTestId = testId.toLowerCase();
    if (lowerTestId.includes('notification') || 
        lowerTestId.includes('inbox') || 
        lowerTestId.includes('bell')) {
      return true;
    }
  }
  
  // Vérifier les classes CSS
  const className = element.className;
  if (typeof className === 'string') {
    const lowerClass = className.toLowerCase();
    if (lowerClass.includes('notification') || 
        lowerClass.includes('inbox') || 
        lowerClass.includes('mail') ||
        lowerClass.includes('bell')) {
      return true;
    }
  }
  
  // Vérifier l'ID
  const id = element.id;
  if (id) {
    const lowerId = id.toLowerCase();
    if (lowerId.includes('notification') || 
        lowerId.includes('inbox') || 
        lowerId.includes('mail')) {
      return true;
    }
  }
  
  // Vérifier href pour les liens
  const href = element.getAttribute('href');
  if (href && href.includes('/message/inbox')) {
    return true;
  }
  
  return false;
}

// Vérifier par contenu et icônes
function isNotificationIconByContent(element) {
  // Vérifier le texte du title/tooltip
  const title = element.getAttribute('title');
  if (title) {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('notification') || 
        lowerTitle.includes('inbox') || 
        lowerTitle.includes('message')) {
      return true;
    }
  }
  
  // Vérifier les icônes SVG
  const svg = element.querySelector('svg') || (element.tagName === 'SVG' ? element : null);
  if (svg) {
    // Vérifier les classes d'icône
    const svgClass = svg.getAttribute('class');
    if (svgClass && (svgClass.includes('bell') || svgClass.includes('notification') || svgClass.includes('mail'))) {
      return true;
    }
    
    // Vérifier les paths d'icône (forme de cloche, enveloppe, etc.)
    const paths = svg.querySelectorAll('path');
    for (const path of paths) {
      const d = path.getAttribute('d');
      if (d && (d.includes('bell') || isBellIconPath(d))) {
        return true;
      }
    }
  }
  
  // Vérifier les icônes font (FontAwesome, etc.)
  if (element.tagName === 'I') {
    const className = element.className;
    if (typeof className === 'string') {
      const lowerClass = className.toLowerCase();
      if (lowerClass.includes('bell') || 
          lowerClass.includes('notification') || 
          lowerClass.includes('envelope') ||
          lowerClass.includes('mail')) {
        return true;
      }
    }
  }
  
  return false;
}

// Vérifier si un path SVG ressemble à une icône de cloche
function isBellIconPath(pathData) {
  // Patterns communs pour les icônes de cloche en SVG
  const bellPatterns = [
    /M\s*\d+.*[Cc]\s*\d+.*[Cc]\s*\d+/,  // Pattern courbe typique des cloches
    /[Mm]\s*\d+.*[Ll]\s*\d+.*[Cc]/,      // Pattern avec courbes
    /bell/i,                              // Référence directe
    /notification/i                       // Référence directe
  ];
  
  return bellPatterns.some(pattern => pattern.test(pathData));
}

// Obtenir la position de l'icône de notification pour positionner la mini-inbox
function getNotificationIconPosition(element) {
  // Trouver l'élément icône le plus proche
  let iconElement = element;
  
  // Remonter jusqu'à trouver l'élément conteneur de l'icône
  while (iconElement && !isDirectNotificationIcon(iconElement)) {
    iconElement = iconElement.parentElement;
    if (!iconElement || iconElement === document.body) {
      iconElement = element; // Fallback à l'élément original
      break;
    }
  }
  
  const rect = iconElement.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  
  return {
    top: rect.bottom + scrollTop + 5, // 5px de marge
    left: rect.left + scrollLeft,
    right: rect.right + scrollLeft,
    width: rect.width,
    height: rect.height,
    element: iconElement
  };
}

// Vérifier si un élément est directement une icône de notification
function isDirectNotificationIcon(element) {
  return notificationSelectors.icons.some(selector => {
    try {
      return element.matches && element.matches(selector);
    } catch (e) {
      return false;
    }
  });
}

// Configurer l'observateur DOM pour détecter les changements dynamiques
function setupDOMObserver() {
  // Nettoyer l'observateur existant
  if (domObserver) {
    domObserver.disconnect();
  }
  
  // Créer un nouvel observateur
  domObserver = new MutationObserver((mutations) => {
    let shouldRecheck = false;
    
    mutations.forEach((mutation) => {
      // Vérifier si des nœuds ont été ajoutés
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        for (const node of mutation.addedNodes) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Vérifier si le nouveau nœud contient une icône de notification
            if (containsNotificationIcon(node)) {
              shouldRecheck = true;
              break;
            }
          }
        }
      }
      
      // Vérifier les changements d'attributs qui pourraient affecter la détection
      if (mutation.type === 'attributes') {
        const target = mutation.target;
        if (target.nodeType === Node.ELEMENT_NODE) {
          const relevantAttributes = ['class', 'aria-label', 'data-testid', 'href'];
          if (relevantAttributes.includes(mutation.attributeName)) {
            if (isNotificationIcon(target)) {
              shouldRecheck = true;
            }
          }
        }
      }
    });
    
    // Re-configurer les sélecteurs si nécessaire
    if (shouldRecheck) {
      console.log('DOM changes detected, rechecking notification selectors...');
      const newVersion = detectRedditVersion();
      if (newVersion !== notificationSelectors.version) {
        setupNotificationSelectors(newVersion);
      }
    }
  });
  
  // Observer les changements dans tout le document
  domObserver.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class', 'aria-label', 'data-testid', 'href']
  });
  
  console.log('DOM observer set up for notification icon detection');
}

// Vérifier si un élément contient une icône de notification
function containsNotificationIcon(element) {
  if (isNotificationIcon(element)) {
    return true;
  }
  
  // Vérifier les enfants
  for (const selector of notificationSelectors.icons) {
    try {
      if (element.querySelector && element.querySelector(selector)) {
        return true;
      }
    } catch (e) {
      continue;
    }
  }
  
  return false;
}

// Nettoyer les ressources lors du déchargement
window.addEventListener('beforeunload', () => {
  if (domObserver) {
    domObserver.disconnect();
  }
});

// Positionner la mini-inbox près de l'icône de notification
function positionMiniInbox(overlayElement, iconPosition) {
  const container = overlayElement.querySelector('.mini-inbox-container');
  if (!container) return;
  
  // Dimensions de la fenêtre
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  
  // Dimensions estimées de la mini-inbox (sera ajustée après rendu)
  const inboxWidth = 400;
  const inboxHeight = 500;
  
  // Position par défaut : en dessous et alignée à gauche de l'icône
  let top = iconPosition.top;
  let left = iconPosition.left;
  
  // Ajuster si la mini-inbox dépasse à droite
  if (left + inboxWidth > windowWidth) {
    left = iconPosition.right - inboxWidth;
  }
  
  // Ajuster si la mini-inbox dépasse en bas
  if (top + inboxHeight > windowHeight + scrollTop) {
    // Positionner au-dessus de l'icône
    top = iconPosition.top - iconPosition.height - inboxHeight - 10;
    
    // Si ça dépasse encore en haut, centrer verticalement
    if (top < scrollTop) {
      top = scrollTop + (windowHeight - inboxHeight) / 2;
    }
  }
  
  // S'assurer que la position n'est pas négative
  left = Math.max(10, left);
  top = Math.max(scrollTop + 10, top);
  
  // Appliquer les styles de positionnement
  overlayElement.style.position = 'absolute';
  overlayElement.style.top = '0';
  overlayElement.style.left = '0';
  overlayElement.style.width = '100%';
  overlayElement.style.height = '100%';
  overlayElement.style.backgroundColor = 'transparent';
  overlayElement.style.pointerEvents = 'none';
  
  // Positionner le conteneur
  container.style.position = 'absolute';
  container.style.top = `${top}px`;
  container.style.left = `${left}px`;
  container.style.pointerEvents = 'auto';
  container.style.zIndex = '10001';
  
  console.log(`Positioned mini-inbox at top: ${top}, left: ${left}`);
}

// Détecter le thème sombre
function detectDarkTheme() {
  // Vérifier les préférences système
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return true;
  }
  
  // Vérifier les classes CSS de Reddit pour le thème sombre
  const body = document.body;
  const html = document.documentElement;
  
  // Vérifications spécifiques à Reddit
  if (body.classList.contains('dark') || 
      body.classList.contains('dark-theme') ||
      html.classList.contains('dark') ||
      html.classList.contains('dark-theme')) {
    return true;
  }
  
  // Vérifier les attributs data
  if (body.getAttribute('data-theme') === 'dark' ||
      html.getAttribute('data-theme') === 'dark') {
    return true;
  }
  
  // Vérifier la couleur de fond pour détecter un thème sombre
  const bodyStyles = window.getComputedStyle(body);
  const backgroundColor = bodyStyles.backgroundColor;
  
  if (backgroundColor) {
    // Convertir la couleur en RGB et vérifier si elle est sombre
    const rgb = backgroundColor.match(/\d+/g);
    if (rgb && rgb.length >= 3) {
      const r = parseInt(rgb[0]);
      const g = parseInt(rgb[1]);
      const b = parseInt(rgb[2]);
      
      // Calculer la luminosité
      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
      
      // Si la luminosité est faible, c'est probablement un thème sombre
      if (luminance < 0.5) {
        return true;
      }
    }
  }
  
  return false;
}

// === FONCTIONS UTILITAIRES POUR L'AFFICHAGE DES NOTIFICATIONS ===

// Obtenir le contexte d'une notification (subreddit, post, etc.)
function getNotificationContext(notification) {
  if (!notification) return '';
  
  let context = '';
  
  // Pour les réponses de commentaires et mentions
  if (notification.type === 'comment_reply' || notification.type === 'username_mention') {
    if (notification.subreddit) {
      context = `dans <a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>`;
    }
    if (notification.link_title) {
      context += context ? ' • ' : '';
      context += `"${escapeHtml(notification.link_title)}"`;
    }
  }
  
  // Pour les réponses de posts
  if (notification.type === 'post_reply') {
    if (notification.subreddit) {
      context = `dans <a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>`;
    }
  }
  
  return context;
}

// Obtenir les actions disponibles pour une notification
function getNotificationActions(notification) {
  if (!notification) return '';
  
  let actions = [];
  
  // Action "Répondre" pour les commentaires et messages
  if (notification.type === 'comment_reply' || notification.type === 'username_mention' || notification.type === 'private_message') {
    actions.push(`<a href="#" class="notification-action" data-action="reply" data-id="${notification.id}">répondre</a>`);
  }
  
  // Action "Voir le contexte" pour les commentaires
  if (notification.permalink) {
    const contextUrl = notification.context || notification.permalink;
    actions.push(`<a href="${escapeHtml(contextUrl)}" class="notification-action" target="_blank">contexte</a>`);
  }
  
  // Action "Marquer comme lu/non lu"
  if (notification.new) {
    actions.push(`<a href="#" class="notification-action" data-action="mark-read" data-id="${notification.id}">marquer comme lu</a>`);
  } else {
    actions.push(`<a href="#" class="notification-action" data-action="mark-unread" data-id="${notification.id}">marquer comme non lu</a>`);
  }
  
  return actions.join(' • ');
}

// Formater le corps d'une notification avec support HTML limité
function formatNotificationBody(body) {
  if (!body) return '';
  
  // Nettoyer et limiter le HTML
  let cleanBody = escapeHtml(body);
  
  // Convertir les liens markdown en HTML
  cleanBody = cleanBody.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
  
  // Convertir les mentions d'utilisateurs
  cleanBody = cleanBody.replace(/\/?u\/([a-zA-Z0-9_-]+)/g, '<a href="/user/$1" target="_blank">u/$1</a>');
  
  // Convertir les mentions de subreddits
  cleanBody = cleanBody.replace(/\/?r\/([a-zA-Z0-9_-]+)/g, '<a href="/r/$1" target="_blank">r/$1</a>');
  
  // Convertir les retours à la ligne
  cleanBody = cleanBody.replace(/\n/g, '<br>');
  
  return cleanBody;
}

// Formater l'heure complète pour le tooltip
function formatFullTime(timestamp) {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// Améliorer la fonction de gestion des clics sur les notifications
function handleNotificationClick(event) {
  const notificationItem = event.currentTarget;
  const notificationId = notificationItem.dataset.id;
  const notificationType = notificationItem.dataset.type;
  
  // Vérifier si c'est un clic sur une action spécifique
  const target = event.target;
  if (target.classList.contains('notification-action')) {
    event.preventDefault();
    event.stopPropagation();
    
    const action = target.dataset.action;
    handleNotificationAction(action, notificationId, target);
    return;
  }
  
  // Vérifier si c'est un clic sur un lien
  if (target.tagName === 'A' && target.href) {
    // Laisser le lien s'ouvrir normalement
    return;
  }
  
  console.log('Notification clicked:', notificationId, notificationType);
  
  // Marquer comme lu si ce n'est pas déjà fait
  if (notificationItem.classList.contains('unread')) {
    markNotificationAsRead(notificationId, notificationItem);
  }
  
  // Ouvrir le contexte de la notification dans un nouvel onglet
  const contextLink = notificationItem.querySelector('a[href*="/r/"], a[href*="/user/"], a[href*="/comments/"]');
  if (contextLink && contextLink.href) {
    window.open(contextLink.href, '_blank');
  }
}

// Gérer les actions spécifiques sur les notifications
function handleNotificationAction(action, notificationId, element) {
  switch (action) {
    case 'mark-read':
      markNotificationAsRead(notificationId, element.closest('.notification-item'));
      break;
      
    case 'mark-unread':
      markNotificationAsUnread(notificationId, element.closest('.notification-item'));
      break;
      
    case 'reply':
      // TODO: Implémenter la réponse rapide
      console.log('Reply to notification:', notificationId);
      break;
      
    default:
      console.warn('Unknown notification action:', action);
  }
}

// Marquer une notification comme lue
function markNotificationAsRead(notificationId, notificationElement) {
  // Ajouter l'état de chargement
  notificationElement.classList.add('marking-read');
  setLoadingState(LoadingStates.MARKING_READ, 'Marquage comme lu...');
  
  chrome.runtime.sendMessage({
    action: 'markAsRead',
    notificationId: notificationId
  }).then(response => {
    if (response && response.success) {
      // Marquer visuellement comme lu
      notificationElement.classList.remove('unread', 'marking-read');
      notificationElement.classList.add('read');
      
      // Mettre à jour les statistiques
      updateNotificationStats();
    } else {
      // Supprimer l'état de chargement en cas d'erreur
      notificationElement.classList.remove('marking-read');
      console.error('Failed to mark notification as read');
    }
  }).catch(error => {
    notificationElement.classList.remove('marking-read');
    console.error('Error marking notification as read:', error);
  });
}

// Marquer une notification comme non lue
function markNotificationAsUnread(notificationId, notificationElement) {
  notificationElement.classList.add('marking-read');
  
  chrome.runtime.sendMessage({
    action: 'markAsUnread',
    notificationId: notificationId
  }).then(response => {
    if (response && response.success) {
      notificationElement.classList.remove('read', 'marking-read');
      notificationElement.classList.add('unread');
      updateNotificationStats();
    } else {
      notificationElement.classList.remove('marking-read');
      console.error('Failed to mark notification as unread');
    }
  }).catch(error => {
    notificationElement.classList.remove('marking-read');
    console.error('Error marking notification as unread:', error);
  });
}

// Mettre à jour les statistiques des notifications
function updateNotificationStats() {
  if (!miniInboxElement) return;
  
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  const notifications = miniInboxElement.querySelectorAll('.notification-item');
  const unreadNotifications = miniInboxElement.querySelectorAll('.notification-item.unread');
  
  const unreadCount = unreadNotifications.length;
  const totalCount = notifications.length;
  
  if (statsElement) {
    statsElement.querySelector('.unread-count').textContent = `${unreadCount} non lue${unreadCount !== 1 ? 's' : ''}`;
    statsElement.querySelector('.total-count').textContent = `${totalCount} total`;
  }
  
  // Mettre à jour le compteur dans l'icône Reddit
  updateRedditNotificationBadge(unreadCount);
}

// Mettre à jour le compteur de notifications dans l'icône Reddit
function updateRedditNotificationBadge(unreadCount) {
  console.log('Updating Reddit notification badge:', unreadCount);
  
  // Chercher l'icône de notification Reddit
  const notificationIcons = document.querySelectorAll([
    '[aria-label*="notification"]',
    '[aria-label*="inbox"]',
    '[data-testid*="notification"]',
    '[data-testid*="inbox"]',
    'a[href*="/message/inbox"]',
    '#mail',
    '.mail'
  ].join(', '));
  
  notificationIcons.forEach(icon => {
    updateIconBadge(icon, unreadCount);
  });
}

// Mettre à jour le badge d'une icône spécifique
function updateIconBadge(iconElement, count) {
  if (!iconElement) return;
  
  // Supprimer l'ancien badge s'il existe
  const existingBadge = iconElement.querySelector('.reddit-mini-inbox-badge');
  if (existingBadge) {
    existingBadge.remove();
  }
  
  // Ajouter un nouveau badge si il y a des notifications non lues
  if (count > 0) {
    const badge = document.createElement('span');
    badge.className = 'reddit-mini-inbox-badge';
    badge.textContent = count > 99 ? '99+' : count.toString();
    badge.style.cssText = `
      position: absolute;
      top: -8px;
      right: -8px;
      background: #ff4500;
      color: white;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      font-size: 10px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10002;
      border: 2px solid white;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    `;
    
    // S'assurer que l'icône parent a une position relative
    if (getComputedStyle(iconElement).position === 'static') {
      iconElement.style.position = 'relative';
    }
    
    iconElement.appendChild(badge);
  }
}

// Fonction pour rafraîchir les notifications
function refreshNotifications() {
  if (!miniInboxElement) return;
  
  setLoadingState(LoadingStates.REFRESHING, 'Actualisation des notifications...');
  
  const refreshButton = miniInboxElement.querySelector('.refresh-button');
  
  // Ajouter un indicateur de chargement
  if (refreshButton) {
    refreshButton.classList.add('loading');
    refreshButton.disabled = true;
  }
  
  chrome.runtime.sendMessage({
    action: 'getNotifications',
    force: true
  }).then(response => {
    if (response && response.success) {
      displayNotifications(response.notifications);
      console.log('Notifications refreshed successfully');
    } else {
      displayError(response.error);
    }
  }).catch(error => {
    console.error('Error refreshing notifications:', error);
    displayError('Erreur lors du rafraîchissement');
  }).finally(() => {
    if (refreshButton) {
      refreshButton.classList.remove('loading');
      refreshButton.disabled = false;
    }
  });
}

function handleNotificationAction(action, notificationId, element) {
  switch (action) {
    case 'mark-read':
      markNotificationAsRead(notificationId, element.closest('.notification-item'));
      break;
      
    case 'mark-unread':
      markNotificationAsUnread(notificationId, element.closest('.notification-item'));
      break;
      
    case 'reply':
      // TODO: Implémenter la réponse rapide
      console.log('Reply to notification:', notificationId);
      break;
      
    default:
      console.warn('Unknown notification action:', action);
  }
}

// Marquer une notification comme lue
function markNotificationAsRead(notificationId, notificationElement) {
  // Ajouter l'état de chargement
  notificationElement.classList.add('marking-read');
  
  chrome.runtime.sendMessage({
    action: 'markAsRead',
    notificationId: notificationId
  }).then(response => {
    if (response && response.success) {
      // Mettre à jour l'interface
      notificationElement.classList.remove('unread', 'marking-read');
      notificationElement.classList.add('read');
      
      // Mettre à jour les actions
      const actions = notificationElement.querySelector('.notification-actions');
      if (actions) {
        actions.innerHTML = actions.innerHTML.replace(
          'marquer comme lu',
          'marquer comme non lu'
        ).replace(
          'data-action="mark-read"',
          'data-action="mark-unread"'
        );
      }
      
      // Mettre à jour les statistiques
      updateNotificationStats();
    } else {
      // Supprimer l'état de chargement en cas d'erreur
      notificationElement.classList.remove('marking-read');
      console.error('Failed to mark notification as read');
    }
  }).catch(error => {
    notificationElement.classList.remove('marking-read');
    console.error('Error marking notification as read:', error);
  });
}

// Marquer une notification comme non lue
function markNotificationAsUnread(notificationId, notificationElement) {
  // TODO: Implémenter si l'API Reddit le supporte
  console.log('Mark as unread not implemented yet');
}

// Mettre à jour les statistiques de notifications
function updateNotificationStats() {
  if (!miniInboxElement) return;
  
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  const notifications = miniInboxElement.querySelectorAll('.notification-item');
  const unreadNotifications = miniInboxElement.querySelectorAll('.notification-item.unread');
  
  if (statsElement) {
    const unreadCount = unreadNotifications.length;
    const totalCount = notifications.length;
    
    statsElement.querySelector('.unread-count').textContent = `${unreadCount} non lue${unreadCount !== 1 ? 's' : ''}`;
    statsElement.querySelector('.total-count').textContent = `${totalCount} total`;
  }
}

// Améliorer la fonction displayError
// États de chargement et d'erreur améliorés
const LoadingStates = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  REFRESHING: 'refreshing',
  MARKING_READ: 'marking-read'
};

let currentLoadingState = LoadingStates.IDLE;

// Fonction pour changer l'état de chargement
function setLoadingState(state, message = null) {
  currentLoadingState = state;
  
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const header = miniInboxElement.querySelector('.mini-inbox-header');
  
  if (!content) return;
  
  // Supprimer tous les états précédents
  content.classList.remove('loading-state', 'error-state', 'success-state');
  
  switch (state) {
    case LoadingStates.LOADING:
      showLoadingIndicator(message || 'Chargement des notifications...');
      break;
    case LoadingStates.REFRESHING:
      showRefreshingIndicator(message || 'Actualisation...');
      break;
    case LoadingStates.ERROR:
      showErrorState(message || 'Une erreur est survenue');
      break;
    case LoadingStates.SUCCESS:
      hideLoadingIndicators();
      break;
  }
}

// Afficher l'indicateur de chargement
function showLoadingIndicator(message) {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.classList.add('loading-state');
  
  content.innerHTML = `
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">${escapeHtml(message)}</div>
      <div class="loading-progress">
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
      </div>
    </div>
  `;
  
  // Animer la barre de progression
  setTimeout(() => {
    const progressFill = content.querySelector('.progress-fill');
    if (progressFill) {
      progressFill.style.width = '70%';
    }
  }, 500);
}

// Afficher l'indicateur de rafraîchissement
function showRefreshingIndicator(message) {
  if (!miniInboxElement) return;
  
  const header = miniInboxElement.querySelector('.mini-inbox-header');
  const refreshBtn = header.querySelector('.refresh-button');
  
  if (refreshBtn) {
    refreshBtn.classList.add('loading');
    refreshBtn.disabled = true;
    refreshBtn.innerHTML = '⟳';
  }
  
  // Ajouter un indicateur subtil dans le header
  let refreshIndicator = header.querySelector('.refresh-indicator');
  if (!refreshIndicator) {
    refreshIndicator = document.createElement('span');
    refreshIndicator.className = 'refresh-indicator';
    header.appendChild(refreshIndicator);
  }
  
  refreshIndicator.textContent = message;
  refreshIndicator.style.display = 'inline';
}

// Masquer les indicateurs de chargement
function hideLoadingIndicators() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const header = miniInboxElement.querySelector('.mini-inbox-header');
  
  content.classList.remove('loading-state');
  
  const refreshBtn = header.querySelector('.refresh-button');
  if (refreshBtn) {
    refreshBtn.classList.remove('loading');
    refreshBtn.disabled = false;
    refreshBtn.innerHTML = '↻';
  }
  
  const refreshIndicator = header.querySelector('.refresh-indicator');
  if (refreshIndicator) {
    refreshIndicator.style.display = 'none';
  }
}

// Fonction displayError améliorée
function displayError(error, canRetry = true) {
  if (!miniInboxElement) return;
  
  setLoadingState(LoadingStates.ERROR);
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  
  // Masquer les statistiques en cas d'erreur
  if (statsElement) {
    statsElement.style.display = 'none';
  }
  
  content.classList.add('error-state');
  
  // Déterminer le type d'erreur et le message approprié
  let errorMessage = 'Une erreur est survenue';
  let errorDetails = '';
  let retryAction = 'Réessayer';
  
  if (typeof error === 'string') {
    if (error.includes('network') || error.includes('fetch')) {
      errorMessage = 'Problème de connexion';
      errorDetails = 'Vérifiez votre connexion internet et réessayez.';
    } else if (error.includes('auth') || error.includes('401')) {
      errorMessage = 'Authentification requise';
      errorDetails = 'Vous devez vous reconnecter à Reddit.';
      retryAction = 'Se connecter';
    } else if (error.includes('rate') || error.includes('429')) {
      errorMessage = 'Trop de requêtes';
      errorDetails = 'Attendez quelques instants avant de réessayer.';
    } else {
      errorDetails = error;
    }
  }
  
  content.innerHTML = `
    <div class="error">
      <div class="error-icon">⚠️</div>
      <div class="error-title">${errorMessage}</div>
      <div class="error-details">${escapeHtml(errorDetails)}</div>
      <div class="error-actions">
        ${canRetry ? `<button class="retry-button" onclick="retryLoadNotifications()">${retryAction}</button>` : ''}
        <button class="close-error-button" onclick="closeMiniInbox()">Fermer</button>
      </div>
      <div class="error-timestamp">
        Erreur survenue à ${new Date().toLocaleTimeString()}
      </div>
    </div>
  `;
}

// Fonction pour réessayer le chargement
function retryLoadNotifications() {
  setLoadingState(LoadingStates.LOADING, 'Nouvelle tentative...');
  
  setTimeout(async () => {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getNotifications',
        force: true
      });
      
      if (response.success) {
        setLoadingState(LoadingStates.SUCCESS);
        displayNotifications(response.notifications);
      } else {
        displayError(response.error);
      }
    } catch (error) {
      console.error('Retry failed:', error);
      displayError('Échec de la nouvelle tentative');
    }
  }, 1000);
}

// Afficher le message "Aucune notification"
function displayNoNotifications() {
  if (!miniInboxElement) return;
  
  setLoadingState(LoadingStates.SUCCESS);
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.innerHTML = `
    <div class="no-notifications">
      <div class="no-notifications-icon">[EMPTY]</div>
      <div class="no-notifications-title">Aucune notification</div>
      <div class="no-notifications-subtitle">
        Vous êtes à jour ! Revenez plus tard pour voir vos nouvelles notifications.
      </div>
      <button class="refresh-notifications-button" onclick="refreshNotifications()">
        Actualiser
      </button>
    </div>
  `;
}

// === RENDU SPÉCIALISÉ DES NOTIFICATIONS ===

// Rendu spécialisé pour différents types de notifications
function renderNotificationByType(notification) {
  const baseHtml = {
    id: notification.id,
    type: notification.type || 'message',
    isUnread: notification.new,
    author: notification.author,
    time: notification.created_utc,
    subject: notification.subject,
    body: notification.body
  };
  
  switch (notification.type) {
    case 'comment_reply':
      return renderCommentReply(notification, baseHtml);
    case 'username_mention':
      return renderUsernameMention(notification, baseHtml);
    case 'private_message':
      return renderPrivateMessage(notification, baseHtml);
    case 'post_reply':
      return renderPostReply(notification, baseHtml);
    default:
      return renderGenericNotification(notification, baseHtml);
  }
}

// Rendu pour les réponses de commentaires
function renderCommentReply(notification, base) {
  const subredditLink = notification.subreddit ? 
    `<a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>` : '';
  
  const postTitle = notification.link_title ? 
    `<span class="post-title">"${escapeHtml(notification.link_title)}"</span>` : '';
  
  const contextUrl = notification.context || notification.permalink;
  
  return {
    ...base,
    icon: '💬',
    contextHtml: subredditLink && postTitle ? `${subredditLink} • ${postTitle}` : (subredditLink || postTitle),
    actions: [
      { text: 'répondre', action: 'reply', id: notification.id },
      { text: 'contexte', url: contextUrl, external: true },
      { text: base.isUnread ? 'marquer comme lu' : 'marquer comme non lu', 
        action: base.isUnread ? 'mark-read' : 'mark-unread', id: notification.id }
    ]
  };
}

// Rendu pour les mentions d'utilisateur
function renderUsernameMention(notification, base) {
  const subredditLink = notification.subreddit ? 
    `<a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>` : '';
  
  const contextUrl = notification.context || notification.permalink;
  
  return {
    ...base,
    icon: '@',
    contextHtml: subredditLink ? `mentionné dans ${subredditLink}` : 'mentionné',
    actions: [
      { text: 'répondre', action: 'reply', id: notification.id },
      { text: 'voir', url: contextUrl, external: true },
      { text: base.isUnread ? 'marquer comme lu' : 'marquer comme non lu', 
        action: base.isUnread ? 'mark-read' : 'mark-unread', id: notification.id }
    ]
  };
}

// Rendu pour les messages privés
function renderPrivateMessage(notification, base) {
  return {
    ...base,
    icon: '✉️',
    contextHtml: 'message privé',
    actions: [
      { text: 'répondre', action: 'reply', id: notification.id },
      { text: 'voir conversation', url: `/message/messages/${notification.id}`, external: true },
      { text: base.isUnread ? 'marquer comme lu' : 'marquer comme non lu', 
        action: base.isUnread ? 'mark-read' : 'mark-unread', id: notification.id }
    ]
  };
}

// Rendu pour les réponses de posts
function renderPostReply(notification, base) {
  const subredditLink = notification.subreddit ? 
    `<a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>` : '';
  
  const postTitle = notification.link_title ? 
    `<span class="post-title">"${escapeHtml(notification.link_title)}"</span>` : '';
  
  const contextUrl = notification.context || notification.permalink;
  
  return {
    ...base,
    icon: '📝',
    contextHtml: subredditLink && postTitle ? `${subredditLink} • ${postTitle}` : (subredditLink || postTitle),
    actions: [
      { text: 'répondre', action: 'reply', id: notification.id },
      { text: 'voir post', url: contextUrl, external: true },
      { text: base.isUnread ? 'marquer comme lu' : 'marquer comme non lu', 
        action: base.isUnread ? 'mark-read' : 'mark-unread', id: notification.id }
    ]
  };
}

// Rendu générique pour les types inconnus
function renderGenericNotification(notification, base) {
  return {
    ...base,
    icon: '[INBOX]',
    contextHtml: notification.subreddit ? 
      `<a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>` : '',
    actions: [
      { text: 'voir', url: notification.permalink || notification.context, external: true },
      { text: base.isUnread ? 'marquer comme lu' : 'marquer comme non lu', 
        action: base.isUnread ? 'mark-read' : 'mark-unread', id: notification.id }
    ]
  };
}

// Générer le HTML final à partir des données de rendu
function generateNotificationHtml(renderData) {
  const typeClass = renderData.type.replace('_', '-');
  const statusClass = renderData.isUnread ? 'unread' : 'read';
  
  const actionsHtml = renderData.actions.map(action => {
    if (action.url) {
      return `<a href="${escapeHtml(action.url)}" class="notification-action" target="_blank">${action.text}</a>`;
    } else {
      return `<a href="#" class="notification-action" data-action="${action.action}" data-id="${action.id}">${action.text}</a>`;
    }
  }).join(' • ');
  
  return `
    <div class="notification-item ${statusClass} ${typeClass}" 
         data-id="${renderData.id}" 
         data-type="${renderData.type}"
         tabindex="0"
         role="button"
         aria-label="Notification de ${escapeHtml(renderData.author)}">
      <div class="notification-header">
        <a href="/user/${escapeHtml(renderData.author)}" class="notification-author" target="_blank">
          ${escapeHtml(renderData.author)}
        </a>
        <span class="notification-time" title="${formatFullTime(renderData.time)}">
          ${formatTime(renderData.time)}
        </span>
      </div>
      <div class="notification-subject">${escapeHtml(renderData.subject)}</div>
      ${renderData.contextHtml ? `<div class="notification-context">${renderData.contextHtml}</div>` : ''}
      <div class="notification-body">${formatNotificationBody(renderData.body)}</div>
      <div class="notification-actions">
        ${actionsHtml}
      </div>
    </div>
  `;
}

// Fonction améliorée pour afficher les notifications avec rendu spécialisé
function displayNotificationsWithSpecializedRendering(notifications) {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  
  // Supprimer l'état de chargement
  content.classList.remove('loading-state');
  
  // Mettre à jour les statistiques
  const unreadCount = notifications.filter(n => n.new).length;
  const totalCount = notifications.length;
  
  if (statsElement) {
    statsElement.style.display = 'flex';
    statsElement.querySelector('.unread-count').textContent = `${unreadCount} non lue${unreadCount !== 1 ? 's' : ''}`;
    statsElement.querySelector('.total-count').textContent = `${totalCount} total`;
  }
  
  if (notifications.length === 0) {
    content.innerHTML = '<div class="no-notifications">[EMPTY] Aucune notification</div>';
    return;
  }
  
  // Grouper les notifications par type pour un meilleur affichage
  const groupedNotifications = groupNotificationsByType(notifications);
  
  let notificationsHtml = '';
  
  // Afficher les notifications non lues en premier
  const unreadNotifications = notifications.filter(n => n.new);
  const readNotifications = notifications.filter(n => !n.new);
  
  if (unreadNotifications.length > 0) {
    notificationsHtml += unreadNotifications.map(notification => {
      const renderData = renderNotificationByType(notification);
      return generateNotificationHtml(renderData);
    }).join('');
  }
  
  if (readNotifications.length > 0) {
    if (unreadNotifications.length > 0) {
      notificationsHtml += '<div class="notifications-separator">Messages lus</div>';
    }
    
    notificationsHtml += readNotifications.map(notification => {
      const renderData = renderNotificationByType(notification);
      return generateNotificationHtml(renderData);
    }).join('');
  }
  
  content.innerHTML = notificationsHtml;
  
  // Ajouter les event listeners pour les notifications
  content.querySelectorAll('.notification-item').forEach((item, index) => {
    item.addEventListener('click', handleNotificationClick);
    item.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        handleNotificationClick(event);
      }
    });
    
    // Animation d'apparition échelonnée
    setTimeout(() => {
      item.classList.add('new-notification');
    }, index * 30);
  });
}

// Grouper les notifications par type
function groupNotificationsByType(notifications) {
  const groups = {
    'comment_reply': [],
    'username_mention': [],
    'private_message': [],
    'post_reply': [],
    'other': []
  };
  
  notifications.forEach(notification => {
    const type = notification.type || 'other';
    if (groups[type]) {
      groups[type].push(notification);
    } else {
      groups.other.push(notification);
    }
  });
  
  return groups;
}

// Fonction pour tronquer le texte intelligemment
function truncateText(text, maxLength = 150) {
  if (!text || text.length <= maxLength) return text;
  
  // Trouver le dernier espace avant la limite
  const truncated = text.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + '...';
  }
  
  return truncated + '...';
}

// Fonction pour détecter et formater les liens dans le texte
function formatLinksInText(text) {
  if (!text) return '';
  
  // URLs
  text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
  
  // Mentions Reddit
  text = text.replace(/\/?u\/([a-zA-Z0-9_-]+)/g, '<a href="/user/$1" target="_blank">u/$1</a>');
  text = text.replace(/\/?r\/([a-zA-Z0-9_-]+)/g, '<a href="/r/$1" target="_blank">r/$1</a>');
  
  return text;
}

// Remplacer la fonction displayNotifications existante
// (Cette fonction sera appelée à la place de l'ancienne)
const originalDisplayNotifications = displayNotifications;
displayNotifications = displayNotificationsWithSpecializedRendering;

// === INTERACTIONS UTILISATEUR AVANCÉES ===

// Gestionnaire d'événements amélioré pour les interactions utilisateur
function setupAdvancedUserInteractions() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const container = miniInboxElement.querySelector('.mini-inbox-container');
  
  // Gestion du scroll avec indicateurs visuels
  setupScrollIndicators(content);
  
  // Gestion des raccourcis clavier
  setupKeyboardShortcuts(container);
  
  // Gestion du drag and drop (pour réorganiser les notifications)
  setupDragAndDrop(content);
  
  // Gestion des gestes tactiles pour mobile
  setupTouchGestures(container);
  
  // Gestion de la sélection multiple
  setupMultiSelection(content);
  
  // Auto-refresh des notifications
  setupAutoRefresh();
}

// Configuration des indicateurs de scroll
function setupScrollIndicators(content) {
  if (!content) return;
  
  const scrollIndicatorTop = document.createElement('div');
  scrollIndicatorTop.className = 'scroll-indicator scroll-indicator-top';
  scrollIndicatorTop.innerHTML = '▲';
  
  const scrollIndicatorBottom = document.createElement('div');
  scrollIndicatorBottom.className = 'scroll-indicator scroll-indicator-bottom';
  scrollIndicatorBottom.innerHTML = '▼';
  
  content.parentElement.appendChild(scrollIndicatorTop);
  content.parentElement.appendChild(scrollIndicatorBottom);
  
  // Gestionnaire de scroll
  content.addEventListener('scroll', () => {
    const { scrollTop, scrollHeight, clientHeight } = content;
    
    // Indicateur du haut
    scrollIndicatorTop.style.opacity = scrollTop > 20 ? '1' : '0';
    
    // Indicateur du bas
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 20;
    scrollIndicatorBottom.style.opacity = isAtBottom ? '0' : '1';
    
    // Marquer les notifications visibles comme vues
    markVisibleNotificationsAsViewed(content);
  });
  
  // Clic sur les indicateurs pour scroll
  scrollIndicatorTop.addEventListener('click', () => {
    content.scrollTo({ top: 0, behavior: 'smooth' });
  });
  
  scrollIndicatorBottom.addEventListener('click', () => {
    content.scrollTo({ top: content.scrollHeight, behavior: 'smooth' });
  });
}

// Configuration des raccourcis clavier
function setupKeyboardShortcuts(container) {
  if (!container) return;
  
  container.addEventListener('keydown', (event) => {
    // Empêcher la propagation pour éviter les conflits avec Reddit
    event.stopPropagation();
    
    switch (event.key) {
      case 'Escape':
        closeMiniInbox();
        break;
        
      case 'r':
      case 'R':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          refreshNotifications();
        }
        break;
        
      case 'a':
      case 'A':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          selectAllNotifications();
        }
        break;
        
      case 'ArrowDown':
        event.preventDefault();
        navigateNotifications('down');
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        navigateNotifications('up');
        break;
        
      case 'Enter':
      case ' ':
        event.preventDefault();
        activateSelectedNotification();
        break;
        
      case 'Delete':
      case 'Backspace':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          markSelectedNotificationsAsRead();
        }
        break;
    }
  });
}

// Navigation au clavier entre les notifications
let currentNotificationIndex = -1;

function navigateNotifications(direction) {
  const notifications = miniInboxElement.querySelectorAll('.notification-item');
  if (notifications.length === 0) return;
  
  // Supprimer la sélection précédente
  if (currentNotificationIndex >= 0 && notifications[currentNotificationIndex]) {
    notifications[currentNotificationIndex].classList.remove('keyboard-selected');
  }
  
  // Calculer le nouvel index
  if (direction === 'down') {
    currentNotificationIndex = Math.min(currentNotificationIndex + 1, notifications.length - 1);
  } else {
    currentNotificationIndex = Math.max(currentNotificationIndex - 1, 0);
  }
  
  // Sélectionner la nouvelle notification
  const selectedNotification = notifications[currentNotificationIndex];
  selectedNotification.classList.add('keyboard-selected');
  selectedNotification.focus();
  
  // Scroll pour garder la notification visible
  selectedNotification.scrollIntoView({ 
    behavior: 'smooth', 
    block: 'nearest' 
  });
}

// Activer la notification sélectionnée
function activateSelectedNotification() {
  const selectedNotification = miniInboxElement.querySelector('.notification-item.keyboard-selected');
  if (selectedNotification) {
    selectedNotification.click();
  }
}

// Sélectionner toutes les notifications
function selectAllNotifications() {
  const notifications = miniInboxElement.querySelectorAll('.notification-item');
  notifications.forEach(notification => {
    notification.classList.add('multi-selected');
  });
  
  updateSelectionCounter();
}

// Marquer les notifications sélectionnées comme lues
function markSelectedNotificationsAsRead() {
  const selectedNotifications = miniInboxElement.querySelectorAll('.notification-item.multi-selected');
  
  selectedNotifications.forEach(notification => {
    if (notification.classList.contains('unread')) {
      const notificationId = notification.dataset.id;
      markNotificationAsRead(notificationId, notification);
    }
  });
  
  // Désélectionner après action
  clearSelection();
}

// Configuration du drag and drop
function setupDragAndDrop(content) {
  if (!content) return;
  
  let draggedElement = null;
  
  content.addEventListener('dragstart', (event) => {
    if (event.target.classList.contains('notification-item')) {
      draggedElement = event.target;
      event.target.classList.add('dragging');
      event.dataTransfer.effectAllowed = 'move';
    }
  });
  
  content.addEventListener('dragover', (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    
    const afterElement = getDragAfterElement(content, event.clientY);
    if (afterElement == null) {
      content.appendChild(draggedElement);
    } else {
      content.insertBefore(draggedElement, afterElement);
    }
  });
  
  content.addEventListener('dragend', (event) => {
    if (event.target.classList.contains('notification-item')) {
      event.target.classList.remove('dragging');
      draggedElement = null;
    }
  });
}

// Trouver l'élément après lequel insérer lors du drag
function getDragAfterElement(container, y) {
  const draggableElements = [...container.querySelectorAll('.notification-item:not(.dragging)')];
  
  return draggableElements.reduce((closest, child) => {
    const box = child.getBoundingClientRect();
    const offset = y - box.top - box.height / 2;
    
    if (offset < 0 && offset > closest.offset) {
      return { offset: offset, element: child };
    } else {
      return closest;
    }
  }, { offset: Number.NEGATIVE_INFINITY }).element;
}

// Configuration des gestes tactiles
function setupTouchGestures(container) {
  if (!container) return;
  
  let touchStartX = 0;
  let touchStartY = 0;
  let touchStartTime = 0;
  
  container.addEventListener('touchstart', (event) => {
    const touch = event.touches[0];
    touchStartX = touch.clientX;
    touchStartY = touch.clientY;
    touchStartTime = Date.now();
  });
  
  container.addEventListener('touchend', (event) => {
    const touch = event.changedTouches[0];
    const touchEndX = touch.clientX;
    const touchEndY = touch.clientY;
    const touchEndTime = Date.now();
    
    const deltaX = touchEndX - touchStartX;
    const deltaY = touchEndY - touchStartY;
    const deltaTime = touchEndTime - touchStartTime;
    
    // Swipe vers la droite pour fermer
    if (deltaX > 100 && Math.abs(deltaY) < 50 && deltaTime < 300) {
      closeMiniInbox();
    }
    
    // Swipe vers le bas pour rafraîchir
    if (deltaY > 100 && Math.abs(deltaX) < 50 && deltaTime < 300) {
      const content = container.querySelector('.mini-inbox-content');
      if (content && content.scrollTop === 0) {
        refreshNotifications();
      }
    }
  });
}

// Configuration de la sélection multiple
function setupMultiSelection(content) {
  if (!content) return;
  
  let isMultiSelectMode = false;
  
  content.addEventListener('click', (event) => {
    const notification = event.target.closest('.notification-item');
    if (!notification) return;
    
    // Ctrl/Cmd + clic pour sélection multiple
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      event.stopPropagation();
      
      notification.classList.toggle('multi-selected');
      isMultiSelectMode = true;
      updateSelectionCounter();
      return;
    }
    
    // Shift + clic pour sélection de plage
    if (event.shiftKey && isMultiSelectMode) {
      event.preventDefault();
      event.stopPropagation();
      
      selectNotificationRange(notification);
      updateSelectionCounter();
      return;
    }
    
    // Clic normal - désélectionner tout
    if (isMultiSelectMode) {
      clearSelection();
      isMultiSelectMode = false;
    }
  });
}

// Sélectionner une plage de notifications
function selectNotificationRange(endNotification) {
  const notifications = [...miniInboxElement.querySelectorAll('.notification-item')];
  const selectedNotifications = [...miniInboxElement.querySelectorAll('.notification-item.multi-selected')];
  
  if (selectedNotifications.length === 0) {
    endNotification.classList.add('multi-selected');
    return;
  }
  
  const lastSelected = selectedNotifications[selectedNotifications.length - 1];
  const startIndex = notifications.indexOf(lastSelected);
  const endIndex = notifications.indexOf(endNotification);
  
  const minIndex = Math.min(startIndex, endIndex);
  const maxIndex = Math.max(startIndex, endIndex);
  
  for (let i = minIndex; i <= maxIndex; i++) {
    notifications[i].classList.add('multi-selected');
  }
}

// Mettre à jour le compteur de sélection
function updateSelectionCounter() {
  const selectedCount = miniInboxElement.querySelectorAll('.notification-item.multi-selected').length;
  
  let selectionCounter = miniInboxElement.querySelector('.selection-counter');
  
  if (selectedCount > 0) {
    if (!selectionCounter) {
      selectionCounter = document.createElement('div');
      selectionCounter.className = 'selection-counter';
      miniInboxElement.querySelector('.mini-inbox-header').appendChild(selectionCounter);
    }
    
    selectionCounter.textContent = `${selectedCount} sélectionnée${selectedCount > 1 ? 's' : ''}`;
    selectionCounter.style.display = 'block';
  } else if (selectionCounter) {
    selectionCounter.style.display = 'none';
  }
}

// Effacer la sélection
function clearSelection() {
  const selectedNotifications = miniInboxElement.querySelectorAll('.notification-item.multi-selected');
  selectedNotifications.forEach(notification => {
    notification.classList.remove('multi-selected');
  });
  
  updateSelectionCounter();
}

// Marquer les notifications visibles comme vues
function markVisibleNotificationsAsViewed(content) {
  const notifications = content.querySelectorAll('.notification-item');
  const contentRect = content.getBoundingClientRect();
  
  notifications.forEach(notification => {
    const notificationRect = notification.getBoundingClientRect();
    
    // Vérifier si la notification est visible
    if (notificationRect.top >= contentRect.top && 
        notificationRect.bottom <= contentRect.bottom) {
      
      // Marquer comme vue après 2 secondes de visibilité
      if (!notification.dataset.viewTimer) {
        notification.dataset.viewTimer = setTimeout(() => {
          notification.classList.add('viewed');
          delete notification.dataset.viewTimer;
        }, 2000);
      }
    } else {
      // Annuler le timer si la notification n'est plus visible
      if (notification.dataset.viewTimer) {
        clearTimeout(notification.dataset.viewTimer);
        delete notification.dataset.viewTimer;
      }
    }
  });
}

// Rafraîchir les notifications
async function refreshNotifications() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const refreshButton = miniInboxElement.querySelector('.refresh-button');
  
  // Ajouter un indicateur de chargement
  if (refreshButton) {
    refreshButton.classList.add('loading');
    refreshButton.disabled = true;
  }
  
  try {
    const response = await chrome.runtime.sendMessage({
      action: 'getNotifications',
      force: true // Forcer le rafraîchissement
    });
    
    if (response.success) {
      displayNotifications(response.notifications);
      
      // Animation de rafraîchissement
      content.style.opacity = '0.5';
      setTimeout(() => {
        content.style.opacity = '1';
      }, 200);
      
      console.log('Notifications refreshed successfully');
    } else {
      displayError(response.error);
    }
  } catch (error) {
    console.error('Error refreshing notifications:', error);
    displayError('Erreur lors du rafraîchissement');
  } finally {
    if (refreshButton) {
      refreshButton.classList.remove('loading');
      refreshButton.disabled = false;
    }
  }
}

// Configuration de l'auto-refresh
function setupAutoRefresh() {
  // Rafraîchir automatiquement toutes les 5 minutes
  const autoRefreshInterval = setInterval(() => {
    if (miniInboxOpen && miniInboxElement) {
      refreshNotifications();
    } else {
      clearInterval(autoRefreshInterval);
    }
  }, 5 * 60 * 1000);
  
  // Rafraîchir quand la fenêtre reprend le focus
  const handleFocus = () => {
    if (miniInboxOpen && miniInboxElement) {
      refreshNotifications();
    }
  };
  
  window.addEventListener('focus', handleFocus);
  
  // Nettoyer les event listeners
  const cleanup = () => {
    clearInterval(autoRefreshInterval);
    window.removeEventListener('focus', handleFocus);
  };
  
  // Stocker la fonction de nettoyage pour l'utiliser lors de la fermeture
  if (miniInboxElement) {
    miniInboxElement.cleanup = cleanup;
  }
}

// Améliorer la fonction d'ouverture des notifications dans de nouveaux onglets
function openNotificationInNewTab(notification) {
  const notificationElement = notification.target ? notification.target.closest('.notification-item') : notification;
  const notificationId = notificationElement.dataset.id;
  const notificationType = notificationElement.dataset.type;
  
  // Construire l'URL appropriée selon le type de notification
  let url = '';
  
  // Récupérer les données de la notification depuis le DOM
  const contextLink = notificationElement.querySelector('.notification-context a');
  const authorLink = notificationElement.querySelector('.notification-author');
  
  switch (notificationType) {
    case 'comment_reply':
    case 'username_mention':
    case 'post_reply':
      // Utiliser le lien de contexte s'il existe
      if (contextLink && contextLink.href) {
        url = contextLink.href;
      }
      break;
      
    case 'private_message':
      // Ouvrir la conversation de message privé
      url = `/message/messages/${notificationId}`;
      break;
      
    default:
      // Fallback vers le profil de l'auteur
      if (authorLink && authorLink.href) {
        url = authorLink.href;
      }
  }
  
  if (url) {
    // Ouvrir dans un nouvel onglet
    window.open(url, '_blank', 'noopener,noreferrer');
    
    // Marquer comme lu si ce n'est pas déjà fait
    if (notificationElement.classList.contains('unread')) {
      markNotificationAsRead(notificationId, notificationElement);
    }
    
    console.log(`Opened notification ${notificationId} in new tab: ${url}`);
  } else {
    console.warn('Could not determine URL for notification:', notificationId);
  }
}

// Améliorer la gestion du scroll avec momentum sur mobile
function enhanceScrolling(content) {
  if (!content) return;
  
  // Ajouter le momentum scrolling sur iOS
  content.style.webkitOverflowScrolling = 'touch';
  
  // Smooth scrolling pour tous les navigateurs
  content.style.scrollBehavior = 'smooth';
  
  // Gérer le scroll infini (charger plus de notifications)
  content.addEventListener('scroll', debounce(() => {
    const { scrollTop, scrollHeight, clientHeight } = content;
    
    // Si on est proche du bas, charger plus de notifications
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      loadMoreNotifications();
    }
  }, 250));
}

// Charger plus de notifications (pagination)
async function loadMoreNotifications() {
  // TODO: Implémenter la pagination si l'API Reddit le supporte
  console.log('Load more notifications requested');
}

// Fonction utilitaire de debounce
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Améliorer la fonction de fermeture pour nettoyer les ressources
const originalCloseMiniInbox = closeMiniInbox;
closeMiniInbox = function() {
  // Éviter les fermetures multiples
  if (!miniInboxOpen || !miniInboxElement) {
    return;
  }
  
  console.log('Enhanced close: cleaning up resources...');
  
  // Nettoyer les timers et event listeners
  if (miniInboxElement && miniInboxElement.cleanup) {
    miniInboxElement.cleanup();
  }
  
  // Nettoyer les timers de vue des notifications
  const notifications = miniInboxElement?.querySelectorAll('.notification-item') || [];
  notifications.forEach(notification => {
    if (notification.dataset.viewTimer) {
      clearTimeout(notification.dataset.viewTimer);
      delete notification.dataset.viewTimer;
    }
  });
  
  // Nettoyer les event listeners globaux
  document.removeEventListener('click', handleGlobalClick);
  document.removeEventListener('keydown', handleGlobalKeydown);
  
  // Nettoyer les observateurs d'intersection si ils existent
  if (window.miniInboxIntersectionObserver) {
    window.miniInboxIntersectionObserver.disconnect();
    window.miniInboxIntersectionObserver = null;
  }
  
  // Réinitialiser l'index de navigation
  currentNotificationIndex = -1;
  
  // Déclencher un événement personnalisé pour les autres composants
  document.dispatchEvent(new CustomEvent('miniInboxClosed', {
    detail: { timestamp: Date.now() }
  }));
  
  // Appeler la fonction originale
  originalCloseMiniInbox();
};

// Gestionnaires globaux pour la fermeture
function handleGlobalClick(event) {
  if (!miniInboxOpen || !miniInboxElement) return;
  
  // Vérifier si le clic est en dehors de la mini-inbox
  if (!miniInboxElement.contains(event.target)) {
    closeMiniInbox();
  }
}

function handleGlobalKeydown(event) {
  if (!miniInboxOpen) return;
  
  switch (event.key) {
    case 'Escape':
      event.preventDefault();
      closeMiniInbox();
      break;
    case 'Tab':
      // Garder le focus dans la mini-inbox
      if (miniInboxElement && !miniInboxElement.contains(event.target)) {
        event.preventDefault();
        const firstFocusable = miniInboxElement.querySelector('button, a, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
          firstFocusable.focus();
        }
      }
      break;
  }
}

// Gestionnaire pour fermeture automatique après inactivité
let inactivityTimer = null;
const INACTIVITY_TIMEOUT = 30000; // 30 secondes

function resetInactivityTimer() {
  if (inactivityTimer) {
    clearTimeout(inactivityTimer);
  }
  
  if (miniInboxOpen) {
    inactivityTimer = setTimeout(() => {
      console.log('Closing mini-inbox due to inactivity');
      closeMiniInbox();
    }, INACTIVITY_TIMEOUT);
  }
}

// Gestionnaire pour fermeture lors du changement de page
function handlePageChange() {
  if (miniInboxOpen) {
    console.log('Page change detected, closing mini-inbox');
    closeMiniInbox();
  }
}

// Gestionnaire pour fermeture lors du redimensionnement de fenêtre
function handleWindowResize() {
  if (miniInboxOpen && miniInboxElement) {
    // Repositionner ou fermer selon la taille de l'écran
    const windowWidth = window.innerWidth;
    if (windowWidth < 480) {
      // Sur très petit écran, fermer la mini-inbox
      console.log('Small screen detected, closing mini-inbox');
      closeMiniInbox();
    } else {
      // Repositionner la mini-inbox
      const container = miniInboxElement.querySelector('.mini-inbox-container');
      if (container) {
        const rect = container.getBoundingClientRect();
        if (rect.right > windowWidth || rect.bottom > window.innerHeight) {
          // Repositionner si elle dépasse
          container.style.left = Math.max(10, windowWidth - rect.width - 10) + 'px';
          container.style.top = Math.max(10, window.innerHeight - rect.height - 10) + 'px';
        }
      }
    }
  }
}

// Gestionnaire pour fermeture lors de la perte de focus de la fenêtre
function handleWindowBlur() {
  // Optionnel: fermer la mini-inbox quand l'utilisateur change d'onglet
  // Commenté par défaut car peut être trop agressif
  /*
  if (miniInboxOpen) {
    console.log('Window lost focus, closing mini-inbox');
    closeMiniInbox();
  }
  */
}

// Ajouter les gestionnaires d'événements globaux
window.addEventListener('resize', handleWindowResize);
window.addEventListener('blur', handleWindowBlur);

// Observer les changements d'URL pour les SPAs
let currentUrl = window.location.href;
const urlObserver = new MutationObserver(() => {
  if (window.location.href !== currentUrl) {
    currentUrl = window.location.href;
    handlePageChange();
  }
});

// Observer les changements dans l'historique
window.addEventListener('popstate', handlePageChange);
window.addEventListener('pushstate', handlePageChange);
window.addEventListener('replacestate', handlePageChange);

// Améliorer la fonction d'ouverture pour configurer les interactions
const originalOpenMiniInbox = openMiniInbox;
openMiniInbox = async function(iconPosition = null) {
  await originalOpenMiniInbox(iconPosition);
  
  // Démarrer le timer d'inactivité
  resetInactivityTimer();
  
  // Ajouter les event listeners pour réinitialiser le timer d'inactivité
  if (miniInboxElement) {
    miniInboxElement.addEventListener('mousemove', resetInactivityTimer);
    miniInboxElement.addEventListener('keydown', resetInactivityTimer);
    miniInboxElement.addEventListener('scroll', resetInactivityTimer);
    miniInboxElement.addEventListener('click', resetInactivityTimer);
  }
  
  // Configurer les interactions avancées après l'ouverture
  setTimeout(() => {
    setupAdvancedUserInteractions();
    
    // Améliorer le scrolling
    const content = miniInboxElement?.querySelector('.mini-inbox-content');
    if (content) {
      enhanceScrolling(content);
    }
  }, 100);
};