// Script de débogage pour identifier les problèmes
console.log('🔍 DEBUG: Script de débogage chargé - Version 2.0');

// Test 1: Vérifier que le script se charge
console.log('✅ DEBUG: Le script de débogage s\'exécute');

// Test 2: Vérifier l'environnement
console.log('🌐 DEBUG: URL actuelle:', window.location.href);
console.log('📄 DEBUG: Document ready state:', document.readyState);

// Test 3: Vérifier Chrome APIs
if (typeof chrome !== 'undefined') {
  console.log('✅ DEBUG: Chrome APIs disponibles');
  if (chrome.runtime) {
    console.log('✅ DEBUG: chrome.runtime disponible');
  } else {
    console.log('❌ DEBUG: chrome.runtime manquant');
  }
} else {
  console.log('❌ DEBUG: Chrome APIs manquantes');
}

// Test 4: Tester la détection d'icône simple
function testIconDetection() {
  console.log('🔍 DEBUG: Test de détection d\'icône');
  
  // Chercher des éléments qui pourraient être des icônes de notification
  const possibleIcons = document.querySelectorAll([
    'a[href*="inbox"]',
    'a[href*="message"]', 
    'a[href*="notification"]',
    '[aria-label*="notification"]',
    '[aria-label*="inbox"]',
    '[aria-label*="message"]',
    '#mail',
    '.mail'
  ].join(', '));
  
  console.log(`🔍 DEBUG: ${possibleIcons.length} icônes potentielles trouvées`);
  
  possibleIcons.forEach((icon, index) => {
    console.log(`📍 DEBUG: Icône ${index + 1}:`, {
      tagName: icon.tagName,
      id: icon.id,
      className: icon.className,
      href: icon.href,
      ariaLabel: icon.getAttribute('aria-label'),
      textContent: icon.textContent?.substring(0, 50)
    });
  });
}

// Test 5: Ajouter un listener de clic simple
function addSimpleClickListener() {
  console.log('👂 DEBUG: Ajout d\'un listener de clic simple');
  
  document.addEventListener('click', function(event) {
    console.log('🖱️ DEBUG: Clic détecté sur:', {
      tagName: event.target.tagName,
      id: event.target.id,
      className: event.target.className,
      href: event.target.href,
      ariaLabel: event.target.getAttribute('aria-label'),
      textContent: event.target.textContent?.substring(0, 30)
    });
    
    // Vérifier si c'est un lien vers inbox
    const href = event.target.href || event.target.closest('a')?.href;
    if (href && (href.includes('inbox') || href.includes('message') || href.includes('notification'))) {
      console.log('🎯 DEBUG: Clic sur lien inbox/message détecté!');
      console.log('🔗 DEBUG: URL:', href);
      
      // NE PAS empêcher la navigation pour le moment, juste logger
      // event.preventDefault();
    }
  }, true);
}

// Exécuter les tests
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DEBUG: DOM chargé');
    testIconDetection();
    addSimpleClickListener();
  });
} else {
  console.log('📄 DEBUG: DOM déjà chargé');
  testIconDetection();
  addSimpleClickListener();
}

// Test périodique pour voir si de nouveaux éléments apparaissent
setInterval(function() {
  const inboxLinks = document.querySelectorAll('a[href*="inbox"], a[href*="message"]');
  if (inboxLinks.length > 0) {
    console.log(`🔄 DEBUG: ${inboxLinks.length} liens inbox/message trouvés`);
  }
}, 5000);

console.log('🔍 DEBUG: Script de débogage configuré');