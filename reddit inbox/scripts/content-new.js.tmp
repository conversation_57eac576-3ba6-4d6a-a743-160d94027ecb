// Reddit Mini Inbox Extension - Content Script
// Version: 2.0.0 - Complete rewrite
console.log('🚀 Reddit Mini Inbox v2.0.0 loaded - DEBUG MODE');
console.log('🔍 DEBUG: URL actuelle:', window.location.href);
console.log('🔍 DEBUG: User agent:', navigator.userAgent);

// Global state
let miniInboxOpen = false;
let miniInboxElement = null;

// Notification selectors for different Reddit versions
const REDDIT_SELECTORS = {
  old: {
    icons: ['#mail', '.mail', 'a[href*="/message/inbox"]'],
    containers: ['#header-bottom-right', '.user']
  },
  new: {
    icons: ['[aria-label*="notification"]', '[aria-label*="inbox"]', 'a[href*="/message/inbox"]'],
    containers: ['[data-testid="header"]', 'header']
  },
  'new-react': {
    icons: ['button[aria-label*="notification"]', '[data-testid="notification-bell"]', 'a[href*="/message/inbox"]'],
    containers: ['shreddit-app', 'reddit-header-large']
  }
};

// Initialize extension
function init() {
  console.log('Initializing Reddit Mini Inbox...');
  
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setup);
  } else {
    setup();
  }
}

// Setup notification interception
function setup() {
  console.log('Setting up notification interception...');
  
  // Add click listener
  document.addEventListener('click', handleClick, true);
  document.addEventListener('keydown', handleKeyPress);
  
  console.log('✅ Reddit Mini Inbox setup complete');
}

// Handle clicks
function handleClick(event) {
  const target = event.target;
  
  // Check if click is on notification icon
  if (isNotificationIcon(target)) {
    console.log('🎯 Notification icon clicked, intercepting...');
    event.preventDefault();
    event.stopPropagation();
    
    const iconPosition = getIconPosition(target);
    toggleMiniInbox(iconPosition);
    return false;
  }
  
  // Temporary: intercept all inbox links for testing
  const href = target.getAttribute('href') || target.closest('a')?.getAttribute('href');
  if (href && href.includes('/message/inbox')) {
    console.log('🔧 Intercepting inbox link...');
    event.preventDefault();
    event.stopPropagation();
    
    const iconPosition = getIconPosition(target);
    toggleMiniInbox(iconPosition);
    return false;
  }
}

// Handle keyboard
function handleKeyPress(event) {
  if (event.key === 'Escape' && miniInboxOpen) {
    closeMiniInbox();
  }
}

// Toggle mini inbox
function toggleMiniInbox(iconPosition = null) {
  if (miniInboxOpen) {
    closeMiniInbox();
  } else {
    openMiniInbox(iconPosition);
  }
}

// Open mini inbox
async function openMiniInbox(iconPosition = null) {
  console.log('Opening mini inbox...');
  
  if (miniInboxElement) {
    miniInboxElement.remove();
  }
  
  createMiniInboxElement(iconPosition);
  setLoadingState('Loading notifications...');
  
  try {
    // First check if user is authenticated
    const authData = await chrome.storage.local.get(['authenticated']);
    
    const response = await chrome.runtime.sendMessage({
      action: 'getNotifications',
      force: false
    });
    
    if (response && response.success) {
      if (response.notifications && response.notifications.length > 0) {
        displayNotifications(response.notifications);
        if (response.isMockData) {
          showMockDataIndicator();
        }
      } else {
        displayNoNotifications();
      }
    } else {
      if (response && response.needsAuth) {
        displayAuthRequired();
      } else {
        displayError(response.error || 'Unknown error');
      }
    }
  } catch (error) {
    console.error('Error getting notifications:', error);
    displayError('Error loading notifications');
  }
  
  miniInboxOpen = true;
}

// Close mini inbox
function closeMiniInbox() {
  console.log('Closing mini inbox...');
  
  if (miniInboxElement) {
    miniInboxElement.classList.remove('show');
    const container = miniInboxElement.querySelector('.mini-inbox-container');
    if (container) {
      container.classList.add('closing');
    }
    
    setTimeout(() => {
      if (miniInboxElement && miniInboxElement.parentNode) {
        miniInboxElement.remove();
      }
      miniInboxElement = null;
    }, 150);
  }
  
  miniInboxOpen = false;
}

// Create mini inbox element
function createMiniInboxElement(iconPosition = null) {
  miniInboxElement = document.createElement('div');
  miniInboxElement.id = 'reddit-mini-inbox-overlay';
  
  const isDarkTheme = detectDarkTheme();
  const themeClass = isDarkTheme ? 'dark-theme' : '';
  
  miniInboxElement.innerHTML = `
    <div class="mini-inbox-container opening ${themeClass}">
      <div class="mini-inbox-header">
        <div class="mini-inbox-title">
          <span class="mini-inbox-icon">📬</span>
          <span>inbox</span>
        </div>
        <div class="mini-inbox-actions">
          <button class="refresh-button" type="button" aria-label="Refresh" title="Refresh">↻</button>
          <button class="close-btn" type="button" aria-label="Close" title="Close">×</button>
        </div>
      </div>
      <div class="mini-inbox-stats" style="display: none;">
        <span class="unread-count">0 unread</span>
        <span class="total-count">0 total</span>
      </div>
      <div class="mini-inbox-content loading-state">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading notifications...</div>
        </div>
      </div>
    </div>
  `;
  
  if (iconPosition) {
    positionMiniInbox(miniInboxElement, iconPosition);
  }
  
  // Add event listeners
  const closeBtn = miniInboxElement.querySelector('.close-btn');
  closeBtn.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();
    closeMiniInbox();
  });
  
  miniInboxElement.addEventListener('click', (event) => {
    if (event.target === miniInboxElement) {
      closeMiniInbox();
    }
  });
  
  miniInboxElement.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      event.preventDefault();
      closeMiniInbox();
    }
  });
  
  document.body.appendChild(miniInboxElement);
  
  setTimeout(() => {
    miniInboxElement.classList.add('show');
    const container = miniInboxElement.querySelector('.mini-inbox-container');
    container.classList.add('show');
  }, 10);
}

// Display notifications
function displayNotifications(notifications) {
  if (!miniInboxElement) return;
  
  setLoadingState('success');
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  
  const unreadCount = notifications.filter(n => n.new).length;
  const totalCount = notifications.length;
  
  if (statsElement) {
    statsElement.style.display = 'flex';
    statsElement.querySelector('.unread-count').textContent = `${unreadCount} unread`;
    statsElement.querySelector('.total-count').textContent = `${totalCount} total`;
  }
  
  if (notifications.length === 0) {
    displayNoNotifications();
    return;
  }
  
  const notificationsHtml = notifications.map(notification => {
    const typeClass = notification.type ? notification.type.replace('_', '-') : 'message';
    const contextText = getNotificationContext(notification);
    const actionsHtml = getNotificationActions(notification);
    
    return `
      <div class="notification-item ${notification.new ? 'unread' : 'read'} ${typeClass}" 
           data-id="${notification.id}" 
           data-type="${notification.type || 'message'}"
           tabindex="0"
           role="button"
           aria-label="Notification from ${escapeHtml(notification.author)}">
        <div class="notification-header">
          <a href="/user/${escapeHtml(notification.author)}" class="notification-author" target="_blank">
            ${escapeHtml(notification.author)}
          </a>
          <span class="notification-time" title="${formatFullTime(notification.created_utc)}">
            ${formatTime(notification.created_utc)}
          </span>
        </div>
        <div class="notification-subject">${escapeHtml(notification.subject)}</div>
        ${contextText ? `<div class="notification-context">${contextText}</div>` : ''}
        <div class="notification-body">${formatNotificationBody(notification.body)}</div>
        <div class="notification-actions">
          ${actionsHtml}
        </div>
      </div>
    `;
  }).join('');
  
  content.innerHTML = notificationsHtml;
  
  // CRITICAL: Attach event listeners immediately after HTML insertion
  attachEventListeners(content);
}

// Attach event listeners - THIS IS THE KEY FUNCTION
function attachEventListeners(content) {
  console.log('🔧 DEBUG: Attaching event listeners...');
  console.log('🔍 DEBUG: Content element:', content);
  
  // Attach to notification items
  const notificationItems = content.querySelectorAll('.notification-item');
  console.log(`🔍 DEBUG: Found ${notificationItems.length} notification items`);
  
  notificationItems.forEach((item, index) => {
    console.log(`🔍 DEBUG: Attaching to notification item ${index}:`, item);
    
    item.addEventListener('click', (event) => {
      console.log('🖱️ DEBUG: Notification item clicked');
      if (!event.target.closest('.notification-actions') && 
          !event.target.closest('.quick-reply-interface')) {
        handleNotificationClick(event);
      }
    });
    
    setTimeout(() => item.classList.add('new-notification'), index * 50);
  });
  
  // Attach to action buttons - MOST IMPORTANT
  const actionButtons = content.querySelectorAll('.notification-action');
  console.log(`🔍 DEBUG: Found ${actionButtons.length} action buttons`);
  
  actionButtons.forEach((action, index) => {
    console.log(`🔍 DEBUG: Attaching to action ${index}:`, {
      element: action,
      textContent: action.textContent,
      dataAction: action.getAttribute('data-action'),
      dataId: action.getAttribute('data-id'),
      tagName: action.tagName,
      className: action.className
    });
    
    action.addEventListener('click', function(event) {
      console.log('🎯 DEBUG: Action button clicked!', this);
      event.preventDefault();
      event.stopPropagation();
      
      const actionType = this.getAttribute('data-action');
      const notificationId = this.getAttribute('data-id');
      const notificationItem = this.closest('.notification-item');
      
      console.log('🎯 DEBUG: Action details:', {
        actionType,
        notificationId,
        notificationItem,
        element: this
      });
      
      handleNotificationAction(actionType, notificationId, notificationItem, this);
    });
    
    // Test immédiat pour vérifier si l'event listener fonctionne
    action.style.border = '1px solid red';
    console.log(`🔍 DEBUG: Added red border to action ${index} for testing`);
  });
  
  // Attach to user links
  const userLinks = content.querySelectorAll('.notification-author');
  console.log(`🔍 DEBUG: Found ${userLinks.length} user links`);
  
  userLinks.forEach((link, index) => {
    link.addEventListener('click', (event) => {
      event.stopPropagation();
      console.log('👤 DEBUG: User link clicked:', link.textContent);
    });
  });
  
  console.log('✅ DEBUG: Event listeners attached successfully!');
  console.log('🔍 DEBUG: Summary:', {
    notificationItems: notificationItems.length,
    actionButtons: actionButtons.length,
    userLinks: userLinks.length
  });
}

// Handle notification actions
function handleNotificationAction(actionType, notificationId, notificationItem, actionElement) {
  console.log('Handling action:', actionType);
  
  switch (actionType) {
    case 'reply':
      handleReplyAction(notificationId, notificationItem);
      break;
    
    case 'mark-read':
      handleMarkAsReadAction(notificationId, notificationItem, actionElement);
      break;
    
    case 'mark-unread':
      handleMarkAsUnreadAction(notificationId, notificationItem, actionElement);
      break;
    
    default:
      console.warn('Unknown action:', actionType);
  }
}

// Handle reply action
function handleReplyAction(notificationId, notificationItem) {
  console.log('💬 Reply action for notification:', notificationId);
  
  const existingReply = notificationItem.querySelector('.quick-reply-interface');
  if (existingReply) {
    existingReply.remove();
    return;
  }
  
  const replyInterface = createQuickReplyInterface(notificationId, notificationItem);
  const notificationBody = notificationItem.querySelector('.notification-body');
  notificationBody.insertAdjacentElement('afterend', replyInterface);
  
  const textarea = replyInterface.querySelector('.reply-textarea');
  textarea.focus();
  
  // Mark as read since user is interacting
  handleMarkAsReadAction(notificationId, notificationItem);
}

// Create quick reply interface
function createQuickReplyInterface(notificationId, notificationItem) {
  const replyDiv = document.createElement('div');
  replyDiv.className = 'quick-reply-interface';
  replyDiv.style.cssText = `
    background: #f8f9fa; border: 1px solid #e5e5e5; border-radius: 4px;
    margin: 8px 0; padding: 0; animation: slideDown 0.2s ease-out;
  `;
  
  replyDiv.innerHTML = `
    <div style="background: #ff4500; color: white; padding: 6px 10px; display: flex; justify-content: space-between; border-radius: 4px 4px 0 0;">
      <span style="font-weight: bold; font-size: 11px;">Quick Reply</span>
      <button class="reply-close" style="background: none; border: none; color: white; cursor: pointer; font-size: 16px;">×</button>
    </div>
    <div style="padding: 10px;">
      <textarea class="reply-textarea" placeholder="Write your reply..." rows="3" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-size: 11px; box-sizing: border-box; margin-bottom: 8px; resize: vertical;"></textarea>
      <div style="display: flex; gap: 6px; flex-wrap: wrap;">
        <button class="reply-send" style="background: #ff4500; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer; font-weight: bold;">Send Reply</button>
        <button class="reply-cancel" style="background: #f5f5f5; color: #666; border: 1px solid #ddd; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Cancel</button>
        <button class="reply-full" style="background: #0079d3; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Open Full Editor</button>
      </div>
    </div>
  `;
  
  // Add event listeners
  const closeBtn = replyDiv.querySelector('.reply-close');
  const cancelBtn = replyDiv.querySelector('.reply-cancel');
  const sendBtn = replyDiv.querySelector('.reply-send');
  const fullBtn = replyDiv.querySelector('.reply-full');
  const textarea = replyDiv.querySelector('.reply-textarea');
  
  const closeReply = () => replyDiv.remove();
  closeBtn.addEventListener('click', closeReply);
  cancelBtn.addEventListener('click', closeReply);
  
  sendBtn.addEventListener('click', () => {
    const text = textarea.value.trim();
    if (text) {
      sendQuickReply(notificationId, text, replyDiv);
    } else {
      textarea.focus();
      textarea.style.borderColor = '#ff4500';
      setTimeout(() => textarea.style.borderColor = '', 2000);
    }
  });
  
  fullBtn.addEventListener('click', () => {
    const text = textarea.value.trim();
    let url = '/message/compose';
    if (text) {
      url += '?message=' + encodeURIComponent(text);
    }
    
    const tempLink = document.createElement('a');
    tempLink.href = url;
    tempLink.target = '_blank';
    tempLink.rel = 'noopener noreferrer';
    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
    
    closeReply();
  });
  
  textarea.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      sendBtn.click();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      closeReply();
    }
  });
  
  return replyDiv;
}

// Send quick reply
async function sendQuickReply(notificationId, replyText, replyInterface) {
  const sendBtn = replyInterface.querySelector('.reply-send');
  const textarea = replyInterface.querySelector('.reply-textarea');
  
  sendBtn.disabled = true;
  sendBtn.textContent = 'Sending...';
  textarea.disabled = true;
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    replyInterface.innerHTML = `
      <div style="padding: 15px; text-align: center; background: #f0f8ff; border-radius: 4px;">
        <div style="font-size: 24px; margin-bottom: 8px;">✅</div>
        <div style="color: #0079d3; font-weight: bold; margin-bottom: 10px;">Reply sent successfully!</div>
        <button onclick="this.closest('.quick-reply-interface').remove()" style="background: #0079d3; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 10px; cursor: pointer;">Close</button>
      </div>
    `;
    
    setTimeout(() => {
      if (replyInterface.parentNode) {
        replyInterface.remove();
      }
    }, 3000);
    
  } catch (error) {
    console.error('Error sending reply:', error);
    sendBtn.disabled = false;
    sendBtn.textContent = 'Send Reply';
    textarea.disabled = false;
    textarea.focus();
  }
}

// Handle mark as read
function handleMarkAsReadAction(notificationId, notificationItem, actionElement = null) {
  console.log('✅ Mark as read:', notificationId);
  
  chrome.runtime.sendMessage({
    action: 'markAsRead',
    notificationId: notificationId
  }).then(response => {
    if (response && response.success) {
      notificationItem.classList.remove('unread');
      notificationItem.classList.add('read');
      
      if (actionElement) {
        actionElement.setAttribute('data-action', 'mark-unread');
        actionElement.textContent = 'mark as unread';
      }
      
      updateUnreadCount();
    }
  }).catch(error => {
    console.error('Error marking as read:', error);
  });
}

// Handle mark as unread
function handleMarkAsUnreadAction(notificationId, notificationItem, actionElement) {
  console.log('👁️ Mark as unread:', notificationId);
  
  notificationItem.classList.remove('read');
  notificationItem.classList.add('unread');
  
  actionElement.setAttribute('data-action', 'mark-read');
  actionElement.textContent = 'mark as read';
  
  updateUnreadCount();
}

// Handle notification click
function handleNotificationClick(event) {
  const notificationItem = event.currentTarget;
  const notificationId = notificationItem.dataset.id;
  
  console.log('Notification clicked:', notificationId);
  
  chrome.runtime.sendMessage({
    action: 'markAsRead',
    notificationId: notificationId
  }).then(response => {
    if (response && response.success) {
      notificationItem.classList.remove('unread');
      notificationItem.classList.add('read');
      updateUnreadCount();
    }
  }).catch(error => {
    console.error('Error marking as read:', error);
  });
}

// Update unread count
function updateUnreadCount() {
  if (!miniInboxElement) return;
  
  const unreadItems = miniInboxElement.querySelectorAll('.notification-item.unread');
  const unreadCount = unreadItems.length;
  
  const statsElement = miniInboxElement.querySelector('.mini-inbox-stats');
  if (statsElement) {
    const unreadCountElement = statsElement.querySelector('.unread-count');
    if (unreadCountElement) {
      unreadCountElement.textContent = `${unreadCount} unread`;
    }
  }
}

// Utility functions
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function formatTime(timestamp) {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) return 'now';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
  return `${Math.floor(diff / 86400000)}d`;
}

function formatFullTime(timestamp) {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

function formatNotificationBody(body) {
  if (!body) return '';
  
  let cleanBody = escapeHtml(body);
  cleanBody = cleanBody.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
  cleanBody = cleanBody.replace(/\/?u\/([a-zA-Z0-9_-]+)/g, '<a href="/user/$1" target="_blank">u/$1</a>');
  cleanBody = cleanBody.replace(/\/?r\/([a-zA-Z0-9_-]+)/g, '<a href="/r/$1" target="_blank">r/$1</a>');
  cleanBody = cleanBody.replace(/\n/g, '<br>');
  
  return cleanBody;
}

function getNotificationContext(notification) {
  if (!notification) return '';
  
  let context = '';
  
  if (notification.type === 'comment_reply' || notification.type === 'username_mention') {
    if (notification.subreddit) {
      context = `in <a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>`;
    }
  }
  
  if (notification.type === 'post_reply') {
    if (notification.subreddit) {
      context = `in <a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>`;
    }
  }
  
  return context;
}

function getNotificationActions(notification) {
  if (!notification) return '';
  
  let actions = [];
  
  if (notification.type === 'comment_reply' || notification.type === 'username_mention' || notification.type === 'private_message') {
    actions.push(`<a href="#" class="notification-action" data-action="reply" data-id="${notification.id}">reply</a>`);
  }
  
  if (notification.permalink) {
    const contextUrl = notification.context || notification.permalink;
    actions.push(`<a href="${escapeHtml(contextUrl)}" class="notification-action" target="_blank">context</a>`);
  }
  
  if (notification.new) {
    actions.push(`<a href="#" class="notification-action" data-action="mark-read" data-id="${notification.id}">mark as read</a>`);
  } else {
    actions.push(`<a href="#" class="notification-action" data-action="mark-unread" data-id="${notification.id}">mark as unread</a>`);
  }
  
  return actions.join(' • ');
}

// Display states
function setLoadingState(message = 'Loading...') {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.className = 'mini-inbox-content loading-state';
  content.innerHTML = `
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">${message}</div>
    </div>
  `;
}

function displayError(error) {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.innerHTML = `<div class="error">Error: ${escapeHtml(error)}</div>`;
}

function displayNoNotifications() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.innerHTML = `
    <div class="no-notifications">
      <div class="no-notifications-icon">📭</div>
      <div class="no-notifications-title">No notifications</div>
      <div class="no-notifications-subtitle">You're all caught up!</div>
    </div>
  `;
}

function displayAuthRequired() {
  if (!miniInboxElement) return;
  
  const content = miniInboxElement.querySelector('.mini-inbox-content');
  content.innerHTML = `
    <div class="auth-required">
      <div class="auth-required-icon">🔐</div>
      <div class="auth-required-title">Authentication required</div>
      <div class="auth-required-subtitle">Connect to Reddit to see your real notifications</div>
      <button class="auth-required-button" onclick="chrome.runtime.sendMessage({action: 'openPopup'})">Connect to Reddit</button>
    </div>
  `;
}

function showMockDataIndicator() {
  if (!miniInboxElement) return;
  
  const header = miniInboxElement.querySelector('.mini-inbox-header');
  if (header) {
    const indicator = document.createElement('div');
    indicator.className = 'mock-data-indicator';
    indicator.innerHTML = `<span>🧪</span><span>Test data</span>`;
    indicator.style.cssText = `
      display: flex; align-items: center; gap: 4px; background: rgba(255, 193, 7, 0.1);
      border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 12px; padding: 4px 8px;
      font-size: 10px; color: #856404; margin-left: 8px;
    `;
    header.appendChild(indicator);
  }
}

// Detection functions
function isNotificationIcon(element) {
  if (!element) return false;
  
  // Check element and parents
  for (let el = element; el && el !== document; el = el.parentElement) {
    if (isNotificationIconByAttributes(el)) {
      return true;
    }
  }
  
  // Check if it's a link to inbox
  const href = element.getAttribute('href') || element.closest('a')?.getAttribute('href');
  if (href && (href.includes('/message/inbox') || href.includes('/notifications'))) {
    return true;
  }
  
  return false;
}

function isNotificationIconByAttributes(element) {
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel) {
    const lowerLabel = ariaLabel.toLowerCase();
    if (lowerLabel.includes('notification') || lowerLabel.includes('inbox') || 
        lowerLabel.includes('message') || lowerLabel.includes('mail') || 
        lowerLabel.includes('unread')) {
      return true;
    }
  }
  
  const id = element.id;
  if (id === 'mail' || id === 'inbox' || id === 'messages' || 
      id === 'notification' || id === 'notifications') {
    return true;
  }
  
  const className = element.className;
  if (typeof className === 'string' && 
      (className.includes('mail') || className.includes('inbox') || 
       className.includes('notification') || className.includes('message') || 
       className.includes('bell')) {
    return true;
  }
  
  const tagName = element.tagName?.toLowerCase();
  if (tagName === 'shreddit-notification-bell' || tagName === 'reddit-notification-bell' || 
      tagName === 'notification-bell' || tagName === 'reddit-header-action-button') {
    return true;
  }
  
  if (element.getAttribute('data-testid')?.includes('notification') ||
      element.getAttribute('data-testid')?.includes('inbox') ||
      element.getAttribute('data-testid')?.includes('message')) {
    return true;
  }
  
  return false;
}

function getIconPosition(element) {
  const rect = element.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  
  return {
    top: rect.bottom + scrollTop + 5,
    left: rect.left + scrollLeft,
    right: rect.right + scrollLeft,
    width: rect.width,
    height: rect.height,
    element: element
  };
}

function positionMiniInbox(overlayElement, iconPosition) {
  const container = overlayElement.querySelector('.mini-inbox-container');
  if (!container) return;
  
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  
  const inboxWidth = 400;
  const inboxHeight = 500;
  
  let top = iconPosition.top;
  let left = iconPosition.left;
  
  if (left + inboxWidth > windowWidth) {
    left = iconPosition.right - inboxWidth;
  }
  
  if (top + inboxHeight > windowHeight + scrollTop) {
    top = iconPosition.top - iconPosition.height - inboxHeight - 10;
    if (top < scrollTop) {
      top = scrollTop + (windowHeight - inboxHeight) / 2;
    }
  }
  
  left = Math.max(10, left);
  top = Math.max(scrollTop + 10, top);
  
  overlayElement.style.position = 'absolute';
  overlayElement.style.top = '0';
  overlayElement.style.left = '0';
  overlayElement.style.width = '100%';
  overlayElement.style.height = '100%';
  overlayElement.style.backgroundColor = 'transparent';
  overlayElement.style.pointerEvents = 'none';
  
  container.style.position = 'absolute';
  container.style.top = `${top}px`;
  container.style.left = `${left}px`;
  container.style.pointerEvents = 'auto';
  container.style.zIndex = '10001';
}

function detectDarkTheme() {
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return true;
  }
  
  const body = document.body;
  const html = document.documentElement;
  
  if (body.classList.contains('dark') || body.classList.contains('dark-theme') ||
      html.classList.contains('dark') || html.classList.contains('dark-theme')) {
    return true;
  }
  
  if (body.getAttribute('data-theme') === 'dark' ||
      html.getAttribute('data-theme') === 'dark') {
    return true;
  }
  
  return false;
}

// Initialize extension
init();