// Test approfondi du CSS pour identifier le problème de colonnes
console.log('🔍 ANALYSE APPROFONDIE DU PROBLÈME DE DESIGN EN COLONNES');
console.log('='.repeat(70));

const fs = require('fs');

function analyzeCSSStructure() {
  console.log('\n🎨 ANALYSE DE LA STRUCTURE CSS:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Rechercher les propriétés qui pourraient causer un layout en colonnes
    const problematicProperties = [
      { prop: 'display.*grid', desc: 'CSS Grid' },
      { prop: 'display.*flex', desc: 'Flexbox' },
      { prop: 'column-count', desc: 'CSS Columns' },
      { prop: 'columns:', desc: 'CSS Columns shorthand' },
      { prop: 'column-width', desc: 'Column width' },
      { prop: 'column-gap', desc: 'Column gap' },
      { prop: 'grid-template-columns', desc: 'Grid columns' },
      { prop: 'flex-direction.*column', desc: 'Flex column direction' },
      { prop: 'writing-mode', desc: 'Writing mode' },
      { prop: 'text-align.*justify', desc: 'Text justify' }
    ];
    
    console.log('🔍 Recherche de propriétés problématiques:');
    let foundProblems = [];
    
    problematicProperties.forEach(({ prop, desc }) => {
      const regex = new RegExp(prop, 'gi');
      const matches = cssContent.match(regex);
      if (matches) {
        console.log(`⚠️  ${desc}: ${matches.length} occurrence(s)`);
        foundProblems.push({ desc, matches: matches.length, examples: matches.slice(0, 3) });
        
        // Afficher les premières occurrences
        matches.slice(0, 2).forEach(match => {
          console.log(`     Exemple: ${match}`);
        });
      } else {
        console.log(`✅ ${desc}: non trouvé`);
      }
    });
    
    return foundProblems;
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return [];
  }
}

function analyzeContainerProperties() {
  console.log('\n📦 ANALYSE DES PROPRIÉTÉS DE CONTENEUR:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Extraire les règles pour .mini-inbox-container
    const containerMatch = cssContent.match(/\.mini-inbox-container\s*\{[^}]*\}/g);
    
    if (containerMatch) {
      console.log(`✅ Trouvé ${containerMatch.length} règle(s) pour .mini-inbox-container:`);
      
      containerMatch.forEach((rule, index) => {
        console.log(`\n📋 Règle ${index + 1}:`);
        console.log(rule);
        
        // Analyser les propriétés spécifiques
        const properties = [
          'width', 'min-width', 'max-width',
          'display', 'position', 'overflow',
          'flex', 'grid', 'column'
        ];
        
        properties.forEach(prop => {
          const propRegex = new RegExp(`${prop}\\s*:\\s*([^;]+)`, 'gi');
          const propMatches = rule.match(propRegex);
          if (propMatches) {
            propMatches.forEach(match => {
              console.log(`   ${prop}: ${match}`);
            });
          }
        });
      });
    } else {
      console.log('❌ Aucune règle trouvée pour .mini-inbox-container');
    }
    
    return containerMatch;
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return null;
  }
}

function analyzeNotificationItemProperties() {
  console.log('\n📝 ANALYSE DES PROPRIÉTÉS DES NOTIFICATION-ITEM:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Extraire les règles pour .notification-item
    const itemMatch = cssContent.match(/\.notification-item[^{]*\{[^}]*\}/g);
    
    if (itemMatch) {
      console.log(`✅ Trouvé ${itemMatch.length} règle(s) pour .notification-item:`);
      
      itemMatch.forEach((rule, index) => {
        console.log(`\n📋 Règle ${index + 1}:`);
        console.log(rule.substring(0, 200) + (rule.length > 200 ? '...' : ''));
        
        // Vérifier les propriétés critiques
        const criticalProps = [
          'display', 'width', 'float', 'position',
          'flex', 'grid', 'column', 'writing-mode'
        ];
        
        criticalProps.forEach(prop => {
          if (rule.includes(prop)) {
            const propRegex = new RegExp(`${prop}\\s*:\\s*([^;!]+)`, 'gi');
            const propMatches = rule.match(propRegex);
            if (propMatches) {
              propMatches.forEach(match => {
                console.log(`   ⚠️  ${match.trim()}`);
              });
            }
          }
        });
      });
    } else {
      console.log('❌ Aucune règle trouvée pour .notification-item');
    }
    
    return itemMatch;
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return null;
  }
}

function checkForConflictingCSS() {
  console.log('\n⚔️  RECHERCHE DE CSS CONFLICTUEL:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Rechercher des patterns qui pourraient causer des problèmes
    const conflictPatterns = [
      { pattern: 'display\\s*:\\s*table', desc: 'Display table' },
      { pattern: 'display\\s*:\\s*inline', desc: 'Display inline' },
      { pattern: 'float\\s*:\\s*(left|right)', desc: 'Float properties' },
      { pattern: 'position\\s*:\\s*absolute.*width\\s*:\\s*33', desc: 'Absolute positioning with 33% width' },
      { pattern: 'width\\s*:\\s*33%', desc: '33% width (3 columns)' },
      { pattern: 'width\\s*:\\s*calc\\([^)]*\\/\\s*3', desc: 'Width calc divided by 3' },
      { pattern: 'flex\\s*:\\s*1\\s+1\\s+33', desc: 'Flex basis 33%' },
      { pattern: 'grid-template-columns.*1fr.*1fr.*1fr', desc: 'Grid 3 columns' }
    ];
    
    let foundConflicts = [];
    
    conflictPatterns.forEach(({ pattern, desc }) => {
      const regex = new RegExp(pattern, 'gi');
      const matches = cssContent.match(regex);
      if (matches) {
        console.log(`🚨 ${desc}: ${matches.length} occurrence(s)`);
        foundConflicts.push({ desc, matches });
        matches.forEach(match => {
          console.log(`     ${match}`);
        });
      } else {
        console.log(`✅ ${desc}: non trouvé`);
      }
    });
    
    return foundConflicts;
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return [];
  }
}

function analyzeImportantDeclarations() {
  console.log('\n❗ ANALYSE DES DÉCLARATIONS !IMPORTANT:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Compter les !important
    const importantCount = (cssContent.match(/!important/g) || []).length;
    console.log(`📊 Total de déclarations !important: ${importantCount}`);
    
    // Analyser les !important sur les propriétés critiques
    const criticalProperties = [
      'width', 'min-width', 'max-width',
      'display', 'position', 'overflow',
      'flex', 'grid', 'column', 'float'
    ];
    
    console.log('\n🔍 !important sur propriétés critiques:');
    criticalProperties.forEach(prop => {
      const regex = new RegExp(`${prop}\\s*:[^;!]*!important`, 'gi');
      const matches = cssContent.match(regex);
      if (matches) {
        console.log(`✅ ${prop}: ${matches.length} occurrence(s)`);
        matches.slice(0, 2).forEach(match => {
          console.log(`     ${match.trim()}`);
        });
      } else {
        console.log(`❌ ${prop}: aucun !important`);
      }
    });
    
    return importantCount;
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return 0;
  }
}

function compareWithWorkingVersion() {
  console.log('\n🔄 COMPARAISON AVEC VERSION FONCTIONNELLE:');
  
  // Propriétés CSS qui devraient être présentes pour un layout correct
  const expectedProperties = [
    { selector: '.mini-inbox-container', prop: 'width: 400px !important' },
    { selector: '.mini-inbox-container', prop: 'min-width: 400px !important' },
    { selector: '.mini-inbox-container', prop: 'display: block !important' },
    { selector: '.notification-item', prop: 'display: block !important' },
    { selector: '.notification-item', prop: 'width: 100% !important' },
    { selector: '.notification-actions', prop: 'display: flex' }
  ];
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    console.log('🔍 Vérification des propriétés attendues:');
    let missingProperties = [];
    
    expectedProperties.forEach(({ selector, prop }) => {
      // Rechercher le sélecteur et la propriété
      const selectorRegex = new RegExp(`${selector.replace('.', '\\.')}[^{]*\\{[^}]*${prop.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'gi');
      
      if (selectorRegex.test(cssContent)) {
        console.log(`✅ ${selector} - ${prop}`);
      } else {
        console.log(`❌ ${selector} - ${prop} MANQUANT`);
        missingProperties.push({ selector, prop });
      }
    });
    
    return missingProperties;
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return [];
  }
}

function identifyColumnCause() {
  console.log('\n🎯 IDENTIFICATION DE LA CAUSE DES COLONNES:');
  
  const cssProblems = analyzeCSSStructure();
  const containerProps = analyzeContainerProperties();
  const itemProps = analyzeNotificationItemProperties();
  const conflicts = checkForConflictingCSS();
  const importantCount = analyzeImportantDeclarations();
  const missingProps = compareWithWorkingVersion();
  
  console.log('\n' + '='.repeat(60));
  console.log('🎯 DIAGNOSTIC FINAL - CAUSE DES COLONNES:');
  console.log('='.repeat(60));
  
  let suspectedCauses = [];
  
  // Analyser les causes potentielles
  if (conflicts.length > 0) {
    suspectedCauses.push('🚨 CSS conflictuel détecté');
    conflicts.forEach(conflict => {
      console.log(`   - ${conflict.desc}: ${conflict.matches.length} occurrence(s)`);
    });
  }
  
  if (missingProps.length > 0) {
    suspectedCauses.push('❌ Propriétés CSS critiques manquantes');
    missingProps.forEach(missing => {
      console.log(`   - ${missing.selector}: ${missing.prop}`);
    });
  }
  
  if (cssProblems.length > 0) {
    suspectedCauses.push('⚠️  Propriétés CSS problématiques');
    cssProblems.forEach(problem => {
      console.log(`   - ${problem.desc}: ${problem.matches} occurrence(s)`);
    });
  }
  
  if (importantCount < 50) {
    suspectedCauses.push('❗ Pas assez de déclarations !important');
    console.log(`   - Seulement ${importantCount} !important trouvés`);
  }
  
  console.log('\n💡 CAUSES PROBABLES DU PROBLÈME DE COLONNES:');
  if (suspectedCauses.length === 0) {
    console.log('🤔 Aucune cause évidente trouvée dans le CSS');
    console.log('   Le problème pourrait être:');
    console.log('   1. JavaScript qui modifie le DOM/CSS dynamiquement');
    console.log('   2. CSS de Reddit qui interfère');
    console.log('   3. Problème de timing de chargement');
  } else {
    suspectedCauses.forEach((cause, index) => {
      console.log(`   ${index + 1}. ${cause}`);
    });
  }
  
  return {
    cssProblems,
    conflicts,
    missingProps,
    importantCount,
    suspectedCauses
  };
}

// Exécuter l'analyse complète
identifyColumnCause();