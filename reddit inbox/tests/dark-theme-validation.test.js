const fs = require('fs');
const path = require('path');

describe('Dark Theme Validation', () => {
  let cssContent;
  let jsContent;

  beforeAll(() => {
    const cssPath = path.join(__dirname, '..', 'styles', 'mini-inbox.css');
    const jsPath = path.join(__dirname, '..', 'scripts', 'content-new.js');
    cssContent = fs.readFileSync(cssPath, 'utf8');
    jsContent = fs.readFileSync(jsPath, 'utf8');
  });

  describe('CSS Dark Theme Support', () => {
    test('should not force white background with !important', () => {
      // Vérifier qu'il n'y a plus de background: white !important
      expect(cssContent).not.toMatch(/background:\s*white\s*!important/);
      expect(cssContent).not.toMatch(/background-color:\s*#ffffff\s*!important/);
    });

    test('should have dark theme overrides with !important', () => {
      // Vérifier que les overrides dark-theme ont !important
      expect(cssContent).toMatch(/\.mini-inbox-container\.dark-theme\s*{[^}]*background-color:\s*#1a1a1b\s*!important/);
      expect(cssContent).toMatch(/\.mini-inbox-container\.dark-theme\s+\.mini-inbox-content\s*{[^}]*background-color:\s*#1a1a1b\s*!important/);
      expect(cssContent).toMatch(/\.mini-inbox-container\.dark-theme\s+\.notification-item\s*{[^}]*background:\s*#1a1a1b\s*!important/);
    });

    test('should have dark theme hover states', () => {
      expect(cssContent).toMatch(/\.mini-inbox-container\.dark-theme\s+\.notification-item:hover\s*{[^}]*background-color:\s*#272729\s*!important/);
    });

    test('should have proper dark theme colors', () => {
      expect(cssContent).toMatch(/\.mini-inbox-container\.dark-theme\s+\.notification-author\s*{[^}]*color:\s*#4fbcff/);
      expect(cssContent).toMatch(/\.mini-inbox-container\.dark-theme\s+\.notification-subject\s*{[^}]*color:\s*#d7dadc/);
      expect(cssContent).toMatch(/\.mini-inbox-container\.dark-theme\s+\.notification-body\s*{[^}]*color:\s*#818384/);
    });
  });

  describe('JavaScript Dark Theme Detection', () => {
    test('should have enhanced detectDarkTheme function', () => {
      expect(jsContent).toContain('function detectDarkTheme()');
      
      // Vérifier les nouveaux sélecteurs Reddit
      expect(jsContent).toContain('data-theme');
      expect(jsContent).toContain('data-colorscheme');
      expect(jsContent).toContain('data-nightmode');
      expect(jsContent).toContain('theme-dark');
      expect(jsContent).toContain('theme--nightmode');
      expect(jsContent).toContain('shreddit-app');
    });

    test('should have theme observer functionality', () => {
      expect(jsContent).toContain('function updateMiniInboxTheme()');
      expect(jsContent).toContain('function setupThemeObserver()');
      expect(jsContent).toContain('MutationObserver');
    });

    test('should detect system color scheme preference', () => {
      expect(jsContent).toContain('prefers-color-scheme: dark');
      expect(jsContent).toContain('matchMedia');
    });

    test('should detect theme from computed styles', () => {
      expect(jsContent).toContain('getComputedStyle');
      expect(jsContent).toContain('backgroundColor');
      expect(jsContent).toContain('brightness');
    });

    test('should initialize theme observer', () => {
      expect(jsContent).toContain('setupThemeObserver()');
    });

    test('should update theme when showing mini-inbox', () => {
      expect(jsContent).toContain('updateMiniInboxTheme()');
    });
  });

  describe('Theme Integration', () => {
    test('should have proper CSS specificity for dark theme', () => {
      // Vérifier que les règles dark-theme sont assez spécifiques
      const darkThemeRules = cssContent.match(/\.mini-inbox-container\.dark-theme[^{]*{[^}]*}/g);
      expect(darkThemeRules).toBeTruthy();
      expect(darkThemeRules.length).toBeGreaterThan(10);
    });

    test('should not have conflicting background rules', () => {
      // Vérifier qu'il n'y a pas de conflits entre les règles de background
      const whiteBackgrounds = cssContent.match(/background(-color)?:\s*(white|#ffffff)\s*!important/g);
      expect(whiteBackgrounds).toBeNull();
    });
  });

  describe('Reddit Compatibility', () => {
    test('should support all Reddit theme selectors', () => {
      const requiredSelectors = [
        'data-theme',
        'data-colorscheme', 
        'data-nightmode',
        'theme-dark',
        'theme--nightmode',
        'shreddit-app'
      ];

      requiredSelectors.forEach(selector => {
        expect(jsContent).toContain(selector);
      });
    });

    test('should handle dynamic theme changes', () => {
      expect(jsContent).toContain('MutationObserver');
      expect(jsContent).toContain('attributeFilter');
      expect(jsContent).toContain('addListener');
    });
  });
});
