// Tests unitaires pour la détection de l'icône de notification Reddit

// Mock du DOM pour les tests
global.document = {
  createElement: jest.fn(() => ({
    textContent: '',
    innerHTML: ''
  })),
  querySelector: jest.fn(),
  querySelectorAll: jest.fn(() => []),
  body: {
    appendChild: jest.fn()
  },
  documentElement: {
    scrollTop: 0
  }
};

global.window = {
  location: {
    hostname: 'www.reddit.com',
    pathname: '/'
  },
  innerWidth: 1920,
  innerHeight: 1080,
  pageYOffset: 0,
  addEventListener: jest.fn()
};

global.Node = {
  ELEMENT_NODE: 1
};

global.MutationObserver = jest.fn(() => ({
  observe: jest.fn(),
  disconnect: jest.fn()
}));

describe('Reddit Notification Icon Detection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('detectRedditVersion', () => {
    test('should detect old Reddit', () => {
      window.location.hostname = 'old.reddit.com';
      
      const version = detectRedditVersion();
      
      expect(version).toBe('old');
    });

    test('should detect new React Reddit', () => {
      window.location.hostname = 'www.reddit.com';
      document.querySelector.mockReturnValueOnce({ tagName: 'SHREDDIT-APP' });
      
      const version = detectRedditVersion();
      
      expect(version).toBe('new-react');
    });

    test('should detect new Reddit as fallback', () => {
      window.location.hostname = 'www.reddit.com';
      document.querySelector.mockReturnValue(null);
      
      const version = detectRedditVersion();
      
      expect(version).toBe('new');
    });

    test('should default to new for unknown hostnames', () => {
      window.location.hostname = 'unknown.reddit.com';
      
      const version = detectRedditVersion();
      
      expect(version).toBe('new');
    });
  });

  describe('setupNotificationSelectors', () => {
    test('should configure selectors for old Reddit', () => {
      const selectors = {};
      
      setupNotificationSelectors('old', selectors);
      
      expect(selectors.version).toBe('old');
      expect(selectors.icons).toContain('#mail');
      expect(selectors.icons).toContain('a[href*="/message/inbox"]');
      expect(selectors.containers).toContain('#header-bottom-right');
    });

    test('should configure selectors for new Reddit', () => {
      const selectors = {};
      
      setupNotificationSelectors('new', selectors);
      
      expect(selectors.version).toBe('new');
      expect(selectors.icons).toContain('[aria-label*="notification"]');
      expect(selectors.icons).toContain('button[aria-label*="notification"]');
      expect(selectors.containers).toContain('header');
    });

    test('should configure selectors for new React Reddit', () => {
      const selectors = {};
      
      setupNotificationSelectors('new-react', selectors);
      
      expect(selectors.version).toBe('new-react');
      expect(selectors.icons).toContain('button[aria-label*="notification"]');
      expect(selectors.icons).toContain('[data-testid="notification-bell"]');
      expect(selectors.containers).toContain('shreddit-app');
    });

    test('should configure generic selectors for unknown version', () => {
      const selectors = {};
      
      setupNotificationSelectors('unknown', selectors);
      
      expect(selectors.icons).toContain('[aria-label*="notification"]');
      expect(selectors.containers).toContain('header');
    });
  });

  describe('isNotificationIcon', () => {
    let mockElement;

    beforeEach(() => {
      mockElement = {
        matches: jest.fn(),
        getAttribute: jest.fn(),
        querySelector: jest.fn(),
        tagName: 'BUTTON',
        className: '',
        id: '',
        parentElement: null
      };
    });

    test('should detect notification icon by selector match', () => {
      mockElement.matches.mockReturnValue(true);
      
      const result = isNotificationIcon(mockElement, {
        icons: ['button[aria-label*="notification"]']
      });
      
      expect(result).toBe(true);
      expect(mockElement.matches).toHaveBeenCalled();
    });

    test('should detect notification icon by aria-label', () => {
      mockElement.matches.mockReturnValue(false);
      mockElement.getAttribute.mockImplementation((attr) => {
        if (attr === 'aria-label') return 'Open notifications';
        return null;
      });
      
      const result = isNotificationIconByAttributes(mockElement);
      
      expect(result).toBe(true);
    });

    test('should detect notification icon by data-testid', () => {
      mockElement.getAttribute.mockImplementation((attr) => {
        if (attr === 'data-testid') return 'notification-bell';
        return null;
      });
      
      const result = isNotificationIconByAttributes(mockElement);
      
      expect(result).toBe(true);
    });

    test('should detect notification icon by class name', () => {
      mockElement.className = 'icon-notification active';
      
      const result = isNotificationIconByAttributes(mockElement);
      
      expect(result).toBe(true);
    });

    test('should detect notification icon by ID', () => {
      mockElement.id = 'mail-notification';
      
      const result = isNotificationIconByAttributes(mockElement);
      
      expect(result).toBe(true);
    });

    test('should detect notification icon by href', () => {
      mockElement.getAttribute.mockImplementation((attr) => {
        if (attr === 'href') return '/message/inbox';
        return null;
      });
      
      const result = isNotificationIconByAttributes(mockElement);
      
      expect(result).toBe(true);
    });

    test('should not detect non-notification elements', () => {
      mockElement.matches.mockReturnValue(false);
      mockElement.getAttribute.mockReturnValue(null);
      mockElement.className = 'regular-button';
      mockElement.id = 'submit-btn';
      
      const result = isNotificationIcon(mockElement, { icons: [] });
      
      expect(result).toBe(false);
    });
  });

  describe('isNotificationIconByContent', () => {
    let mockElement;

    beforeEach(() => {
      mockElement = {
        getAttribute: jest.fn(),
        querySelector: jest.fn(),
        tagName: 'BUTTON',
        className: ''
      };
    });

    test('should detect by title attribute', () => {
      mockElement.getAttribute.mockImplementation((attr) => {
        if (attr === 'title') return 'View notifications';
        return null;
      });
      
      const result = isNotificationIconByContent(mockElement);
      
      expect(result).toBe(true);
    });

    test('should detect SVG bell icons', () => {
      const mockSvg = {
        getAttribute: jest.fn(() => 'icon-bell'),
        querySelectorAll: jest.fn(() => [])
      };
      mockElement.querySelector.mockReturnValue(mockSvg);
      
      const result = isNotificationIconByContent(mockElement);
      
      expect(result).toBe(true);
    });

    test('should detect font icons', () => {
      mockElement.tagName = 'I';
      mockElement.className = 'fa fa-bell';
      
      const result = isNotificationIconByContent(mockElement);
      
      expect(result).toBe(true);
    });

    test('should not detect non-notification content', () => {
      mockElement.getAttribute.mockReturnValue(null);
      mockElement.querySelector.mockReturnValue(null);
      
      const result = isNotificationIconByContent(mockElement);
      
      expect(result).toBe(false);
    });
  });

  describe('isBellIconPath', () => {
    test('should detect bell-like SVG paths', () => {
      const bellPath = 'M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z';
      
      const result = isBellIconPath(bellPath);
      
      expect(result).toBe(true);
    });

    test('should detect paths with bell reference', () => {
      const pathWithBell = 'M12 2 bell-icon path data';
      
      const result = isBellIconPath(pathWithBell);
      
      expect(result).toBe(true);
    });

    test('should not detect non-bell paths', () => {
      const regularPath = 'M0 0 L10 10 L0 20 Z';
      
      const result = isBellIconPath(regularPath);
      
      expect(result).toBe(false);
    });
  });

  describe('getNotificationIconPosition', () => {
    let mockElement;

    beforeEach(() => {
      mockElement = {
        getBoundingClientRect: jest.fn(() => ({
          top: 50,
          left: 100,
          right: 150,
          bottom: 80,
          width: 50,
          height: 30
        })),
        parentElement: null
      };
      
      window.pageYOffset = 0;
      window.pageXOffset = 0;
    });

    test('should return correct position data', () => {
      const position = getNotificationIconPosition(mockElement);
      
      expect(position).toEqual({
        top: 85, // bottom + 5px margin
        left: 100,
        right: 150,
        width: 50,
        height: 30,
        element: mockElement
      });
    });

    test('should account for page scroll', () => {
      window.pageYOffset = 100;
      window.pageXOffset = 50;
      
      const position = getNotificationIconPosition(mockElement);
      
      expect(position.top).toBe(185); // 80 + 100 + 5
      expect(position.left).toBe(150); // 100 + 50
    });
  });

  describe('positionMiniInbox', () => {
    let mockOverlay, mockContainer;

    beforeEach(() => {
      mockContainer = {
        style: {}
      };
      
      mockOverlay = {
        querySelector: jest.fn(() => mockContainer),
        style: {}
      };
      
      window.innerWidth = 1920;
      window.innerHeight = 1080;
      window.pageYOffset = 0;
    });

    test('should position mini-inbox below icon by default', () => {
      const iconPosition = {
        top: 100,
        left: 200,
        right: 250,
        width: 50,
        height: 30
      };
      
      positionMiniInbox(mockOverlay, iconPosition);
      
      expect(mockContainer.style.top).toBe('100px');
      expect(mockContainer.style.left).toBe('200px');
    });

    test('should adjust position when overflowing right edge', () => {
      const iconPosition = {
        top: 100,
        left: 1600, // Close to right edge
        right: 1650,
        width: 50,
        height: 30
      };
      
      positionMiniInbox(mockOverlay, iconPosition);
      
      expect(mockContainer.style.left).toBe('1250px'); // 1650 - 400 (inbox width)
    });

    test('should position above icon when overflowing bottom', () => {
      const iconPosition = {
        top: 1000, // Close to bottom
        left: 200,
        right: 250,
        width: 50,
        height: 30
      };
      
      positionMiniInbox(mockOverlay, iconPosition);
      
      expect(parseInt(mockContainer.style.top)).toBeLessThan(1000);
    });

    test('should ensure minimum margins', () => {
      const iconPosition = {
        top: 5,
        left: -50, // Negative position
        right: 0,
        width: 50,
        height: 30
      };
      
      positionMiniInbox(mockOverlay, iconPosition);
      
      expect(mockContainer.style.left).toBe('10px'); // Minimum margin
      expect(parseInt(mockContainer.style.top)).toBeGreaterThanOrEqual(10);
    });
  });

  describe('containsNotificationIcon', () => {
    let mockElement;

    beforeEach(() => {
      mockElement = {
        querySelector: jest.fn(),
        matches: jest.fn()
      };
    });

    test('should return true if element is notification icon', () => {
      mockElement.matches.mockReturnValue(true);
      
      const result = containsNotificationIcon(mockElement, {
        icons: ['button[aria-label*="notification"]']
      });
      
      expect(result).toBe(true);
    });

    test('should return true if element contains notification icon', () => {
      mockElement.matches.mockReturnValue(false);
      mockElement.querySelector.mockReturnValue({ tagName: 'BUTTON' });
      
      const result = containsNotificationIcon(mockElement, {
        icons: ['button[aria-label*="notification"]']
      });
      
      expect(result).toBe(true);
    });

    test('should return false if no notification icon found', () => {
      mockElement.matches.mockReturnValue(false);
      mockElement.querySelector.mockReturnValue(null);
      
      const result = containsNotificationIcon(mockElement, { icons: [] });
      
      expect(result).toBe(false);
    });
  });
});

// Helper functions for tests (normally in content script)
function detectRedditVersion() {
  const hostname = window.location.hostname;
  
  if (hostname === 'old.reddit.com') {
    return 'old';
  }
  
  if (hostname === 'www.reddit.com') {
    if (document.querySelector('[data-testid]') || document.querySelector('shreddit-app')) {
      return 'new-react';
    }
    return 'new';
  }
  
  return 'new';
}

function setupNotificationSelectors(version, selectors = {}) {
  selectors.version = version;
  
  switch (version) {
    case 'old':
      selectors.icons = [
        '#mail',
        '.mail',
        'a[href*="/message/inbox"]',
        'a[href="/message/inbox/"]'
      ];
      selectors.containers = [
        '#header-bottom-right',
        '.user'
      ];
      break;
      
    case 'new':
      selectors.icons = [
        '[aria-label*="notification"]',
        '[aria-label*="inbox"]',
        '[data-testid*="notification"]',
        '[data-testid*="inbox"]',
        'a[href*="/message/inbox"]',
        'button[aria-label*="notification"]',
        '.icon-notification',
        '.notification-icon'
      ];
      selectors.containers = [
        '[data-testid="header"]',
        'header',
        '.Header',
        '#SHORTCUT_FOCUSABLE_DIV'
      ];
      break;
      
    case 'new-react':
      selectors.icons = [
        'button[aria-label*="notification"]',
        'button[aria-label*="inbox"]',
        'button[aria-label*="Open inbox"]',
        '[data-testid="notification-bell"]',
        '[data-testid="inbox-button"]',
        'a[href*="/message/inbox"]',
        'shreddit-notification-bell',
        'reddit-header-action-button[aria-label*="notification"]'
      ];
      selectors.containers = [
        'shreddit-app',
        'reddit-header-large',
        'reddit-header-small',
        '[slot="header"]',
        'header'
      ];
      break;
      
    default:
      selectors.icons = [
        '[aria-label*="notification"]',
        '[aria-label*="inbox"]',
        'a[href*="/message/inbox"]',
        'button[aria-label*="notification"]'
      ];
      selectors.containers = [
        'header',
        '[data-testid="header"]'
      ];
  }
  
  return selectors;
}

function isNotificationIcon(element, selectors) {
  if (!element || !selectors) return false;
  
  for (let el = element; el && el !== document; el = el.parentElement) {
    for (const selector of selectors.icons) {
      try {
        if (el.matches && el.matches(selector)) {
          return true;
        }
      } catch (e) {
        continue;
      }
    }
    
    if (isNotificationIconByAttributes(el)) {
      return true;
    }
    
    if (isNotificationIconByContent(el)) {
      return true;
    }
  }
  
  return false;
}

function isNotificationIconByAttributes(element) {
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel) {
    const lowerLabel = ariaLabel.toLowerCase();
    if (lowerLabel.includes('notification') || 
        lowerLabel.includes('inbox') || 
        lowerLabel.includes('message') ||
        lowerLabel.includes('mail')) {
      return true;
    }
  }
  
  const testId = element.getAttribute('data-testid');
  if (testId) {
    const lowerTestId = testId.toLowerCase();
    if (lowerTestId.includes('notification') || 
        lowerTestId.includes('inbox') || 
        lowerTestId.includes('bell')) {
      return true;
    }
  }
  
  const className = element.className;
  if (typeof className === 'string') {
    const lowerClass = className.toLowerCase();
    if (lowerClass.includes('notification') || 
        lowerClass.includes('inbox') || 
        lowerClass.includes('mail') ||
        lowerClass.includes('bell')) {
      return true;
    }
  }
  
  const id = element.id;
  if (id) {
    const lowerId = id.toLowerCase();
    if (lowerId.includes('notification') || 
        lowerId.includes('inbox') || 
        lowerId.includes('mail')) {
      return true;
    }
  }
  
  const href = element.getAttribute('href');
  if (href && href.includes('/message/inbox')) {
    return true;
  }
  
  return false;
}

function isNotificationIconByContent(element) {
  const title = element.getAttribute('title');
  if (title) {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('notification') || 
        lowerTitle.includes('inbox') || 
        lowerTitle.includes('message')) {
      return true;
    }
  }
  
  const svg = element.querySelector('svg') || (element.tagName === 'SVG' ? element : null);
  if (svg) {
    const svgClass = svg.getAttribute('class');
    if (svgClass && (svgClass.includes('bell') || svgClass.includes('notification') || svgClass.includes('mail'))) {
      return true;
    }
    
    const paths = svg.querySelectorAll('path');
    for (const path of paths) {
      const d = path.getAttribute('d');
      if (d && (d.includes('bell') || isBellIconPath(d))) {
        return true;
      }
    }
  }
  
  if (element.tagName === 'I') {
    const className = element.className;
    if (typeof className === 'string') {
      const lowerClass = className.toLowerCase();
      if (lowerClass.includes('bell') || 
          lowerClass.includes('notification') || 
          lowerClass.includes('envelope') ||
          lowerClass.includes('mail')) {
        return true;
      }
    }
  }
  
  return false;
}

function isBellIconPath(pathData) {
  const bellPatterns = [
    /M\s*\d+.*[Cc]\s*\d+.*[Cc]\s*\d+/,
    /[Mm]\s*\d+.*[Ll]\s*\d+.*[Cc]/,
    /bell/i,
    /notification/i
  ];
  
  return bellPatterns.some(pattern => pattern.test(pathData));
}

function getNotificationIconPosition(element) {
  const rect = element.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  
  return {
    top: rect.bottom + scrollTop + 5,
    left: rect.left + scrollLeft,
    right: rect.right + scrollLeft,
    width: rect.width,
    height: rect.height,
    element: element
  };
}

function positionMiniInbox(overlayElement, iconPosition) {
  const container = overlayElement.querySelector('.mini-inbox-container');
  if (!container) return;
  
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  
  const inboxWidth = 400;
  const inboxHeight = 500;
  
  let top = iconPosition.top;
  let left = iconPosition.left;
  
  if (left + inboxWidth > windowWidth) {
    left = iconPosition.right - inboxWidth;
  }
  
  if (top + inboxHeight > windowHeight + scrollTop) {
    top = iconPosition.top - iconPosition.height - inboxHeight - 10;
    
    if (top < scrollTop) {
      top = scrollTop + (windowHeight - inboxHeight) / 2;
    }
  }
  
  left = Math.max(10, left);
  top = Math.max(scrollTop + 10, top);
  
  overlayElement.style.position = 'absolute';
  overlayElement.style.top = '0';
  overlayElement.style.left = '0';
  overlayElement.style.width = '100%';
  overlayElement.style.height = '100%';
  overlayElement.style.backgroundColor = 'transparent';
  overlayElement.style.pointerEvents = 'none';
  
  container.style.position = 'absolute';
  container.style.top = `${top}px`;
  container.style.left = `${left}px`;
  container.style.pointerEvents = 'auto';
  container.style.zIndex = '10001';
}

function containsNotificationIcon(element, selectors) {
  if (isNotificationIcon(element, selectors)) {
    return true;
  }
  
  for (const selector of selectors.icons) {
    try {
      if (element.querySelector && element.querySelector(selector)) {
        return true;
      }
    } catch (e) {
      continue;
    }
  }
  
  return false;
}