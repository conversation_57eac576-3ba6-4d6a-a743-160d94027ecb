// Test d'analyse de l'état actuel de l'extension
console.log('🔍 ANALYSE DE L\'ÉTAT ACTUEL DE L\'EXTENSION');
console.log('='.repeat(60));

const fs = require('fs');

// Test 1: Vérifier l'état des fichiers après autofix
function analyzeFileStates() {
  console.log('\n📁 ANALYSE DES FICHIERS APRÈS AUTOFIX:');
  
  try {
    // Vérifier manifest.json
    const manifest = JSON.parse(fs.readFileSync('./manifest.json', 'utf8'));
    console.log('✅ Manifest.json lu avec succès');
    console.log(`   - Content script: ${manifest.content_scripts[0].js[0]}`);
    console.log(`   - CSS: ${manifest.content_scripts[0].css[0]}`);
    console.log(`   - Run at: ${manifest.content_scripts[0].run_at}`);
    
    // Vérifier content script
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    console.log('✅ Content script lu avec succès');
    console.log(`   - Taille: ${contentScript.length} caractères`);
    console.log(`   - Premières lignes contiennent DEBUG: ${contentScript.includes('DEBUG MODE')}`);
    
    // Vérifier CSS
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    console.log('✅ CSS lu avec succès');
    console.log(`   - Taille: ${cssContent.length} caractères`);
    console.log(`   - Contient !important: ${cssContent.includes('!important')}`);
    
  } catch (error) {
    console.log('❌ Erreur lors de la lecture des fichiers:', error.message);
  }
}

// Test 2: Analyser la syntaxe JavaScript
function analyzeSyntax() {
  console.log('\n🔧 ANALYSE DE LA SYNTAXE JAVASCRIPT:');
  
  try {
    const { execSync } = require('child_process');
    
    // Test content script
    try {
      execSync('node -c scripts/content-new.js', { stdio: 'pipe' });
      console.log('✅ Content script: syntaxe valide');
    } catch (error) {
      console.log('❌ Content script: erreur de syntaxe');
      console.log(`   Erreur: ${error.message}`);
      return false;
    }
    
    // Test background script
    try {
      execSync('node -c scripts/background.js', { stdio: 'pipe' });
      console.log('✅ Background script: syntaxe valide');
    } catch (error) {
      console.log('❌ Background script: erreur de syntaxe');
      console.log(`   Erreur: ${error.message}`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Erreur lors du test de syntaxe:', error.message);
    return false;
  }
}

// Test 3: Analyser les fonctions critiques
function analyzeCriticalFunctions() {
  console.log('\n⚙️ ANALYSE DES FONCTIONS CRITIQUES:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    const criticalFunctions = [
      'function init',
      'function setup',
      'function handleClick',
      'function toggleMiniInbox',
      'function openMiniInbox',
      'function createMiniInboxElement',
      'function displayNotifications',
      'function attachEventListeners',
      'function handleNotificationAction'
    ];
    
    let foundFunctions = 0;
    let missingFunctions = [];
    
    criticalFunctions.forEach(func => {
      if (contentScript.includes(func)) {
        console.log(`✅ ${func}`);
        foundFunctions++;
      } else {
        console.log(`❌ ${func} - MANQUANTE`);
        missingFunctions.push(func);
      }
    });
    
    console.log(`\n📊 Résultat: ${foundFunctions}/${criticalFunctions.length} fonctions trouvées`);
    
    if (missingFunctions.length > 0) {
      console.log('⚠️  FONCTIONS MANQUANTES:');
      missingFunctions.forEach(func => console.log(`   - ${func}`));
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de l\'analyse des fonctions:', error.message);
    return false;
  }
}

// Test 4: Analyser les sélecteurs CSS
function analyzeCSSSelectors() {
  console.log('\n🎨 ANALYSE DES SÉLECTEURS CSS:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    const criticalSelectors = [
      '#reddit-mini-inbox-overlay',
      '.mini-inbox-container',
      '.mini-inbox-header',
      '.mini-inbox-content',
      '.notification-item',
      '.notification-actions',
      '.notification-action'
    ];
    
    let foundSelectors = 0;
    let missingSelectors = [];
    
    criticalSelectors.forEach(selector => {
      if (cssContent.includes(selector)) {
        console.log(`✅ ${selector}`);
        foundSelectors++;
      } else {
        console.log(`❌ ${selector} - MANQUANT`);
        missingSelectors.push(selector);
      }
    });
    
    console.log(`\n📊 Résultat: ${foundSelectors}/${criticalSelectors.length} sélecteurs trouvés`);
    
    // Vérifier les !important
    const importantCount = (cssContent.match(/!important/g) || []).length;
    console.log(`🔧 Nombre de !important: ${importantCount}`);
    
    if (missingSelectors.length > 0) {
      console.log('⚠️  SÉLECTEURS MANQUANTS:');
      missingSelectors.forEach(selector => console.log(`   - ${selector}`));
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de l\'analyse CSS:', error.message);
    return false;
  }
}

// Test 5: Analyser les event listeners
function analyzeEventListeners() {
  console.log('\n👂 ANALYSE DES EVENT LISTENERS:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    const eventListenerPatterns = [
      'document.addEventListener\\(\'click\'',
      'addEventListener\\(\'click\'',
      'querySelectorAll\\(.*notification-action',
      'data-action',
      'data-id',
      'handleNotificationAction',
      'preventDefault',
      'stopPropagation'
    ];
    
    let foundPatterns = 0;
    let missingPatterns = [];
    
    eventListenerPatterns.forEach(pattern => {
      const regex = new RegExp(pattern);
      if (regex.test(contentScript)) {
        console.log(`✅ ${pattern}`);
        foundPatterns++;
      } else {
        console.log(`❌ ${pattern} - MANQUANT`);
        missingPatterns.push(pattern);
      }
    });
    
    console.log(`\n📊 Résultat: ${foundPatterns}/${eventListenerPatterns.length} patterns trouvés`);
    
    if (missingPatterns.length > 0) {
      console.log('⚠️  PATTERNS MANQUANTS:');
      missingPatterns.forEach(pattern => console.log(`   - ${pattern}`));
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de l\'analyse des event listeners:', error.message);
    return false;
  }
}

// Test 6: Analyser les messages de débogage
function analyzeDebugMessages() {
  console.log('\n🐛 ANALYSE DES MESSAGES DE DÉBOGAGE:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    const debugPatterns = [
      'console.log.*DEBUG',
      'console.log.*Attaching event listeners',
      'console.log.*Action button clicked',
      'console.log.*Event listeners attached successfully'
    ];
    
    let foundDebug = 0;
    
    debugPatterns.forEach(pattern => {
      const regex = new RegExp(pattern);
      const matches = contentScript.match(new RegExp(pattern, 'g'));
      if (matches) {
        console.log(`✅ ${pattern}: ${matches.length} occurrence(s)`);
        foundDebug++;
      } else {
        console.log(`❌ ${pattern}: 0 occurrence`);
      }
    });
    
    console.log(`\n📊 Résultat: ${foundDebug}/${debugPatterns.length} types de debug trouvés`);
    
    return foundDebug > 0;
  } catch (error) {
    console.log('❌ Erreur lors de l\'analyse du débogage:', error.message);
    return false;
  }
}

// Test 7: Identifier les problèmes potentiels
function identifyPotentialIssues() {
  console.log('\n🚨 IDENTIFICATION DES PROBLÈMES POTENTIELS:');
  
  const issues = [];
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Vérifier les caractères Unicode problématiques
    const unicodeChars = ['📬', '↻', '×', '🧪', '🔐'];
    unicodeChars.forEach(char => {
      if (contentScript.includes(char)) {
        issues.push(`Caractère Unicode potentiellement problématique: ${char}`);
      }
    });
    
    // Vérifier les template literals
    const backtickCount = (contentScript.match(/`/g) || []).length;
    if (backtickCount % 2 !== 0) {
      issues.push('Nombre impair de backticks - template literal non fermé');
    }
    
    // Vérifier les accolades
    const openBraces = (contentScript.match(/\{/g) || []).length;
    const closeBraces = (contentScript.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
      issues.push(`Accolades non équilibrées: ${openBraces} ouvertes, ${closeBraces} fermées`);
    }
    
    // Vérifier les parenthèses
    const openParens = (contentScript.match(/\(/g) || []).length;
    const closeParens = (contentScript.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push(`Parenthèses non équilibrées: ${openParens} ouvertes, ${closeParens} fermées`);
    }
    
    // Vérifier les z-index
    if (!cssContent.includes('z-index: 999999')) {
      issues.push('Z-index élevé manquant dans le CSS');
    }
    
    // Vérifier les !important critiques
    const criticalImportant = [
      'width.*!important',
      'display.*!important',
      'position.*!important'
    ];
    
    criticalImportant.forEach(pattern => {
      const regex = new RegExp(pattern);
      if (!regex.test(cssContent)) {
        issues.push(`!important manquant pour: ${pattern}`);
      }
    });
    
  } catch (error) {
    issues.push(`Erreur lors de l'analyse: ${error.message}`);
  }
  
  if (issues.length === 0) {
    console.log('✅ Aucun problème évident détecté');
  } else {
    console.log(`⚠️  ${issues.length} problème(s) potentiel(s) détecté(s):`);
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }
  
  return issues;
}

// Exécuter tous les tests d'analyse
function runCompleteAnalysis() {
  console.log('🔍 DÉMARRAGE DE L\'ANALYSE COMPLÈTE...\n');
  
  const results = {
    files: analyzeFileStates(),
    syntax: analyzeSyntax(),
    functions: analyzeCriticalFunctions(),
    css: analyzeCSSSelectors(),
    events: analyzeEventListeners(),
    debug: analyzeDebugMessages(),
    issues: identifyPotentialIssues()
  };
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 RÉSUMÉ DE L\'ANALYSE:');
  console.log('='.repeat(60));
  
  Object.entries(results).forEach(([test, result]) => {
    if (test === 'issues') {
      console.log(`${test.toUpperCase()}: ${result.length} problème(s) détecté(s)`);
    } else {
      console.log(`${test.toUpperCase()}: ${result ? '✅ OK' : '❌ PROBLÈME'}`);
    }
  });
  
  console.log('\n🎯 CONCLUSION:');
  if (results.syntax && results.functions && results.css && results.events) {
    console.log('✅ Structure de base correcte');
    if (results.issues.length > 0) {
      console.log('⚠️  Mais des problèmes spécifiques ont été détectés (voir ci-dessus)');
    } else {
      console.log('🤔 Problème probablement lié à l\'exécution runtime ou aux conflits CSS');
    }
  } else {
    console.log('❌ Problèmes structurels détectés - l\'extension ne peut pas fonctionner');
  }
  
  return results;
}

// Exécuter l'analyse
runCompleteAnalysis();