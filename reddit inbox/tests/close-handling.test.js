// Tests pour la gestion de fermeture de la mini-inbox
// Teste tous les mécanismes de fermeture selon les exigences 3.1-3.4

describe('Close Handling', () => {
  let mockChrome;
  let contentScript;
  let miniInboxElement;
  
  beforeEach(() => {
    // Mock Chrome API
    mockChrome = {
      runtime: {
        sendMessage: jest.fn().mockResolvedValue({
          success: true,
          notifications: []
        })
      }
    };
    global.chrome = mockChrome;
    
    // Setup DOM
    document.body.innerHTML = '';
    
    // Mock console pour éviter les logs pendant les tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Simuler les variables globales du content script
    global.miniInboxOpen = false;
    global.miniInboxElement = null;
    
    // Mock des fonctions du content script
    global.closeMiniInbox = jest.fn(() => {
      if (global.miniInboxElement) {
        global.miniInboxElement.classList.remove('show');
        const container = global.miniInboxElement.querySelector('.mini-inbox-container');
        if (container) {
          container.classList.remove('opening', 'show');
          container.classList.add('closing');
        }
        setTimeout(() => {
          if (global.miniInboxElement && global.miniInboxElement.parentNode) {
            global.miniInboxElement.remove();
          }
          global.miniInboxElement = null;
          miniInboxElement = null;
        }, 150);
      }
      global.miniInboxOpen = false;
      window.miniInboxOpen = false;
    });
    
    // Créer une mini-inbox pour les tests
    createTestMiniInbox();
  });
  
  afterEach(() => {
    // Nettoyer le DOM
    document.body.innerHTML = '';
    
    // Restaurer les mocks
    jest.restoreAllMocks();
    
    // Nettoyer les variables globales
    if (typeof miniInboxOpen !== 'undefined') {
      miniInboxOpen = false;
    }
    if (typeof miniInboxElement !== 'undefined') {
      miniInboxElement = null;
    }
  });
  
  function createTestMiniInbox() {
    const overlay = document.createElement('div');
    overlay.id = 'reddit-mini-inbox-overlay';
    overlay.classList.add('show');
    
    overlay.innerHTML = `
      <div class="mini-inbox-container show">
        <div class="mini-inbox-header">
          <div class="mini-inbox-title">
            <span class="mini-inbox-icon">📬</span>
            <span>inbox</span>
          </div>
          <button class="close-btn" type="button" aria-label="Fermer" title="Fermer">×</button>
        </div>
        <div class="mini-inbox-content">
          <div class="notification-item">Test notification</div>
        </div>
      </div>
    `;
    
    document.body.appendChild(overlay);
    miniInboxElement = overlay;
    global.miniInboxElement = overlay;
    
    // Simuler l'état ouvert
    global.miniInboxOpen = true;
    window.miniInboxOpen = true;
    
    // Ajouter les event listeners comme dans le vrai code
    const closeBtn = overlay.querySelector('.close-btn');
    closeBtn.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      global.closeMiniInbox();
    });
    
    // Fermer au clic en dehors
    overlay.addEventListener('click', (event) => {
      if (event.target === overlay) {
        global.closeMiniInbox();
      }
    });
    
    // Support clavier
    overlay.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        event.preventDefault();
        global.closeMiniInbox();
      }
    });
    
    // Event listener global pour Escape
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && global.miniInboxOpen) {
        global.closeMiniInbox();
      }
    });
    
    return overlay;
  }
  
  // Test 3.1: Bouton de fermeture visible
  describe('Close Button', () => {
    test('should have visible close button', () => {
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      expect(closeBtn).toBeTruthy();
      expect(closeBtn.type).toBe('button');
      expect(closeBtn.getAttribute('aria-label')).toBe('Fermer');
      expect(closeBtn.textContent).toBe('×');
    });
    
    test('should close mini-inbox when close button is clicked', (done) => {
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      // Vérifier que la mini-inbox est ouverte
      expect(miniInboxElement.classList.contains('show')).toBe(true);
      
      // Simuler le clic sur le bouton de fermeture
      closeBtn.click();
      
      // Vérifier que l'animation de fermeture commence
      setTimeout(() => {
        expect(miniInboxElement.classList.contains('show')).toBe(false);
        const container = miniInboxElement.querySelector('.mini-inbox-container');
        expect(container.classList.contains('closing')).toBe(true);
        done();
      }, 10);
    });
    
    test('should remove mini-inbox element after close animation', (done) => {
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      closeBtn.click();
      
      // Attendre la fin de l'animation (150ms selon le code)
      setTimeout(() => {
        expect(document.getElementById('reddit-mini-inbox-overlay')).toBeNull();
        done();
      }, 200);
    });
  });
  
  // Test 3.2: Fermeture au clic en dehors
  describe('Click Outside to Close', () => {
    test('should close when clicking on overlay background', (done) => {
      // Vérifier que la mini-inbox est ouverte
      expect(miniInboxElement.classList.contains('show')).toBe(true);
      
      // Simuler un clic sur l'overlay (en dehors du contenu)
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      });
      
      // Le clic doit être sur l'overlay lui-même, pas sur le contenu
      Object.defineProperty(clickEvent, 'target', {
        value: miniInboxElement,
        enumerable: true
      });
      
      miniInboxElement.dispatchEvent(clickEvent);
      
      setTimeout(() => {
        expect(miniInboxElement.classList.contains('show')).toBe(false);
        done();
      }, 10);
    });
    
    test('should NOT close when clicking inside content area', () => {
      const content = miniInboxElement.querySelector('.mini-inbox-content');
      
      // Simuler un clic à l'intérieur du contenu
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      });
      
      Object.defineProperty(clickEvent, 'target', {
        value: content,
        enumerable: true
      });
      
      content.dispatchEvent(clickEvent);
      
      // La mini-inbox devrait rester ouverte
      expect(miniInboxElement.classList.contains('show')).toBe(true);
    });
    
    test('should NOT close when clicking on notification items', () => {
      const notification = miniInboxElement.querySelector('.notification-item');
      
      // Simuler un clic sur une notification
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      });
      
      Object.defineProperty(clickEvent, 'target', {
        value: notification,
        enumerable: true
      });
      
      notification.dispatchEvent(clickEvent);
      
      // La mini-inbox devrait rester ouverte
      expect(miniInboxElement.classList.contains('show')).toBe(true);
    });
  });
  
  // Test 3.3: Fermeture avec la touche Échap
  describe('Escape Key to Close', () => {
    test('should close when Escape key is pressed on document', (done) => {
      // Vérifier que la mini-inbox est ouverte
      expect(miniInboxElement.classList.contains('show')).toBe(true);
      
      // Simuler la pression de la touche Échap
      const escapeEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
        code: 'Escape',
        keyCode: 27,
        bubbles: true,
        cancelable: true
      });
      
      document.dispatchEvent(escapeEvent);
      
      setTimeout(() => {
        expect(miniInboxElement.classList.contains('show')).toBe(false);
        done();
      }, 10);
    });
    
    test('should close when Escape key is pressed on mini-inbox element', (done) => {
      // Simuler la pression de la touche Échap sur la mini-inbox
      const escapeEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
        code: 'Escape',
        keyCode: 27,
        bubbles: true,
        cancelable: true
      });
      
      miniInboxElement.dispatchEvent(escapeEvent);
      
      setTimeout(() => {
        expect(miniInboxElement.classList.contains('show')).toBe(false);
        done();
      }, 10);
    });
    
    test('should NOT close when other keys are pressed', () => {
      // Tester différentes touches
      const keys = ['Enter', 'Space', 'Tab', 'ArrowDown', 'ArrowUp'];
      
      keys.forEach(key => {
        const keyEvent = new KeyboardEvent('keydown', {
          key: key,
          bubbles: true,
          cancelable: true
        });
        
        document.dispatchEvent(keyEvent);
        
        // La mini-inbox devrait rester ouverte
        expect(miniInboxElement.classList.contains('show')).toBe(true);
      });
    });
  });
  
  // Test 3.4: Toggle - fermer si déjà ouverte
  describe('Toggle Functionality', () => {
    test('should close mini-inbox when notification icon is clicked while open', () => {
      // Créer une icône de notification factice
      const notificationIcon = document.createElement('button');
      notificationIcon.setAttribute('aria-label', 'Open inbox');
      notificationIcon.className = 'notification-icon';
      document.body.appendChild(notificationIcon);
      
      // Vérifier que la mini-inbox est ouverte
      expect(miniInboxElement.classList.contains('show')).toBe(true);
      
      // Simuler un clic sur l'icône de notification
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      });
      
      Object.defineProperty(clickEvent, 'target', {
        value: notificationIcon,
        enumerable: true
      });
      
      // Simuler la logique de toggle
      if (window.miniInboxOpen) {
        miniInboxElement.classList.remove('show');
        window.miniInboxOpen = false;
      }
      
      expect(miniInboxElement.classList.contains('show')).toBe(false);
    });
  });
  
  // Tests de nettoyage des ressources
  describe('Resource Cleanup', () => {
    test('should clean up event listeners when closing', (done) => {
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
      
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      closeBtn.click();
      
      setTimeout(() => {
        // Vérifier que les event listeners sont nettoyés
        // (Ceci dépend de l'implémentation spécifique du nettoyage)
        expect(document.getElementById('reddit-mini-inbox-overlay')).toBeNull();
        done();
      }, 200);
    });
    
    test('should reset miniInboxOpen state when closing', (done) => {
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      // Vérifier l'état initial
      if (typeof window.miniInboxOpen !== 'undefined') {
        expect(window.miniInboxOpen).toBe(true);
      }
      
      closeBtn.click();
      
      setTimeout(() => {
        if (typeof window.miniInboxOpen !== 'undefined') {
          expect(window.miniInboxOpen).toBe(false);
        }
        done();
      }, 200);
    });
  });
  
  // Tests d'accessibilité
  describe('Accessibility', () => {
    test('close button should have proper ARIA attributes', () => {
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      expect(closeBtn.getAttribute('aria-label')).toBe('Fermer');
      expect(closeBtn.getAttribute('title')).toBe('Fermer');
      expect(closeBtn.type).toBe('button');
    });
    
    test('should support keyboard navigation for close button', () => {
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      // Le bouton devrait être focusable
      closeBtn.focus();
      expect(document.activeElement).toBe(closeBtn);
      
      // Simuler la pression d'Entrée sur le bouton
      const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        bubbles: true,
        cancelable: true
      });
      
      closeBtn.dispatchEvent(enterEvent);
      
      // Le bouton devrait déclencher la fermeture
      // (Ceci dépend de l'implémentation du gestionnaire d'événements)
    });
  });
  
  // Tests de performance
  describe('Performance', () => {
    test('should close quickly without blocking UI', (done) => {
      const startTime = performance.now();
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      closeBtn.click();
      
      const clickTime = performance.now();
      
      // Le clic devrait être traité rapidement (< 10ms)
      expect(clickTime - startTime).toBeLessThan(10);
      
      setTimeout(() => {
        const endTime = performance.now();
        
        // L'animation complète devrait prendre environ 150ms (avec une marge pour les tests)
        expect(endTime - startTime).toBeLessThan(250);
        expect(endTime - startTime).toBeGreaterThan(100);
        
        done();
      }, 200);
    });
  });
  
  // Tests d'intégration
  describe('Integration', () => {
    test('should work with multiple rapid close attempts', (done) => {
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      // Cliquer plusieurs fois rapidement
      closeBtn.click();
      closeBtn.click();
      closeBtn.click();
      
      // Vérifier que ça ne cause pas d'erreurs
      setTimeout(() => {
        expect(document.getElementById('reddit-mini-inbox-overlay')).toBeNull();
        done();
      }, 200);
    });
    
    test('should handle close during loading state', () => {
      // Ajouter l'état de chargement
      const content = miniInboxElement.querySelector('.mini-inbox-content');
      content.classList.add('loading-state');
      content.innerHTML = '<div class="loading-spinner"></div>';
      
      const closeBtn = miniInboxElement.querySelector('.close-btn');
      
      // Fermer pendant le chargement ne devrait pas causer d'erreur
      expect(() => {
        closeBtn.click();
      }).not.toThrow();
    });
  });
});