// Test de compatibilité et responsive design
const fs = require('fs');

console.log('Test de compatibilité et responsive...');

// Test des sélecteurs Reddit
function testRedditSelectors() {
  console.log('\nTest des sélecteurs Reddit...');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    // Extraire les sélecteurs définis dans REDDIT_SELECTORS
    const selectorsMatch = contentScript.match(/const REDDIT_SELECTORS = \{[\s\S]*?\};/);
    
    if (selectorsMatch) {
      console.log('✅ REDDIT_SELECTORS trouvé');
      
      // Vérifier les sélecteurs pour old.reddit.com
      const oldRedditSelectors = [
        '#mail',
        '.mail',
        'a[href*="/message/inbox"]'
      ];
      
      // Vérifier les sélecteurs pour www.reddit.com
      const newRedditSelectors = [
        '[aria-label*="notification"]',
        '[aria-label*="inbox"]',
        'button[aria-label*="notification"]'
      ];
      
      let oldFound = 0, newFound = 0;
      
      oldRedditSelectors.forEach(selector => {
        if (contentScript.includes(selector)) {
          console.log(`✅ Old Reddit: ${selector}`);
          oldFound++;
        } else {
          console.log(`❌ Old Reddit manquant: ${selector}`);
        }
      });
      
      newRedditSelectors.forEach(selector => {
        if (contentScript.includes(selector)) {
          console.log(`✅ New Reddit: ${selector}`);
          newFound++;
        } else {
          console.log(`❌ New Reddit manquant: ${selector}`);
        }
      });
      
      console.log(`Old Reddit: ${oldFound}/${oldRedditSelectors.length} sélecteurs`);
      console.log(`New Reddit: ${newFound}/${newRedditSelectors.length} sélecteurs`);
      
    } else {
      console.log('❌ REDDIT_SELECTORS non trouvé');
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

// Test du CSS responsive
function testResponsiveCSS() {
  console.log('\nTest du CSS responsive...');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Vérifier les media queries
    const mediaQueries = cssContent.match(/@media[^{]+\{[^}]*\}/g) || [];
    console.log(`Nombre de media queries: ${mediaQueries.length}`);
    
    // Vérifier les breakpoints courants
    const breakpoints = [
      'max-width: 480px',
      'max-width: 768px',
      'max-width: 1024px'
    ];
    
    breakpoints.forEach(breakpoint => {
      if (cssContent.includes(breakpoint)) {
        console.log(`✅ Breakpoint trouvé: ${breakpoint}`);
      } else {
        console.log(`❌ Breakpoint manquant: ${breakpoint}`);
      }
    });
    
    // Vérifier les propriétés responsive importantes
    const responsiveProps = [
      'max-width: 90vw',
      'max-width: 95vw',
      'max-height: 70vh',
      'max-height: 80vh'
    ];
    
    responsiveProps.forEach(prop => {
      if (cssContent.includes(prop)) {
        console.log(`✅ Propriété responsive: ${prop}`);
      } else {
        console.log(`❌ Propriété responsive manquante: ${prop}`);
      }
    });
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

// Test de la détection de thème
function testThemeDetection() {
  console.log('\nTest de détection de thème...');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    if (contentScript.includes('function detectDarkTheme')) {
      console.log('✅ Fonction detectDarkTheme trouvée');
      
      // Vérifier les méthodes de détection
      const detectionMethods = [
        'prefers-color-scheme: dark',
        'classList.contains(\'dark\')',
        'data-theme',
        'matchMedia'
      ];
      
      detectionMethods.forEach(method => {
        if (contentScript.includes(method)) {
          console.log(`✅ Méthode de détection: ${method}`);
        } else {
          console.log(`❌ Méthode manquante: ${method}`);
        }
      });
      
    } else {
      console.log('❌ Fonction detectDarkTheme manquante');
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

// Test du positionnement
function testPositioning() {
  console.log('\nTest du positionnement...');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    if (contentScript.includes('function positionMiniInbox')) {
      console.log('✅ Fonction positionMiniInbox trouvée');
      
      // Vérifier les calculs de position
      const positioningFeatures = [
        'getBoundingClientRect',
        'window.innerWidth',
        'window.innerHeight',
        'scrollTop',
        'scrollLeft'
      ];
      
      positioningFeatures.forEach(feature => {
        if (contentScript.includes(feature)) {
          console.log(`✅ Feature de positionnement: ${feature}`);
        } else {
          console.log(`❌ Feature manquante: ${feature}`);
        }
      });
      
    } else {
      console.log('❌ Fonction positionMiniInbox manquante');
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

// Test des animations
function testAnimations() {
  console.log('\nTest des animations...');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Vérifier les keyframes
    const keyframes = cssContent.match(/@keyframes[^{]+/g) || [];
    console.log(`Nombre d'animations: ${keyframes.length}`);
    
    keyframes.forEach(keyframe => {
      console.log(`✅ Animation: ${keyframe.trim()}`);
    });
    
    // Vérifier les transitions
    const transitionCount = (cssContent.match(/transition:/g) || []).length;
    console.log(`Nombre de transitions: ${transitionCount}`);
    
    // Vérifier les classes d'animation
    const animationClasses = [
      '.opening',
      '.closing',
      '.show',
      '.loading'
    ];
    
    animationClasses.forEach(className => {
      if (cssContent.includes(className)) {
        console.log(`✅ Classe d'animation: ${className}`);
      } else {
        console.log(`❌ Classe d'animation manquante: ${className}`);
      }
    });
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

// Test de la structure HTML générée
function testHTMLStructure() {
  console.log('\nTest de la structure HTML...');
  
  // Simuler la génération de HTML
  const mockHTML = `
    <div class="mini-inbox-container opening">
      <div class="mini-inbox-header">
        <div class="mini-inbox-title">
          <span class="mini-inbox-icon">📬</span>
          <span>inbox</span>
        </div>
        <div class="mini-inbox-actions">
          <button class="refresh-button">↻</button>
          <button class="close-btn">×</button>
        </div>
      </div>
      <div class="mini-inbox-content">
        <div class="notification-item unread">
          <div class="notification-header">
            <a class="notification-author">test_user</a>
            <span class="notification-time">1h</span>
          </div>
          <div class="notification-subject">Test Subject</div>
          <div class="notification-body">Test body</div>
          <div class="notification-actions">
            <a class="notification-action" data-action="reply">reply</a>
            <a class="notification-action" data-action="mark-read">mark as read</a>
          </div>
        </div>
      </div>
    </div>
  `;
  
  // Analyser la structure
  const elements = {
    'mini-inbox-container': (mockHTML.match(/class="[^"]*mini-inbox-container/g) || []).length,
    'mini-inbox-header': (mockHTML.match(/class="[^"]*mini-inbox-header/g) || []).length,
    'mini-inbox-content': (mockHTML.match(/class="[^"]*mini-inbox-content/g) || []).length,
    'notification-item': (mockHTML.match(/class="[^"]*notification-item/g) || []).length,
    'notification-actions': (mockHTML.match(/class="[^"]*notification-actions/g) || []).length,
    'notification-action': (mockHTML.match(/class="[^"]*notification-action/g) || []).length
  };
  
  Object.entries(elements).forEach(([className, count]) => {
    console.log(`${className}: ${count} occurrence(s)`);
  });
  
  // Vérifier les attributs data
  const dataAttributes = (mockHTML.match(/data-[a-z-]+="/g) || []).length;
  console.log(`Attributs data-*: ${dataAttributes}`);
  
  return mockHTML;
}

// Exécuter tous les tests
testRedditSelectors();
testResponsiveCSS();
testThemeDetection();
testPositioning();
testAnimations();
testHTMLStructure();

console.log('\nTest de compatibilité terminé.');