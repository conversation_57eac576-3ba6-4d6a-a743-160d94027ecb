// Test pour localiser précisément le display: inline problématique
console.log('🎯 LOCALISATION DU DISPLAY: INLINE PROBLÉMATIQUE');
console.log('='.repeat(60));

const fs = require('fs');

function findInlineDisplay() {
  console.log('\n🔍 RECHERCHE DU DISPLAY: INLINE:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    const lines = cssContent.split('\n');
    
    let foundInline = [];
    
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      if (line.includes('display') && line.includes('inline')) {
        foundInline.push({
          line: lineNum,
          content: line.trim(),
          context: lines.slice(Math.max(0, index - 2), index + 3)
        });
      }
    });
    
    if (foundInline.length > 0) {
      console.log(`🚨 Trouvé ${foundInline.length} occurrence(s) de display: inline:`);
      
      foundInline.forEach((item, idx) => {
        console.log(`\n📍 Occurrence ${idx + 1} - Ligne ${item.line}:`);
        console.log(`   Code: ${item.content}`);
        console.log(`   Contexte:`);
        item.context.forEach((contextLine, contextIdx) => {
          const contextLineNum = item.line - 2 + contextIdx;
          const marker = contextIdx === 2 ? '>>> ' : '    ';
          console.log(`   ${marker}${contextLineNum}: ${contextLine.trim()}`);
        });
      });
    } else {
      console.log('✅ Aucun display: inline trouvé');
    }
    
    return foundInline;
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return [];
  }
}

function analyzeFlexboxUsage() {
  console.log('\n📦 ANALYSE DE L\'UTILISATION DE FLEXBOX:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Rechercher tous les usages de flexbox
    const flexPatterns = [
      { pattern: 'display\\s*:\\s*flex', desc: 'Display flex' },
      { pattern: 'flex-direction\\s*:\\s*column', desc: 'Flex direction column' },
      { pattern: 'flex-direction\\s*:\\s*row', desc: 'Flex direction row' },
      { pattern: 'flex-wrap\\s*:\\s*wrap', desc: 'Flex wrap' },
      { pattern: 'justify-content', desc: 'Justify content' },
      { pattern: 'align-items', desc: 'Align items' },
      { pattern: 'flex\\s*:\\s*1', desc: 'Flex grow' }
    ];
    
    console.log('🔍 Analyse des propriétés flexbox:');
    
    flexPatterns.forEach(({ pattern, desc }) => {
      const regex = new RegExp(pattern, 'gi');
      const matches = cssContent.match(regex);
      if (matches) {
        console.log(`📊 ${desc}: ${matches.length} occurrence(s)`);
        
        // Trouver les contextes
        const lines = cssContent.split('\n');
        let contexts = [];
        
        lines.forEach((line, index) => {
          if (regex.test(line)) {
            contexts.push({
              line: index + 1,
              content: line.trim(),
              before: lines[index - 1]?.trim() || '',
              after: lines[index + 1]?.trim() || ''
            });
          }
        });
        
        // Afficher les premiers contextes
        contexts.slice(0, 3).forEach(context => {
          console.log(`   Ligne ${context.line}: ${context.content}`);
          if (context.before.includes('.notification')) {
            console.log(`     ⚠️  Appliqué aux notifications!`);
          }
        });
      } else {
        console.log(`✅ ${desc}: non trouvé`);
      }
    });
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

function checkNotificationActionsLayout() {
  console.log('\n🎬 ANALYSE DU LAYOUT DES NOTIFICATION-ACTIONS:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Extraire les règles pour .notification-actions
    const actionsRegex = /\.notification-actions[^{]*\{[^}]*\}/g;
    const actionsMatches = cssContent.match(actionsRegex);
    
    if (actionsMatches) {
      console.log(`✅ Trouvé ${actionsMatches.length} règle(s) pour .notification-actions:`);
      
      actionsMatches.forEach((rule, index) => {
        console.log(`\n📋 Règle ${index + 1}:`);
        console.log(rule);
        
        // Vérifier les propriétés de layout
        const layoutProps = ['display', 'flex-direction', 'flex-wrap', 'gap'];
        layoutProps.forEach(prop => {
          if (rule.includes(prop)) {
            const propRegex = new RegExp(`${prop}\\s*:\\s*([^;]+)`, 'gi');
            const propMatches = rule.match(propRegex);
            if (propMatches) {
              propMatches.forEach(match => {
                console.log(`   🎯 ${match.trim()}`);
              });
            }
          }
        });
      });
    } else {
      console.log('❌ Aucune règle trouvée pour .notification-actions');
    }
    
    // Vérifier aussi .notification-action (singulier)
    const actionRegex = /\.notification-action[^{]*\{[^}]*\}/g;
    const actionMatches = cssContent.match(actionRegex);
    
    if (actionMatches) {
      console.log(`\n✅ Trouvé ${actionMatches.length} règle(s) pour .notification-action:`);
      
      actionMatches.forEach((rule, index) => {
        console.log(`\n📋 Règle ${index + 1}:`);
        console.log(rule.substring(0, 150) + (rule.length > 150 ? '...' : ''));
        
        // Vérifier display inline
        if (rule.includes('display') && rule.includes('inline')) {
          console.log(`   🚨 PROBLÈME TROUVÉ: display inline dans .notification-action!`);
        }
      });
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

function identifyExactProblem() {
  console.log('\n🎯 IDENTIFICATION DU PROBLÈME EXACT:');
  
  const inlineDisplays = findInlineDisplay();
  analyzeFlexboxUsage();
  checkNotificationActionsLayout();
  
  console.log('\n' + '='.repeat(50));
  console.log('🎯 DIAGNOSTIC FINAL:');
  console.log('='.repeat(50));
  
  if (inlineDisplays.length > 0) {
    console.log('🚨 PROBLÈME IDENTIFIÉ:');
    console.log(`   ${inlineDisplays.length} occurrence(s) de display: inline trouvée(s)`);
    
    inlineDisplays.forEach((item, idx) => {
      console.log(`   ${idx + 1}. Ligne ${item.line}: ${item.content}`);
    });
    
    console.log('\n💡 SOLUTION:');
    console.log('   Remplacer display: inline par display: inline-block ou display: block');
    console.log('   selon le contexte pour éviter le layout en colonnes');
  } else {
    console.log('🤔 Aucun display: inline évident trouvé');
    console.log('   Le problème pourrait être:');
    console.log('   1. CSS de Reddit qui interfère');
    console.log('   2. JavaScript qui modifie les styles');
    console.log('   3. Problème de spécificité CSS');
  }
  
  return inlineDisplays;
}

// Exécuter l'analyse
identifyExactProblem();