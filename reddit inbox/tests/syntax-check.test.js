// Test spécifique pour vérifier l'erreur de syntaxe
const fs = require('fs');

console.log('Vérification de l\'erreur de syntaxe...');

try {
  const content = fs.readFileSync('./scripts/content-new.js', 'utf8');
  const lines = content.split('\n');
  
  console.log('Vérification ligne par ligne autour de la ligne 153...');
  
  // Vérifier les lignes 150-160
  for (let i = 149; i < Math.min(160, lines.length); i++) {
    const lineNum = i + 1;
    const line = lines[i];
    
    console.log(`Ligne ${lineNum}: ${line}`);
    
    // Vérifier les problèmes courants
    if (line.includes('mini') && lineNum === 153) {
      console.log(`⚠️  Ligne 153 contient 'mini': "${line}"`);
    }
    
    // Vérifier les caractères problématiques
    if (line.includes('📬') || line.includes('↻') || line.includes('×')) {
      console.log(`⚠️  Ligne ${lineNum} contient des caractères Unicode`);
    }
    
    // Vérifier les template literals non fermés
    const backticks = (line.match(/`/g) || []).length;
    if (backticks % 2 !== 0) {
      console.log(`⚠️  Ligne ${lineNum} a un nombre impair de backticks`);
    }
  }
  
  // Test de compilation spécifique autour de la ligne 153
  console.log('\nTest de compilation des lignes 150-160...');
  const testCode = lines.slice(149, 160).join('\n');
  
  try {
    new Function(testCode);
    console.log('✅ Lignes 150-160 compilent correctement');
  } catch (error) {
    console.log('❌ Erreur de compilation:', error.message);
  }
  
  // Vérifier le fichier complet
  console.log('\nTest de compilation du fichier complet...');
  try {
    new Function(content);
    console.log('✅ Fichier complet compile correctement');
  } catch (error) {
    console.log('❌ Erreur de compilation du fichier complet:', error.message);
    console.log('Position:', error.lineNumber || 'inconnue');
  }
  
} catch (error) {
  console.log('❌ Erreur lors de la lecture du fichier:', error.message);
}

console.log('\nVérification terminée.');