// Test de vérification des corrections
console.log('🔍 VÉRIFICATION DES CORRECTIONS');
console.log('='.repeat(60));

const fs = require('fs');

// Vérifier l'équilibre des parenthèses
function checkParenthesesBalance() {
  console.log('\n📊 VÉRIFICATION DE L\'ÉQUILIBRE DES PARENTHÈSES:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    // Compter les parenthèses
    const openParens = (contentScript.match(/\(/g) || []).length;
    const closeParens = (contentScript.match(/\)/g) || []).length;
    
    console.log(`Parenthèses ouvertes: ${openParens}`);
    console.log(`Parenthèses fermées: ${closeParens}`);
    console.log(`Balance: ${openParens - closeParens}`);
    
    if (openParens === closeParens) {
      console.log('✅ CORRIGÉ: Les parenthèses sont équilibrées!');
      return true;
    } else {
      console.log('❌ NON CORRIGÉ: Les parenthèses sont toujours déséquilibrées');
      return false;
    }
  } catch (error) {
    console.log('❌ Erreur lors de la vérification:', error.message);
    return false;
  }
}

// Vérifier la fonction isNotificationIconByAttributes
function checkProblemFunction() {
  console.log('\n🔍 VÉRIFICATION DE LA FONCTION PROBLÉMATIQUE:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    // Chercher la ligne problématique
    let foundProblem = false;
    let lineContent = '';
    
    for (let i = 780; i < 790; i++) {
      if (lines[i] && lines[i].includes('className.includes(\'bell\')))')) {
        foundProblem = true;
        lineContent = lines[i].trim();
        console.log(`Ligne ${i + 1}: ${lineContent}`);
        break;
      }
    }
    
    if (foundProblem) {
      console.log('❌ NON CORRIGÉ: La parenthèse supplémentaire est toujours présente');
      return false;
    } else {
      console.log('✅ CORRIGÉ: La parenthèse supplémentaire a été supprimée');
      return true;
    }
  } catch (error) {
    console.log('❌ Erreur lors de la vérification:', error.message);
    return false;
  }
}

// Vérifier les caractères Unicode
function checkUnicodeCharacters() {
  console.log('\n🔤 VÉRIFICATION DES CARACTÈRES UNICODE:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    const unicodeChars = [
      { char: '📬', name: 'mailbox emoji' },
      { char: '↻', name: 'refresh symbol' },
      { char: '×', name: 'multiplication sign' },
      { char: '🧪', name: 'test tube emoji' },
      { char: '🔐', name: 'lock emoji' }
    ];
    
    let foundChars = [];
    
    unicodeChars.forEach(({ char, name }) => {
      if (contentScript.includes(char)) {
        foundChars.push({ char, name });
        console.log(`⚠️ ${name} (${char}) toujours présent`);
      } else {
        console.log(`✅ ${name} (${char}) remplacé`);
      }
    });
    
    if (foundChars.length > 0) {
      console.log(`⚠️ ${foundChars.length} caractères Unicode toujours présents`);
    } else {
      console.log('✅ Tous les caractères Unicode ont été remplacés');
    }
    
    return foundChars.length === 0;
  } catch (error) {
    console.log('❌ Erreur lors de la vérification:', error.message);
    return false;
  }
}

// Vérifier la syntaxe JavaScript
function checkJavaScriptSyntax() {
  console.log('\n🔧 VÉRIFICATION DE LA SYNTAXE JAVASCRIPT:');
  
  try {
    const { execSync } = require('child_process');
    
    try {
      execSync('node -c scripts/content-new.js', { stdio: 'pipe' });
      console.log('✅ CORRIGÉ: La syntaxe JavaScript est valide!');
      return true;
    } catch (error) {
      console.log('❌ NON CORRIGÉ: Erreur de syntaxe JavaScript');
      console.log(`   Erreur: ${error.message}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Erreur lors de la vérification:', error.message);
    return false;
  }
}

// Exécuter toutes les vérifications
function runAllChecks() {
  console.log('🚀 DÉMARRAGE DES VÉRIFICATIONS...\n');
  
  const parenthesesFixed = checkParenthesesBalance();
  const functionFixed = checkProblemFunction();
  const unicodeFixed = checkUnicodeCharacters();
  const syntaxFixed = checkJavaScriptSyntax();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 RÉSUMÉ DES VÉRIFICATIONS:');
  console.log('='.repeat(60));
  
  console.log(`Parenthèses équilibrées: ${parenthesesFixed ? '✅' : '❌'}`);
  console.log(`Fonction problématique corrigée: ${functionFixed ? '✅' : '❌'}`);
  console.log(`Caractères Unicode remplacés: ${unicodeFixed ? '✅' : '❌'}`);
  console.log(`Syntaxe JavaScript valide: ${syntaxFixed ? '✅' : '❌'}`);
  
  const allFixed = parenthesesFixed && functionFixed && syntaxFixed;
  
  console.log('\n🎯 CONCLUSION:');
  if (allFixed) {
    console.log('✅ TOUTES LES CORRECTIONS ONT ÉTÉ APPLIQUÉES AVEC SUCCÈS!');
    console.log('   L\'extension devrait maintenant fonctionner correctement.');
  } else {
    console.log('❌ CERTAINES CORRECTIONS N\'ONT PAS ÉTÉ APPLIQUÉES');
    console.log('   L\'extension pourrait toujours ne pas fonctionner correctement.');
  }
  
  if (!unicodeFixed) {
    console.log('\n⚠️ NOTE: Les caractères Unicode sont toujours présents.');
    console.log('   Ils ne causent pas nécessairement de problèmes, mais pourraient');
    console.log('   être remplacés pour une meilleure compatibilité.');
  }
  
  return allFixed;
}

// Exécuter les vérifications
runAllChecks();