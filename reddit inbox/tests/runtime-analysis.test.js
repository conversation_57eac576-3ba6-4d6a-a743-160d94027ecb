// Test d'analyse runtime - pour identifier ce qui se passe réellement dans le navigateur
console.log('🔍 ANALYSE RUNTIME DE L\'EXTENSION');
console.log('='.repeat(60));

const fs = require('fs');

// Test 1: Analyser les caractères Unicode problématiques
function analyzeUnicodeIssues() {
  console.log('\n🔤 ANALYSE DES CARACTÈRES UNICODE:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    const unicodeProblems = [
      { char: '📬', name: 'mailbox emoji', line: null },
      { char: '↻', name: 'refresh symbol', line: null },
      { char: '×', name: 'multiplication sign', line: null },
      { char: '🧪', name: 'test tube emoji', line: null },
      { char: '🔐', name: 'lock emoji', line: null }
    ];
    
    const lines = contentScript.split('\n');
    
    unicodeProblems.forEach(problem => {
      lines.forEach((line, index) => {
        if (line.includes(problem.char)) {
          problem.line = index + 1;
          console.log(`⚠️  ${problem.name} (${problem.char}) trouvé ligne ${problem.line}`);
          console.log(`    Contexte: ${line.trim().substring(0, 80)}...`);
        }
      });
    });
    
    console.log('\n🎯 IMPACT POTENTIEL:');
    console.log('   - Ces caractères peuvent causer des erreurs de parsing');
    console.log('   - Ils peuvent être mal encodés selon le navigateur');
    console.log('   - Ils peuvent empêcher l\'exécution du JavaScript');
    
    return unicodeProblems.filter(p => p.line !== null);
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return [];
  }
}

// Test 2: Analyser le problème de parenthèses
function analyzeParenthesesIssue() {
  console.log('\n🔧 ANALYSE DU PROBLÈME DE PARENTHÈSES:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    let openCount = 0;
    let closeCount = 0;
    let problemLines = [];
    
    lines.forEach((line, index) => {
      const lineOpenCount = (line.match(/\(/g) || []).length;
      const lineCloseCount = (line.match(/\)/g) || []).length;
      
      openCount += lineOpenCount;
      closeCount += lineCloseCount;
      
      // Détecter les lignes avec déséquilibre
      if (lineOpenCount !== lineCloseCount) {
        const balance = lineOpenCount - lineCloseCount;
        problemLines.push({
          line: index + 1,
          content: line.trim(),
          balance: balance,
          open: lineOpenCount,
          close: lineCloseCount
        });
      }
    });
    
    console.log(`📊 Total: ${openCount} ouvertes, ${closeCount} fermées`);
    console.log(`⚠️  Déséquilibre: ${openCount - closeCount}`);
    
    if (problemLines.length > 0) {
      console.log(`\n🚨 ${problemLines.length} lignes avec déséquilibre détectées:`);
      problemLines.slice(0, 5).forEach(problem => {
        console.log(`   Ligne ${problem.line}: balance ${problem.balance > 0 ? '+' : ''}${problem.balance}`);
        console.log(`      ${problem.content.substring(0, 60)}...`);
      });
      
      if (problemLines.length > 5) {
        console.log(`   ... et ${problemLines.length - 5} autres lignes`);
      }
    }
    
    return { total: openCount - closeCount, problemLines };
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return { total: 0, problemLines: [] };
  }
}

// Test 3: Analyser les template literals
function analyzeTemplateLiterals() {
  console.log('\n📝 ANALYSE DES TEMPLATE LITERALS:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    let backtickCount = 0;
    let templateProblems = [];
    let inTemplate = false;
    
    lines.forEach((line, index) => {
      const lineBackticks = (line.match(/`/g) || []).length;
      backtickCount += lineBackticks;
      
      if (lineBackticks % 2 !== 0) {
        inTemplate = !inTemplate;
        templateProblems.push({
          line: index + 1,
          content: line.trim(),
          backticks: lineBackticks,
          state: inTemplate ? 'OUVERT' : 'FERMÉ'
        });
      }
    });
    
    console.log(`📊 Total backticks: ${backtickCount}`);
    console.log(`⚠️  État final: ${backtickCount % 2 === 0 ? 'ÉQUILIBRÉ' : 'DÉSÉQUILIBRÉ'}`);
    
    if (templateProblems.length > 0) {
      console.log(`\n🚨 ${templateProblems.length} changements d'état détectés:`);
      templateProblems.slice(0, 3).forEach(problem => {
        console.log(`   Ligne ${problem.line}: ${problem.state}`);
        console.log(`      ${problem.content.substring(0, 60)}...`);
      });
    }
    
    return { balanced: backtickCount % 2 === 0, problems: templateProblems };
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return { balanced: true, problems: [] };
  }
}

// Test 4: Analyser les conflits CSS potentiels
function analyzeCSSConflicts() {
  console.log('\n🎨 ANALYSE DES CONFLITS CSS POTENTIELS:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Vérifier les propriétés critiques
    const criticalProperties = [
      { prop: 'width.*400px.*!important', desc: 'Largeur fixe' },
      { prop: 'min-width.*400px.*!important', desc: 'Largeur minimale' },
      { prop: 'display.*block.*!important', desc: 'Affichage en bloc' },
      { prop: 'z-index.*999999.*!important', desc: 'Z-index élevé' },
      { prop: 'position.*absolute.*!important', desc: 'Position absolue' },
      { prop: 'cursor.*pointer.*!important', desc: 'Curseur pointeur' }
    ];
    
    let foundProperties = 0;
    let missingProperties = [];
    
    criticalProperties.forEach(({ prop, desc }) => {
      const regex = new RegExp(prop);
      if (regex.test(cssContent)) {
        console.log(`✅ ${desc}: trouvé`);
        foundProperties++;
      } else {
        console.log(`❌ ${desc}: MANQUANT`);
        missingProperties.push(desc);
      }
    });
    
    console.log(`\n📊 Propriétés critiques: ${foundProperties}/${criticalProperties.length}`);
    
    // Vérifier les sélecteurs spécifiques
    const specificSelectors = [
      '.mini-inbox-container',
      '.notification-item',
      '.notification-action'
    ];
    
    console.log('\n🔍 Vérification des sélecteurs spécifiques:');
    specificSelectors.forEach(selector => {
      const regex = new RegExp(`${selector.replace('.', '\\.')}\\s*\\{[^}]*!important`, 'g');
      const matches = cssContent.match(regex);
      if (matches) {
        console.log(`✅ ${selector}: ${matches.length} règle(s) avec !important`);
      } else {
        console.log(`❌ ${selector}: aucune règle avec !important`);
      }
    });
    
    return { foundProperties, missingProperties };
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return { foundProperties: 0, missingProperties: [] };
  }
}

// Test 5: Analyser les event listeners en détail
function analyzeEventListenerDetails() {
  console.log('\n👂 ANALYSE DÉTAILLÉE DES EVENT LISTENERS:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    // Chercher la fonction attachEventListeners
    const attachFunctionMatch = contentScript.match(/function attachEventListeners\([\s\S]*?\n\}/);
    
    if (attachFunctionMatch) {
      console.log('✅ Fonction attachEventListeners trouvée');
      
      const functionContent = attachFunctionMatch[0];
      
      // Analyser le contenu de la fonction
      const checks = [
        { pattern: 'querySelectorAll\\(.*notification-action', desc: 'Sélection des boutons d\'action' },
        { pattern: 'addEventListener\\(.*click', desc: 'Ajout d\'event listeners' },
        { pattern: 'getAttribute\\(.*data-action', desc: 'Récupération de data-action' },
        { pattern: 'handleNotificationAction', desc: 'Appel de la fonction handler' },
        { pattern: 'preventDefault', desc: 'Prévention du comportement par défaut' },
        { pattern: 'stopPropagation', desc: 'Arrêt de la propagation' }
      ];
      
      let foundChecks = 0;
      checks.forEach(({ pattern, desc }) => {
        const regex = new RegExp(pattern);
        if (regex.test(functionContent)) {
          console.log(`✅ ${desc}: présent`);
          foundChecks++;
        } else {
          console.log(`❌ ${desc}: MANQUANT`);
        }
      });
      
      console.log(`\n📊 Éléments de la fonction: ${foundChecks}/${checks.length}`);
      
      // Vérifier les messages de debug
      const debugMessages = functionContent.match(/console\\.log.*DEBUG/g) || [];
      console.log(`🐛 Messages de debug: ${debugMessages.length}`);
      
      return { found: true, checks: foundChecks, debugCount: debugMessages.length };
    } else {
      console.log('❌ Fonction attachEventListeners NON TROUVÉE');
      return { found: false, checks: 0, debugCount: 0 };
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return { found: false, checks: 0, debugCount: 0 };
  }
}

// Test 6: Identifier le bug principal
function identifyMainBug() {
  console.log('\n🎯 IDENTIFICATION DU BUG PRINCIPAL:');
  
  const unicodeIssues = analyzeUnicodeIssues();
  const parenIssues = analyzeParenthesesIssue();
  const templateIssues = analyzeTemplateLiterals();
  const cssIssues = analyzeCSSConflicts();
  const eventIssues = analyzeEventListenerDetails();
  
  console.log('\n' + '='.repeat(50));
  console.log('🔍 DIAGNOSTIC FINAL:');
  console.log('='.repeat(50));
  
  let bugSeverity = 0;
  let mainIssues = [];
  
  // Analyser la sévérité des problèmes
  if (unicodeIssues.length > 0) {
    bugSeverity += 3;
    mainIssues.push(`🔤 ${unicodeIssues.length} caractères Unicode problématiques`);
  }
  
  if (parenIssues.total !== 0) {
    bugSeverity += 5;
    mainIssues.push(`🔧 Parenthèses déséquilibrées (${parenIssues.total})`);
  }
  
  if (!templateIssues.balanced) {
    bugSeverity += 4;
    mainIssues.push('📝 Template literals déséquilibrés');
  }
  
  if (cssIssues.missingProperties.length > 0) {
    bugSeverity += 2;
    mainIssues.push(`🎨 ${cssIssues.missingProperties.length} propriétés CSS critiques manquantes`);
  }
  
  if (!eventIssues.found || eventIssues.checks < 4) {
    bugSeverity += 4;
    mainIssues.push('👂 Event listeners mal configurés');
  }
  
  console.log(`🚨 SÉVÉRITÉ DU BUG: ${bugSeverity}/18`);
  
  if (bugSeverity === 0) {
    console.log('✅ Aucun problème critique détecté');
    console.log('🤔 Le bug est probablement lié à l\'exécution runtime');
  } else {
    console.log('❌ PROBLÈMES CRITIQUES DÉTECTÉS:');
    mainIssues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }
  
  // Recommandations
  console.log('\n💡 RECOMMANDATIONS:');
  if (parenIssues.total !== 0) {
    console.log('   🔧 PRIORITÉ 1: Corriger les parenthèses déséquilibrées');
  }
  if (unicodeIssues.length > 0) {
    console.log('   🔤 PRIORITÉ 2: Remplacer les caractères Unicode par du texte');
  }
  if (!templateIssues.balanced) {
    console.log('   📝 PRIORITÉ 3: Vérifier les template literals');
  }
  
  return {
    severity: bugSeverity,
    issues: mainIssues,
    unicode: unicodeIssues,
    parentheses: parenIssues,
    templates: templateIssues,
    css: cssIssues,
    events: eventIssues
  };
}

// Exécuter l'analyse runtime complète
identifyMainBug();