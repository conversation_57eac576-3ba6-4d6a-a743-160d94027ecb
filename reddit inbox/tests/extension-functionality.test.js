// Tests de fonctionnalité de l'extension Reddit Mini Inbox
// Ces tests vérifient que l'extension fonctionne correctement

console.log('Demarrage des tests de l\'extension Reddit Mini Inbox...');

// Test 1: Vérifier que le content script se charge
function testContentScriptLoading() {
  console.log('Test 1: Chargement du content script');
  
  try {
    // Simuler le chargement du script
    const scriptContent = require('fs').readFileSync('./scripts/content-new.js', 'utf8');
    
    // Vérifier que les fonctions principales sont définies
    const requiredFunctions = [
      'init',
      'setup', 
      'handleClick',
      'toggleMiniInbox',
      'openMiniInbox',
      'closeMiniInbox',
      'createMiniInboxElement',
      'isNotificationIcon'
    ];
    
    let allFunctionsFound = true;
    requiredFunctions.forEach(funcName => {
      if (!scriptContent.includes(`function ${funcName}`)) {
        console.error(`❌ Fonction manquante: ${funcName}`);
        allFunctionsFound = false;
      }
    });
    
    if (allFunctionsFound) {
      console.log('✅ Test 1 réussi: Toutes les fonctions principales sont présentes');
      return true;
    } else {
      console.log('❌ Test 1 échoué: Fonctions manquantes');
      return false;
    }
  } catch (error) {
    console.error('❌ Test 1 échoué:', error.message);
    return false;
  }
}

// Test 2: Vérifier la syntaxe JavaScript
function testJavaScriptSyntax() {
  console.log('Test 2: Syntaxe JavaScript');
  
  try {
    const { execSync } = require('child_process');
    
    // Tester content script
    execSync('node -c scripts/content-new.js', { stdio: 'pipe' });
    console.log('✅ Content script: syntaxe valide');
    
    // Tester background script
    execSync('node -c scripts/background.js', { stdio: 'pipe' });
    console.log('✅ Background script: syntaxe valide');
    
    // Tester popup script
    execSync('node -c popup/popup.js', { stdio: 'pipe' });
    console.log('✅ Popup script: syntaxe valide');
    
    console.log('✅ Test 2 réussi: Syntaxe JavaScript valide');
    return true;
  } catch (error) {
    console.error('❌ Test 2 échoué: Erreur de syntaxe:', error.message);
    return false;
  }
}

// Test 3: Vérifier la configuration du manifest
function testManifestConfiguration() {
  console.log('Test 3: Configuration du manifest');
  
  try {
    const manifest = JSON.parse(require('fs').readFileSync('./manifest.json', 'utf8'));
    
    // Vérifications essentielles
    const checks = [
      { key: 'manifest_version', expected: 3, actual: manifest.manifest_version },
      { key: 'permissions', expected: 'array', actual: Array.isArray(manifest.permissions) },
      { key: 'host_permissions', expected: 'array', actual: Array.isArray(manifest.host_permissions) },
      { key: 'content_scripts', expected: 'array', actual: Array.isArray(manifest.content_scripts) }
    ];
    
    let allChecksPass = true;
    checks.forEach(check => {
      if (typeof check.expected === 'string') {
        if (!check.actual) {
          console.error(`❌ ${check.key}: attendu ${check.expected}, reçu ${typeof check.actual}`);
          allChecksPass = false;
        }
      } else {
        if (check.actual !== check.expected) {
          console.error(`❌ ${check.key}: attendu ${check.expected}, reçu ${check.actual}`);
          allChecksPass = false;
        }
      }
    });
    
    // Vérifier que le content script pointe vers le bon fichier
    const contentScript = manifest.content_scripts[0];
    if (!contentScript.js.includes('scripts/content-new.js')) {
      console.error('❌ Content script ne pointe pas vers content-new.js');
      allChecksPass = false;
    }
    
    if (allChecksPass) {
      console.log('✅ Test 3 réussi: Configuration du manifest correcte');
      return true;
    } else {
      console.log('❌ Test 3 échoué: Problèmes de configuration');
      return false;
    }
  } catch (error) {
    console.error('❌ Test 3 échoué:', error.message);
    return false;
  }
}

// Test 4: Vérifier les sélecteurs de détection d'icône
function testNotificationIconSelectors() {
  console.log('Test 4: Sélecteurs d\'icône de notification');
  
  try {
    const scriptContent = require('fs').readFileSync('./scripts/content-new.js', 'utf8');
    
    // Vérifier que les sélecteurs Reddit sont présents
    const requiredSelectors = [
      'notification',
      'inbox',
      'message',
      'mail',
      'bell'
    ];
    
    let selectorsFound = 0;
    requiredSelectors.forEach(selector => {
      if (scriptContent.includes(selector)) {
        selectorsFound++;
      }
    });
    
    if (selectorsFound >= 3) {
      console.log(`✅ Test 4 réussi: ${selectorsFound}/${requiredSelectors.length} sélecteurs trouvés`);
      return true;
    } else {
      console.log(`❌ Test 4 échoué: Seulement ${selectorsFound}/${requiredSelectors.length} sélecteurs trouvés`);
      return false;
    }
  } catch (error) {
    console.error('❌ Test 4 échoué:', error.message);
    return false;
  }
}

// Test 5: Vérifier la communication avec le background script
function testBackgroundCommunication() {
  console.log('Test 5: Communication avec le background script');
  
  try {
    const contentScript = require('fs').readFileSync('./scripts/content-new.js', 'utf8');
    const backgroundScript = require('fs').readFileSync('./scripts/background.js', 'utf8');
    
    // Vérifier que le content script envoie des messages
    const contentSendsMessages = contentScript.includes('chrome.runtime.sendMessage');
    
    // Vérifier que le background script écoute les messages
    const backgroundListensMessages = backgroundScript.includes('chrome.runtime.onMessage.addListener');
    
    // Vérifier les actions supportées
    const requiredActions = ['getNotifications', 'markAsRead', 'authenticate'];
    let actionsSupported = 0;
    
    requiredActions.forEach(action => {
      if (backgroundScript.includes(`'${action}'`) || backgroundScript.includes(`"${action}"`)) {
        actionsSupported++;
      }
    });
    
    if (contentSendsMessages && backgroundListensMessages && actionsSupported >= 2) {
      console.log('✅ Test 5 réussi: Communication background configurée');
      return true;
    } else {
      console.log('❌ Test 5 échoué: Problème de communication');
      console.log(`  - Content envoie messages: ${contentSendsMessages}`);
      console.log(`  - Background écoute: ${backgroundListensMessages}`);
      console.log(`  - Actions supportées: ${actionsSupported}/${requiredActions.length}`);
      return false;
    }
  } catch (error) {
    console.error('❌ Test 5 échoué:', error.message);
    return false;
  }
}

// Exécuter tous les tests
function runAllTests() {
  console.log('🚀 Exécution de tous les tests...\n');
  
  const tests = [
    testContentScriptLoading,
    testJavaScriptSyntax,
    testManifestConfiguration,
    testNotificationIconSelectors,
    testBackgroundCommunication
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  tests.forEach((test, index) => {
    console.log(`\n--- Test ${index + 1}/${totalTests} ---`);
    if (test()) {
      passedTests++;
    }
  });
  
  console.log('\n' + '='.repeat(50));
  console.log(`📊 Résultats: ${passedTests}/${totalTests} tests réussis`);
  
  if (passedTests === totalTests) {
    console.log('🎉 Tous les tests sont passés!');
    return true;
  } else {
    console.log('⚠️  Certains tests ont échoué. L\'extension pourrait ne pas fonctionner correctement.');
    return false;
  }
}

// Exporter pour utilisation
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testContentScriptLoading,
    testJavaScriptSyntax,
    testManifestConfiguration,
    testNotificationIconSelectors,
    testBackgroundCommunication
  };
}

// Exécuter si appelé directement
if (require.main === module) {
  runAllTests();
}