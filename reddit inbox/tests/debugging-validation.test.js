const fs = require('fs');
const path = require('path');

describe('Debugging Validation Tests', () => {
  let jsContent;

  beforeAll(() => {
    const jsPath = path.join(__dirname, '..', 'scripts', 'content-new.js');
    jsContent = fs.readFileSync(jsPath, 'utf8');
  });

  describe('Debug Logging', () => {
    test('should have comprehensive debug logs for toggleMiniInbox', () => {
      expect(jsContent).toContain('🔄 DEBUG: toggleMiniInbox called');
      expect(jsContent).toContain('📤 DEBUG: Closing mini inbox');
      expect(jsContent).toContain('📥 DEBUG: Opening mini inbox');
    });

    test('should have debug logs for openMiniInbox flow', () => {
      expect(jsContent).toContain('🚀 DEBUG: openMiniInbox START');
      expect(jsContent).toContain('🔍 DEBUG: Current state');
      expect(jsContent).toContain('🗑️ DEBUG: Removing existing miniInboxElement');
      expect(jsContent).toContain('🏗️ DEBUG: Creating mini inbox element');
      expect(jsContent).toContain('⏳ DEBUG: Setting loading state');
    });

    test('should have debug logs for chrome.runtime communication', () => {
      expect(jsContent).toContain('🔐 DEBUG: Checking authentication');
      expect(jsContent).toContain('📡 DEBUG: Sending message to background script');
      expect(jsContent).toContain('📡 DEBUG: chrome.runtime available?');
      expect(jsContent).toContain('📡 DEBUG: Response received');
    });

    test('should have debug logs for response handling', () => {
      expect(jsContent).toContain('✅ DEBUG: Response successful');
      expect(jsContent).toContain('📋 DEBUG: Displaying notifications');
      expect(jsContent).toContain('📭 DEBUG: No notifications, showing empty state');
      expect(jsContent).toContain('❌ DEBUG: Response failed');
    });

    test('should have debug logs for displayNotifications', () => {
      expect(jsContent).toContain('📋 DEBUG: displayNotifications START');
      expect(jsContent).toContain('❌ DEBUG: miniInboxElement is null!');
      expect(jsContent).toContain('✅ DEBUG: Setting loading state to success');
      expect(jsContent).toContain('🏗️ DEBUG: Building notifications HTML');
    });

    test('should have debug logs for event listener attachment', () => {
      expect(jsContent).toContain('🔧 DEBUG: attachEventListeners START');
      expect(jsContent).toContain('⏰ DEBUG: Timeout executed, DOM should be ready');
      expect(jsContent).toContain('🔍 DEBUG: Found ${actionButtons.length} action buttons');
      expect(jsContent).toContain('🔍 DEBUG: Action buttons details');
    });

    test('should have debug logs for button clicks', () => {
      expect(jsContent).toContain('🎯 DEBUG: ===== ACTION BUTTON CLICKED =====');
      expect(jsContent).toContain('🔄 DEBUG: ===== REFRESH BUTTON CLICKED =====');
      expect(jsContent).toContain('❌ DEBUG: ===== CLOSE BUTTON CLICKED =====');
    });

    test('should have debug logs for action handling', () => {
      expect(jsContent).toContain('🎬 DEBUG: ===== HANDLE NOTIFICATION ACTION =====');
      expect(jsContent).toContain('💬 DEBUG: Handling reply action');
      expect(jsContent).toContain('✅ DEBUG: Handling mark-read action');
      expect(jsContent).toContain('👁️ DEBUG: Handling mark-unread action');
    });

    test('should have debug logs for reply action', () => {
      expect(jsContent).toContain('💬 DEBUG: ===== HANDLE REPLY ACTION =====');
      expect(jsContent).toContain('💬 DEBUG: Creating quick reply interface');
      expect(jsContent).toContain('💬 DEBUG: Reply interface inserted into DOM');
      expect(jsContent).toContain('💬 DEBUG: Textarea focused');
    });
  });

  describe('Error Handling and Fallback', () => {
    test('should have comprehensive error logging', () => {
      expect(jsContent).toContain('💥 DEBUG: Error getting notifications:');
      expect(jsContent).toContain('💥 DEBUG: Error details:');
      expect(jsContent).toContain('🔄 DEBUG: Attempting fallback with test data');
      expect(jsContent).toContain('💥 DEBUG: Fallback also failed:');
    });

    test('should have test notifications fallback', () => {
      expect(jsContent).toContain('function getTestNotifications()');
      expect(jsContent).toContain('🧪 DEBUG: Generating test notifications');
      expect(jsContent).toContain('test_user');
      expect(jsContent).toContain('Test Reply');
      expect(jsContent).toContain('This is a test notification');
    });

    test('should handle missing elements gracefully', () => {
      expect(jsContent).toContain('❌ DEBUG: No actionType found!');
      expect(jsContent).toContain('❌ DEBUG: No notificationId found!');
      expect(jsContent).toContain('❌ DEBUG: Refresh button not found!');
      expect(jsContent).toContain('❌ DEBUG: Close button not found!');
    });
  });

  describe('Visual Debugging Indicators', () => {
    test('should add visual indicators to action buttons', () => {
      expect(jsContent).toContain("action.style.border = '2px solid red'");
      expect(jsContent).toContain("action.style.backgroundColor = 'rgba(255, 255, 0, 0.3)'");
      expect(jsContent).toContain('🔍 DEBUG: Added visual indicators to action');
    });

    test('should add visual indicators to refresh button', () => {
      expect(jsContent).toContain("refreshBtn.style.border = '2px solid blue'");
      expect(jsContent).toContain("refreshBtn.style.backgroundColor = 'rgba(0, 0, 255, 0.1)'");
      expect(jsContent).toContain('DEBUG: Refresh button - Click to test');
    });

    test('should add visual indicators to close button', () => {
      expect(jsContent).toContain("closeBtn.style.border = '2px solid green'");
      expect(jsContent).toContain("closeBtn.style.backgroundColor = 'rgba(0, 255, 0, 0.1)'");
      expect(jsContent).toContain('DEBUG: Close button - Click to test');
    });
  });

  describe('Global Click Debugging', () => {
    test('should debug all clicks when mini inbox is open', () => {
      expect(jsContent).toContain('🖱️ DEBUG: Global click detected');
      expect(jsContent).toContain('closest_mini_inbox');
      expect(jsContent).toContain('closest_notification_action');
      expect(jsContent).toContain('closest_refresh_button');
      expect(jsContent).toContain('closest_close_btn');
    });

    test('should debug clicks outside mini inbox', () => {
      expect(jsContent).toContain('🌍 DEBUG: Click outside mini inbox, closing');
    });
  });

  describe('State Debugging', () => {
    test('should debug state changes', () => {
      expect(jsContent).toContain('✅ DEBUG: openMiniInbox completed');
      expect(jsContent).toContain('✅ DEBUG: displayNotifications COMPLETED');
      expect(jsContent).toContain('✅ DEBUG: handleNotificationAction completed');
      expect(jsContent).toContain('✅ DEBUG: handleReplyAction completed');
    });

    test('should debug button states', () => {
      expect(jsContent).toContain('🔄 DEBUG: Refresh button found:');
      expect(jsContent).toContain('❌ DEBUG: Close button found:');
      expect(jsContent).toContain('✅ DEBUG: Refresh button event listener attached');
      expect(jsContent).toContain('✅ DEBUG: Close button event listener attached');
    });
  });

  describe('Chrome Extension API Debugging', () => {
    test('should debug chrome.runtime availability', () => {
      expect(jsContent).toContain('chrome.runtime available?');
      expect(jsContent).toContain('chrome.runtime.sendMessage available?');
    });

    test('should debug authentication', () => {
      expect(jsContent).toContain('🔐 DEBUG: Auth data');
      expect(jsContent).toContain('🔐 DEBUG: Auth required, showing auth screen');
    });
  });
});
