// Test final pour diagnostiquer le problème de colonnes
console.log('🎯 DIAGNOSTIC FINAL DU PROBLÈME DE COLONNES');
console.log('='.repeat(60));

const fs = require('fs');

function analyzeContainerHierarchy() {
  console.log('\n🏗️ ANALYSE DE LA HIÉRARCHIE DES CONTENEURS:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Analyser les conteneurs principaux
    const containers = [
      '#reddit-mini-inbox-overlay',
      '.mini-inbox-container',
      '.mini-inbox-content',
      '.notification-item',
      '.notification-header',
      '.notification-body',
      '.notification-actions'
    ];
    
    containers.forEach(container => {
      console.log(`\n📦 ${container}:`);
      
      // Extraire toutes les règles pour ce conteneur
      const escapedContainer = container.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`${escapedContainer}[^{]*\\{[^}]*\\}`, 'g');
      const matches = cssContent.match(regex);
      
      if (matches) {
        console.log(`   Trouvé ${matches.length} règle(s)`);
        
        // Analyser les propriétés de layout critiques
        const layoutProps = ['display', 'width', 'flex', 'grid', 'column', 'float', 'position'];
        let foundProps = {};
        
        matches.forEach(rule => {
          layoutProps.forEach(prop => {
            const propRegex = new RegExp(`${prop}\\s*:\\s*([^;!]+)`, 'gi');
            const propMatches = rule.match(propRegex);
            if (propMatches) {
              if (!foundProps[prop]) foundProps[prop] = [];
              foundProps[prop].push(...propMatches.map(m => m.trim()));
            }
          });
        });
        
        // Afficher les propriétés trouvées
        Object.entries(foundProps).forEach(([prop, values]) => {
          const uniqueValues = [...new Set(values)];
          console.log(`   ${prop}: ${uniqueValues.join(', ')}`);
        });
      } else {
        console.log(`   ❌ Aucune règle trouvée`);
      }
    });
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

function checkForOverridingRules() {
  console.log('\n🔄 RECHERCHE DE RÈGLES QUI SE SURCHARGENT:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Rechercher les règles multiples pour .mini-inbox-container
    const containerRules = cssContent.match(/\.mini-inbox-container[^{]*\{[^}]*\}/g);
    
    if (containerRules && containerRules.length > 1) {
      console.log(`⚠️  ${containerRules.length} règles pour .mini-inbox-container:`);
      
      let widthValues = [];
      let displayValues = [];
      
      containerRules.forEach((rule, index) => {
        console.log(`\n📋 Règle ${index + 1}:`);
        
        // Extraire width
        const widthMatch = rule.match(/width\s*:\s*([^;!]+)/gi);
        if (widthMatch) {
          widthMatch.forEach(w => {
            widthValues.push(w.trim());
            console.log(`   📏 ${w.trim()}`);
          });
        }
        
        // Extraire display
        const displayMatch = rule.match(/display\s*:\s*([^;!]+)/gi);
        if (displayMatch) {
          displayMatch.forEach(d => {
            displayValues.push(d.trim());
            console.log(`   📺 ${d.trim()}`);
          });
        }
        
        // Vérifier les media queries
        const beforeRule = cssContent.substring(0, cssContent.indexOf(rule));
        const mediaQueryMatch = beforeRule.match(/@media[^{]*\{[^}]*$/);
        if (mediaQueryMatch) {
          console.log(`   📱 Dans media query: ${mediaQueryMatch[0].substring(0, 50)}...`);
        }
      });
      
      console.log(`\n📊 Résumé des valeurs:`);
      console.log(`   Width: ${[...new Set(widthValues)].join(', ')}`);
      console.log(`   Display: ${[...new Set(displayValues)].join(', ')}`);
      
      // Identifier les conflits potentiels
      if (widthValues.length > 1) {
        console.log(`   ⚠️  CONFLIT POTENTIEL: ${widthValues.length} valeurs de width différentes`);
      }
      
    } else {
      console.log('✅ Une seule règle pour .mini-inbox-container');
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

function analyzeMediaQueries() {
  console.log('\n📱 ANALYSE DES MEDIA QUERIES:');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Extraire toutes les media queries
    const mediaQueries = cssContent.match(/@media[^{]*\{[^{}]*(?:\{[^}]*\}[^{}]*)*\}/g);
    
    if (mediaQueries) {
      console.log(`📊 Trouvé ${mediaQueries.length} media queries`);
      
      mediaQueries.forEach((mq, index) => {
        console.log(`\n📱 Media Query ${index + 1}:`);
        
        // Extraire la condition
        const conditionMatch = mq.match(/@media\s*([^{]+)/);
        if (conditionMatch) {
          console.log(`   Condition: ${conditionMatch[1].trim()}`);
        }
        
        // Vérifier si elle affecte .mini-inbox-container
        if (mq.includes('.mini-inbox-container')) {
          console.log(`   ⚠️  Affecte .mini-inbox-container`);
          
          // Extraire les propriétés width
          const widthMatches = mq.match(/width\s*:\s*([^;!]+)/gi);
          if (widthMatches) {
            widthMatches.forEach(w => {
              console.log(`     📏 ${w.trim()}`);
            });
          }
        }
        
        // Vérifier si elle affecte .notification-item
        if (mq.includes('.notification-item')) {
          console.log(`   ⚠️  Affecte .notification-item`);
        }
      });
    } else {
      console.log('✅ Aucune media query trouvée');
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

function identifyColumnCause() {
  console.log('\n🎯 IDENTIFICATION DE LA CAUSE DES COLONNES:');
  
  analyzeContainerHierarchy();
  checkForOverridingRules();
  analyzeMediaQueries();
  
  console.log('\n' + '='.repeat(60));
  console.log('🎯 DIAGNOSTIC FINAL - CAUSE PROBABLE:');
  console.log('='.repeat(60));
  
  console.log('\n🤔 HYPOTHÈSES PRINCIPALES:');
  console.log('1. 📱 Media queries qui changent la largeur du conteneur');
  console.log('2. 🔄 Règles CSS multiples qui se surchargent');
  console.log('3. 🎨 CSS de Reddit qui interfère avec nos styles');
  console.log('4. ⚡ JavaScript qui modifie les styles après le chargement');
  
  console.log('\n💡 TESTS À EFFECTUER:');
  console.log('1. Vérifier si le problème apparaît sur différentes tailles d\'écran');
  console.log('2. Inspecter l\'élément dans les DevTools pour voir les styles appliqués');
  console.log('3. Désactiver temporairement les media queries');
  console.log('4. Augmenter la spécificité CSS avec des sélecteurs plus précis');
  
  console.log('\n🔧 SOLUTIONS POTENTIELLES:');
  console.log('1. Ajouter des sélecteurs plus spécifiques (ex: body .mini-inbox-container)');
  console.log('2. Utiliser des valeurs CSS plus fortes (!important sur plus de propriétés)');
  console.log('3. Modifier l\'ordre des règles CSS');
  console.log('4. Isoler complètement les styles avec un namespace CSS');
}

// Exécuter le diagnostic final
identifyColumnCause();