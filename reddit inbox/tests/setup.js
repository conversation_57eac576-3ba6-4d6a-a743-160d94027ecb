// Configuration Jest pour les tests de l'extension Chrome

// Mock Chrome API
global.chrome = {
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn()
    }
  },
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn()
    }
  },
  tabs: {
    create: jest.fn(),
    query: jest.fn()
  }
};

// Mock des APIs Web
global.MutationObserver = class {
  constructor(callback) {
    this.callback = callback;
  }
  observe() {}
  disconnect() {}
};

global.IntersectionObserver = class {
  constructor(callback) {
    this.callback = callback;
  }
  observe() {}
  disconnect() {}
  unobserve() {}
};

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now())
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock getComputedStyle
global.getComputedStyle = jest.fn(() => ({
  backgroundColor: 'rgb(255, 255, 255)'
}));

// Nettoyer après chaque test
afterEach(() => {
  // Nettoyer le DOM
  document.body.innerHTML = '';
  
  // Réinitialiser les mocks
  jest.clearAllMocks();
});