// Test spécifique pour l'interception des clics
const fs = require('fs');

console.log('Test d\'interception des clics...');

// Simuler un environnement DOM minimal
global.document = {
  addEventListener: function(event, handler, capture) {
    console.log(`Event listener ajouté: ${event}, capture: ${capture}`);
    this._listeners = this._listeners || {};
    this._listeners[event] = handler;
  },
  createElement: function(tag) {
    return {
      tagName: tag.toUpperCase(),
      id: '',
      className: '',
      innerHTML: '',
      style: {},
      appendChild: function() {},
      remove: function() {},
      querySelector: function() { return null; },
      querySelectorAll: function() { return []; },
      classList: {
        add: function() {},
        remove: function() {},
        contains: function() { return false; }
      },
      getAttribute: function() { return null; },
      setAttribute: function() {},
      addEventListener: function() {}
    };
  },
  body: {
    appendChild: function() {}
  },
  readyState: 'complete'
};

global.window = {
  innerWidth: 1920,
  innerHeight: 1080,
  pageYOffset: 0,
  pageXOffset: 0,
  scrollY: 0,
  scrollX: 0,
  matchMedia: function() {
    return { matches: false };
  }
};

global.chrome = {
  runtime: {
    sendMessage: function(message) {
      console.log('Message envoyé au background:', message);
      return Promise.resolve({
        success: true,
        notifications: [],
        isMockData: true
      });
    }
  },
  storage: {
    local: {
      get: function() {
        return Promise.resolve({ authenticated: false });
      }
    }
  }
};

// Charger le content script
try {
  const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
  
  // Vérifier que les fonctions principales sont définies
  if (contentScript.includes('function handleClick')) {
    console.log('✅ Fonction handleClick trouvée');
  } else {
    console.log('❌ Fonction handleClick manquante');
  }
  
  if (contentScript.includes('function isNotificationIcon')) {
    console.log('✅ Fonction isNotificationIcon trouvée');
  } else {
    console.log('❌ Fonction isNotificationIcon manquante');
  }
  
  if (contentScript.includes('document.addEventListener(\'click\'')) {
    console.log('✅ Event listener de clic configuré');
  } else {
    console.log('❌ Event listener de clic manquant');
  }
  
  // Simuler l'exécution du script
  console.log('Tentative d\'exécution du content script...');
  
  // Remplacer les caractères Unicode problématiques
  const cleanScript = contentScript
    .replace(/📬/g, '📬')
    .replace(/↻/g, '↻')
    .replace(/×/g, '×')
    .replace(/🧪/g, '🧪')
    .replace(/🔐/g, '🔐')
    .replace(/📭/g, '📭')
    .replace(/✅/g, '✅');
  
  // Tester la syntaxe
  try {
    new Function(cleanScript);
    console.log('✅ Script exécutable sans erreur de syntaxe');
  } catch (error) {
    console.log('❌ Erreur de syntaxe:', error.message);
    console.log('Ligne:', error.lineNumber || 'inconnue');
  }
  
} catch (error) {
  console.log('❌ Erreur lors du chargement du script:', error.message);
}

console.log('Test terminé.');