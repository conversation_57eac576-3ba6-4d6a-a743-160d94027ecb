const fs = require('fs');
const path = require('path');

describe('CSS Validation', () => {
  let cssContent;

  beforeAll(() => {
    const cssPath = path.join(__dirname, '..', 'styles', 'mini-inbox.css');
    cssContent = fs.readFileSync(cssPath, 'utf8');
  });

  test('should have correct flexbox layout for mini-inbox-container', () => {
    expect(cssContent).toMatch(/\.mini-inbox-container\s*{[^}]*display:\s*flex\s*!important/);
    expect(cssContent).toMatch(/\.mini-inbox-container\s*{[^}]*flex-direction:\s*column\s*!important/);
  });

  test('should have correct flexbox layout for mini-inbox-content', () => {
    expect(cssContent).toMatch(/\.mini-inbox-content\s*{[^}]*display:\s*flex\s*!important/);
    expect(cssContent).toMatch(/\.mini-inbox-content\s*{[^}]*flex-direction:\s*column\s*!important/);
    expect(cssContent).toMatch(/\.mini-inbox-content\s*{[^}]*width:\s*100%\s*!important/);
  });

  test('should have correct flexbox layout for notification-item', () => {
    expect(cssContent).toMatch(/\.notification-item\s*{[^}]*display:\s*flex\s*!important/);
    expect(cssContent).toMatch(/\.notification-item\s*{[^}]*flex-direction:\s*column\s*!important/);
    expect(cssContent).toMatch(/\.notification-item\s*{[^}]*width:\s*100%\s*!important/);
  });

  test('should have correct flexbox layout for notification-header', () => {
    expect(cssContent).toMatch(/\.notification-header\s*{[^}]*display:\s*flex\s*!important/);
    expect(cssContent).toMatch(/\.notification-header\s*{[^}]*justify-content:\s*space-between\s*!important/);
    expect(cssContent).toMatch(/\.notification-header\s*{[^}]*align-items:\s*center\s*!important/);
  });

  test('should have correct flexbox layout for notification-actions', () => {
    expect(cssContent).toMatch(/\.notification-actions\s*{[^}]*display:\s*flex\s*!important/);
    expect(cssContent).toMatch(/\.notification-actions\s*{[^}]*gap:\s*8px\s*!important/);
    expect(cssContent).toMatch(/\.notification-actions\s*{[^}]*flex-wrap:\s*wrap\s*!important/);
  });

  test('should have improved styling for notification elements', () => {
    expect(cssContent).toMatch(/\.notification-subject\s*{[^}]*font-weight:\s*bold\s*!important/);
    expect(cssContent).toMatch(/\.notification-subject\s*{[^}]*color:\s*#1a1a1b\s*!important/);
    expect(cssContent).toMatch(/\.notification-body\s*{[^}]*color:\s*#666\s*!important/);
    expect(cssContent).toMatch(/\.notification-author\s*{[^}]*color:\s*#0079d3\s*!important/);
    expect(cssContent).toMatch(/\.notification-time\s*{[^}]*color:\s*#999\s*!important/);
  });

  test('should have improved action styling', () => {
    expect(cssContent).toMatch(/\.notification-action\s*{[^}]*color:\s*#0079d3\s*!important/);
    expect(cssContent).toMatch(/\.notification-action\s*{[^}]*white-space:\s*nowrap\s*!important/);
    expect(cssContent).toMatch(/\.notification-action:hover\s*{[^}]*background:\s*#f0f8ff\s*!important/);
  });

  test('should not contain problematic display: block for main containers', () => {
    // Vérifier qu'on n'a pas de display: block pour les conteneurs principaux
    const blockDisplayMatches = cssContent.match(/\.mini-inbox-container\s*{[^}]*display:\s*block\s*!important/g);
    expect(blockDisplayMatches).toBeNull();
  });

  test('should be valid CSS syntax', () => {
    // Test basique de syntaxe CSS - vérifier que les accolades sont équilibrées
    const openBraces = (cssContent.match(/{/g) || []).length;
    const closeBraces = (cssContent.match(/}/g) || []).length;
    expect(openBraces).toBe(closeBraces);
  });
});
