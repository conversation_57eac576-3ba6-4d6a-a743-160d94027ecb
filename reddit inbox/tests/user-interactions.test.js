// Test des interactions utilisateur (boutons, clics, etc.)
const fs = require('fs');

console.log('Test des interactions utilisateur...');

// Simuler un environnement DOM plus complet
global.document = {
  createElement: function(tag) {
    return {
      tagName: tag.toUpperCase(),
      id: '',
      className: '',
      innerHTML: '',
      style: {},
      appendChild: function() {},
      remove: function() {},
      querySelector: function(selector) {
        // Simuler la recherche d'éléments
        if (selector === '.close-btn') {
          return {
            addEventListener: function(event, handler) {
              console.log(`Event listener ajouté sur close-btn: ${event}`);
            }
          };
        }
        if (selector === '.notification-action') {
          return {
            addEventListener: function(event, handler) {
              console.log(`Event listener ajouté sur notification-action: ${event}`);
            },
            getAttribute: function(attr) {
              if (attr === 'data-action') return 'reply';
              if (attr === 'data-id') return 'test123';
              return null;
            }
          };
        }
        return null;
      },
      querySelectorAll: function(selector) {
        console.log(`querySelectorAll: ${selector}`);
        
        if (selector === '.notification-item') {
          return [
            {
              addEventListener: function(event, handler) {
                console.log(`Event listener ajouté sur notification-item: ${event}`);
              },
              classList: {
                add: function(className) {
                  console.log(`Classe ajoutée à notification-item: ${className}`);
                }
              }
            }
          ];
        }
        
        if (selector === '.notification-action') {
          return [
            {
              addEventListener: function(event, handler) {
                console.log(`Event listener ajouté sur notification-action: ${event}`);
              },
              getAttribute: function(attr) {
                if (attr === 'data-action') return 'reply';
                if (attr === 'data-id') return 'test123';
                return null;
              },
              textContent: 'reply'
            },
            {
              addEventListener: function(event, handler) {
                console.log(`Event listener ajouté sur notification-action: ${event}`);
              },
              getAttribute: function(attr) {
                if (attr === 'data-action') return 'mark-read';
                if (attr === 'data-id') return 'test123';
                return null;
              },
              textContent: 'mark as read'
            }
          ];
        }
        
        return [];
      },
      classList: {
        add: function(className) {
          console.log(`Classe ajoutée: ${className}`);
        },
        remove: function(className) {
          console.log(`Classe supprimée: ${className}`);
        },
        contains: function() { return false; }
      },
      getAttribute: function() { return null; },
      setAttribute: function() {},
      addEventListener: function(event, handler) {
        console.log(`Event listener ajouté sur élément: ${event}`);
      }
    };
  },
  body: {
    appendChild: function() {}
  }
};

global.chrome = {
  runtime: {
    sendMessage: function(message) {
      console.log('Message envoyé au background:', message);
      return Promise.resolve({ success: true });
    }
  }
};

// Tester la fonction attachEventListeners
function testEventListeners() {
  console.log('\nTest des event listeners...');
  
  // Simuler la fonction attachEventListeners du content script
  function attachEventListeners(content) {
    console.log('🔧 Attaching event listeners...');
    
    // Attach to notification items
    content.querySelectorAll('.notification-item').forEach((item, index) => {
      item.addEventListener('click', (event) => {
        if (!event.target.closest('.notification-actions') && 
            !event.target.closest('.quick-reply-interface')) {
          console.log('Notification item clicked');
        }
      });
      
      setTimeout(() => item.classList.add('new-notification'), index * 50);
    });
    
    // Attach to action buttons - MOST IMPORTANT
    content.querySelectorAll('.notification-action').forEach((action, index) => {
      console.log(`Attaching to action ${index}:`, action.textContent, action.getAttribute('data-action'));
      
      action.addEventListener('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const actionType = this.getAttribute('data-action');
        const notificationId = this.getAttribute('data-id');
        
        console.log('🎯 Action clicked:', actionType, 'for notification:', notificationId);
        
        // Simuler handleNotificationAction
        handleNotificationAction(actionType, notificationId);
      });
    });
    
    console.log('✅ Event listeners attached successfully!');
  }
  
  function handleNotificationAction(actionType, notificationId) {
    console.log('Handling action:', actionType, 'for notification:', notificationId);
    
    switch (actionType) {
      case 'reply':
        console.log('💬 Reply action triggered');
        break;
      case 'mark-read':
        console.log('✅ Mark as read action triggered');
        chrome.runtime.sendMessage({
          action: 'markAsRead',
          notificationId: notificationId
        });
        break;
      case 'mark-unread':
        console.log('👁️ Mark as unread action triggered');
        break;
      default:
        console.warn('Unknown action:', actionType);
    }
  }
  
  // Créer un élément de contenu simulé
  const mockContent = global.document.createElement('div');
  
  // Tester l'attachement des event listeners
  attachEventListeners(mockContent);
}

// Tester la détection des clics
function testClickDetection() {
  console.log('\nTest de détection des clics...');
  
  // Simuler un événement de clic
  function simulateClick(target, actionType = null, notificationId = null) {
    console.log(`Simulation d'un clic sur: ${target}`);
    
    const mockEvent = {
      target: {
        closest: function(selector) {
          if (selector === '.notification-actions' && target === 'action-button') {
            return {
              getAttribute: function(attr) {
                if (attr === 'data-action') return actionType;
                if (attr === 'data-id') return notificationId;
                return null;
              }
            };
          }
          return null;
        },
        getAttribute: function(attr) {
          if (attr === 'data-action') return actionType;
          if (attr === 'data-id') return notificationId;
          return null;
        }
      },
      preventDefault: function() {
        console.log('preventDefault() appelé');
      },
      stopPropagation: function() {
        console.log('stopPropagation() appelé');
      }
    };
    
    // Simuler le gestionnaire de clic
    if (target === 'action-button') {
      console.log('🎯 Action button clicked');
      mockEvent.preventDefault();
      mockEvent.stopPropagation();
      
      const actionType = mockEvent.target.getAttribute('data-action');
      const notificationId = mockEvent.target.getAttribute('data-id');
      
      console.log('Action:', actionType, 'Notification:', notificationId);
    }
  }
  
  // Tester différents types de clics
  simulateClick('action-button', 'reply', 'test123');
  simulateClick('action-button', 'mark-read', 'test456');
  simulateClick('notification-item');
}

// Tester la communication avec le background
function testBackgroundCommunication() {
  console.log('\nTest de communication avec le background...');
  
  async function testMarkAsRead() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'markAsRead',
        notificationId: 'test123'
      });
      
      if (response && response.success) {
        console.log('✅ Mark as read successful');
      } else {
        console.log('❌ Mark as read failed');
      }
    } catch (error) {
      console.log('❌ Error:', error.message);
    }
  }
  
  testMarkAsRead();
}

// Vérifier le code du content script
function checkContentScriptCode() {
  console.log('\nVérification du code du content script...');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    
    // Vérifier les fonctions importantes
    const importantFunctions = [
      'attachEventListeners',
      'handleNotificationAction',
      'handleReplyAction',
      'handleMarkAsReadAction'
    ];
    
    importantFunctions.forEach(funcName => {
      if (contentScript.includes(`function ${funcName}`) || contentScript.includes(`${funcName} =`)) {
        console.log(`✅ Fonction trouvée: ${funcName}`);
      } else {
        console.log(`❌ Fonction manquante: ${funcName}`);
      }
    });
    
    // Vérifier les event listeners
    const eventListenerPatterns = [
      'addEventListener\\(\'click\'',
      'querySelectorAll\\(.*notification-action',
      'data-action',
      'data-id'
    ];
    
    eventListenerPatterns.forEach(pattern => {
      const regex = new RegExp(pattern);
      if (regex.test(contentScript)) {
        console.log(`✅ Pattern trouvé: ${pattern}`);
      } else {
        console.log(`❌ Pattern manquant: ${pattern}`);
      }
    });
    
  } catch (error) {
    console.log('❌ Erreur lors de la lecture du script:', error.message);
  }
}

// Exécuter tous les tests
testEventListeners();
testClickDetection();
testBackgroundCommunication();
checkContentScriptCode();

console.log('\nTest des interactions terminé.');