// Test de validation de la syntaxe JavaScript
const fs = require('fs');
const path = require('path');

describe('Validation de la syntaxe JavaScript', () => {
  test('content.js doit avoir une syntaxe JavaScript valide', () => {
    const contentPath = path.join(__dirname, '../scripts/content.js');
    const content = fs.readFileSync(contentPath, 'utf8');
    
    // Test de compilation avec Node.js
    expect(() => {
      new Function(content);
    }).not.toThrow();
  });

  test('content-new.js doit avoir une syntaxe JavaScript valide', () => {
    const contentPath = path.join(__dirname, '../scripts/content-new.js');
    const content = fs.readFileSync(contentPath, 'utf8');
    
    // Test de compilation avec Node.js
    expect(() => {
      new Function(content);
    }).not.toThrow();
  });

  test('background.js doit avoir une syntaxe JavaScript valide', () => {
    const backgroundPath = path.join(__dirname, '../scripts/background.js');
    const content = fs.readFileSync(backgroundPath, 'utf8');
    
    // Test de compilation avec Node.js
    expect(() => {
      new Function(content);
    }).not.toThrow();
  });

  test('Vérification de l\'équilibre des parenthèses dans content-new.js', () => {
    const contentPath = path.join(__dirname, '../scripts/content-new.js');
    const content = fs.readFileSync(contentPath, 'utf8');
    
    let openParens = 0;
    let closeParens = 0;
    
    for (let char of content) {
      if (char === '(') openParens++;
      if (char === ')') closeParens++;
    }
    
    expect(openParens).toBe(closeParens);
  });

  test('Vérification de l\'équilibre des backticks dans content-new.js', () => {
    const contentPath = path.join(__dirname, '../scripts/content-new.js');
    const content = fs.readFileSync(contentPath, 'utf8');
    
    const backtickCount = (content.match(/`/g) || []).length;
    
    // Le nombre de backticks doit être pair (chaque template literal a un début et une fin)
    expect(backtickCount % 2).toBe(0);
  });

  test('Vérification de l\'absence de caractères Unicode problématiques', () => {
    const files = [
      '../scripts/content.js',
      '../scripts/content-new.js'
    ];
    
    const problematicChars = ['📬', '↻', '×', '🧪', '🔐', '📭'];
    
    files.forEach(file => {
      const filePath = path.join(__dirname, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      problematicChars.forEach(char => {
        expect(content).not.toContain(char);
      });
    });
  });
});