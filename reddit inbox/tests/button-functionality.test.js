const fs = require('fs');
const path = require('path');

describe('Button Functionality Tests', () => {
  let jsContent;

  beforeAll(() => {
    const jsPath = path.join(__dirname, '..', 'scripts', 'content-new.js');
    jsContent = fs.readFileSync(jsPath, 'utf8');
  });

  describe('Refresh Button', () => {
    test('should have refreshNotifications function', () => {
      expect(jsContent).toContain('function refreshNotifications()');
      expect(jsContent).toContain('async function refreshNotifications()');
    });

    test('should have refresh button event listener', () => {
      expect(jsContent).toContain("querySelector('.refresh-button')");
      expect(jsContent).toContain('refreshBtn.addEventListener');
      expect(jsContent).toContain('refreshNotifications()');
    });

    test('should handle loading state for refresh button', () => {
      expect(jsContent).toContain("refreshButton.classList.add('loading')");
      expect(jsContent).toContain('refreshButton.disabled = true');
      expect(jsContent).toContain("refreshButton.innerHTML = '⟳'");
    });

    test('should restore refresh button after operation', () => {
      expect(jsContent).toContain("refreshButton.classList.remove('loading')");
      expect(jsContent).toContain('refreshButton.disabled = false');
      expect(jsContent).toContain("refreshButton.innerHTML = '↻'");
    });

    test('should force refresh notifications', () => {
      expect(jsContent).toContain('force: true');
      expect(jsContent).toContain("action: 'getNotifications'");
    });
  });

  describe('Close Button', () => {
    test('should have close button event listener', () => {
      expect(jsContent).toContain("querySelector('.close-btn')");
      expect(jsContent).toContain('closeBtn.addEventListener');
      expect(jsContent).toContain('closeMiniInbox()');
    });

    test('should prevent default and stop propagation', () => {
      expect(jsContent).toContain('event.preventDefault()');
      expect(jsContent).toContain('event.stopPropagation()');
    });
  });

  describe('Reply Button', () => {
    test('should have reply action handler', () => {
      expect(jsContent).toContain('function handleReplyAction');
      expect(jsContent).toContain("case 'reply':");
      expect(jsContent).toContain('handleReplyAction(notificationId, notificationItem)');
    });

    test('should create quick reply interface', () => {
      expect(jsContent).toContain('function createQuickReplyInterface');
      expect(jsContent).toContain('quick-reply-interface');
      expect(jsContent).toContain('reply-textarea');
    });

    test('should have reply interface buttons', () => {
      expect(jsContent).toContain('reply-send');
      expect(jsContent).toContain('reply-cancel');
      expect(jsContent).toContain('reply-close');
      expect(jsContent).toContain('reply-full');
    });

    test('should handle reply sending', () => {
      expect(jsContent).toContain('function sendQuickReply');
      expect(jsContent).toContain('sendQuickReply(notificationId, text, replyDiv)');
    });
  });

  describe('Mark Read/Unread Buttons', () => {
    test('should have mark as read handler', () => {
      expect(jsContent).toContain('function handleMarkAsReadAction');
      expect(jsContent).toContain("case 'mark-read':");
    });

    test('should have mark as unread handler', () => {
      expect(jsContent).toContain('function handleMarkAsUnreadAction');
      expect(jsContent).toContain("case 'mark-unread':");
    });
  });

  describe('Event Listener Attachment', () => {
    test('should have attachEventListeners function', () => {
      expect(jsContent).toContain('function attachEventListeners');
      expect(jsContent).toContain('attachEventListeners(content)');
    });

    test('should attach to notification actions', () => {
      expect(jsContent).toContain("querySelectorAll('.notification-action')");
      expect(jsContent).toContain('action.addEventListener');
    });

    test('should handle notification action clicks', () => {
      expect(jsContent).toContain('function handleNotificationAction');
      expect(jsContent).toContain('handleNotificationAction(actionType, notificationId');
    });

    test('should have proper event handling', () => {
      expect(jsContent).toContain('event.preventDefault()');
      expect(jsContent).toContain('event.stopPropagation()');
    });
  });

  describe('Button Generation', () => {
    test('should generate notification actions', () => {
      expect(jsContent).toContain('function getNotificationActions');
      expect(jsContent).toContain('notification-action');
      expect(jsContent).toContain('data-action="reply"');
      expect(jsContent).toContain('data-action="mark-read"');
    });

    test('should include refresh button in HTML', () => {
      expect(jsContent).toContain('refresh-button');
      expect(jsContent).toContain('type="button"');
      expect(jsContent).toContain('aria-label="Refresh"');
    });

    test('should include close button in HTML', () => {
      expect(jsContent).toContain('close-btn');
      expect(jsContent).toContain('aria-label="Close"');
    });
  });

  describe('Error Handling', () => {
    test('should handle refresh errors', () => {
      expect(jsContent).toContain('catch (error)');
      expect(jsContent).toContain('Error refreshing notifications');
    });

    test('should handle reply errors', () => {
      expect(jsContent).toContain('sendBtn.disabled = false');
      expect(jsContent).toContain('textarea.disabled = false');
    });
  });

  describe('Debug and Logging', () => {
    test('should have debug logging for buttons', () => {
      expect(jsContent).toContain('console.log');
      expect(jsContent).toContain('Action button clicked');
      expect(jsContent).toContain('Refreshing notifications');
    });

    test('should log action details', () => {
      expect(jsContent).toContain('actionType');
      expect(jsContent).toContain('notificationId');
      expect(jsContent).toContain('notificationItem');
    });
  });
});
