// Test d'analyse précise du bug après autofix
console.log('🔍 ANALYSE PRÉCISE DU BUG APRÈS AUTOFIX');
console.log('='.repeat(60));

const fs = require('fs');

function analyzeCurrentState() {
  console.log('\n📊 ÉTAT ACTUEL DU FICHIER:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    console.log(`Taille du fichier: ${contentScript.length} caractères`);
    console.log(`Nombre de lignes: ${lines.length}`);
    
    // Vérifier les parenthèses
    const openParens = (contentScript.match(/\(/g) || []).length;
    const closeParens = (contentScript.match(/\)/g) || []).length;
    console.log(`Parenthèses: ${openParens} ouvertes, ${closeParens} fermées (balance: ${openParens - closeParens})`);
    
    // Vérifier les accolades
    const openBraces = (contentScript.match(/\{/g) || []).length;
    const closeBraces = (contentScript.match(/\}/g) || []).length;
    console.log(`Accolades: ${openBraces} ouvertes, ${closeBraces} fermées (balance: ${openBraces - closeBraces})`);
    
    // Vérifier les crochets
    const openBrackets = (contentScript.match(/\[/g) || []).length;
    const closeBrackets = (contentScript.match(/\]/g) || []).length;
    console.log(`Crochets: ${openBrackets} ouverts, ${closeBrackets} fermés (balance: ${openBrackets - closeBrackets})`);
    
    return { openParens, closeParens, openBraces, closeBraces, openBrackets, closeBrackets };
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return null;
  }
}

function findExactParenthesesProblem() {
  console.log('\n🔍 LOCALISATION EXACTE DU PROBLÈME DE PARENTHÈSES:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    let balance = 0;
    let problemLines = [];
    
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      const openCount = (line.match(/\(/g) || []).length;
      const closeCount = (line.match(/\)/g) || []).length;
      
      balance += openCount - closeCount;
      
      // Enregistrer les lignes où la balance devient négative
      if (balance < 0) {
        problemLines.push({
          line: lineNum,
          content: line.trim(),
          balance: balance,
          openCount: openCount,
          closeCount: closeCount
        });
      }
    });
    
    console.log(`Balance finale: ${balance}`);
    
    if (problemLines.length > 0) {
      console.log(`\n🚨 ${problemLines.length} ligne(s) où la balance devient négative:`);
      problemLines.slice(0, 5).forEach(problem => {
        console.log(`   Ligne ${problem.line}: balance ${problem.balance}`);
        console.log(`      Parenthèses: ${problem.openCount} ouvertes, ${problem.closeCount} fermées`);
        console.log(`      Code: ${problem.content.substring(0, 80)}...`);
      });
    }
    
    return problemLines;
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return [];
  }
}

function testActualExecution() {
  console.log('\n🧪 TEST D\'EXÉCUTION RÉELLE:');
  
  try {
    // Tester si le fichier peut être exécuté
    const { execSync } = require('child_process');
    
    try {
      execSync('node -c scripts/content-new.js', { stdio: 'pipe' });
      console.log('✅ Syntaxe JavaScript: VALIDE');
    } catch (error) {
      console.log('❌ Syntaxe JavaScript: INVALIDE');
      console.log(`   Erreur: ${error.message}`);
      return false;
    }
    
    // Tester si le fichier peut être lu par Node.js
    try {
      const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
      new Function(contentScript);
      console.log('✅ Exécution JavaScript: POSSIBLE');
      return true;
    } catch (error) {
      console.log('❌ Exécution JavaScript: IMPOSSIBLE');
      console.log(`   Erreur: ${error.message}`);
      console.log(`   Ligne: ${error.lineNumber || 'inconnue'}`);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Erreur lors du test:', error.message);
    return false;
  }
}

function analyzeSpecificLines() {
  console.log('\n🔍 ANALYSE DES LIGNES SPÉCIFIQUES:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    // Analyser les lignes autour de 785
    const linesToCheck = [780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790];
    
    linesToCheck.forEach(lineNum => {
      if (lines[lineNum - 1]) {
        const line = lines[lineNum - 1];
        const openParens = (line.match(/\(/g) || []).length;
        const closeParens = (line.match(/\)/g) || []).length;
        const balance = openParens - closeParens;
        
        console.log(`Ligne ${lineNum}: balance ${balance > 0 ? '+' : ''}${balance}`);
        console.log(`   Parenthèses: ${openParens} ouvertes, ${closeParens} fermées`);
        console.log(`   Code: ${line.trim()}`);
        
        // Vérifier les caractères spéciaux
        if (line.includes('bell')) {
          console.log(`   ⚠️  Contient 'bell'`);
        }
        if (line.includes(')))')) {
          console.log(`   ⚠️  Contient triple parenthèse fermante`);
        }
        
        console.log('');
      }
    });
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

function checkIfExtensionWorks() {
  console.log('\n🎯 DIAGNOSTIC FINAL:');
  
  const state = analyzeCurrentState();
  const problems = findExactParenthesesProblem();
  const canExecute = testActualExecution();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 RÉSUMÉ:');
  console.log('='.repeat(50));
  
  if (state) {
    console.log(`Parenthèses: ${state.openParens} vs ${state.closeParens} (${state.openParens - state.closeParens})`);
    console.log(`Accolades: ${state.openBraces} vs ${state.closeBraces} (${state.openBraces - state.closeBraces})`);
    console.log(`Crochets: ${state.openBrackets} vs ${state.closeBrackets} (${state.openBrackets - state.closeBrackets})`);
  }
  
  console.log(`Lignes problématiques: ${problems.length}`);
  console.log(`Peut s'exécuter: ${canExecute ? 'OUI' : 'NON'}`);
  
  console.log('\n💡 CONCLUSION:');
  if (canExecute) {
    console.log('✅ Le fichier JavaScript est syntaxiquement correct');
    console.log('✅ L\'extension DEVRAIT fonctionner');
    console.log('');
    console.log('🤔 Si l\'extension ne fonctionne toujours pas, le problème est probablement:');
    console.log('   1. Un problème de timing (script chargé trop tôt/tard)');
    console.log('   2. Un conflit avec les styles CSS de Reddit');
    console.log('   3. Un problème de permissions Chrome');
    console.log('   4. Un problème de détection des icônes Reddit');
  } else {
    console.log('❌ Le fichier JavaScript a des erreurs de syntaxe');
    console.log('❌ L\'extension NE PEUT PAS fonctionner');
    console.log('');
    console.log('🔧 Actions requises:');
    console.log('   1. Corriger les erreurs de syntaxe identifiées');
    console.log('   2. Équilibrer les parenthèses/accolades/crochets');
  }
}

// Exécuter l'analyse complète
analyzeCurrentState();
findExactParenthesesProblem();
testActualExecution();
analyzeSpecificLines();
checkIfExtensionWorks();