// Tests unitaires pour l'authentification Reddit OAuth2

// Mock des APIs Chrome pour les tests
global.chrome = {
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn()
    }
  },
  identity: {
    getRedirectURL: jest.fn(() => 'https://extension-id.chromiumapp.org/'),
    launchWebAuthFlow: jest.fn()
  },
  runtime: {
    sendMessage: jest.fn()
  }
};

// Mock de fetch pour les tests
global.fetch = jest.fn();

describe('Reddit OAuth2 Authentication', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('buildAuthUrl', () => {
    test('should build correct authorization URL', () => {
      const REDDIT_CONFIG = {
        clientId: 'test_client_id',
        redirectUri: 'https://extension-id.chromiumapp.org/',
        scope: 'identity read privatemessages',
        responseType: 'code',
        duration: 'permanent'
      };

      const authUrl = buildAuthUrl(REDDIT_CONFIG);
      
      expect(authUrl).toContain('https://www.reddit.com/api/v1/authorize');
      expect(authUrl).toContain('client_id=test_client_id');
      expect(authUrl).toContain('response_type=code');
      expect(authUrl).toContain('scope=identity%20read%20privatemessages');
      expect(authUrl).toContain('duration=permanent');
    });
  });

  describe('exchangeCodeForToken', () => {
    test('should exchange authorization code for access token', async () => {
      const mockTokenResponse = {
        access_token: 'test_access_token',
        refresh_token: 'test_refresh_token',
        expires_in: 3600,
        token_type: 'bearer'
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTokenResponse)
      });

      const result = await exchangeCodeForToken('test_auth_code', 'test_client_id');
      
      expect(fetch).toHaveBeenCalledWith(
        'https://www.reddit.com/api/v1/access_token',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': expect.stringContaining('Basic')
          })
        })
      );
      
      expect(result).toEqual(mockTokenResponse);
    });

    test('should throw error on failed token exchange', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({
          error: 'invalid_grant',
          error_description: 'Invalid authorization code'
        })
      });

      await expect(exchangeCodeForToken('invalid_code', 'test_client_id'))
        .rejects.toThrow('Token exchange failed: Invalid authorization code');
    });
  });

  describe('refreshAccessToken', () => {
    test('should refresh access token successfully', async () => {
      const mockTokenResponse = {
        access_token: 'new_access_token',
        expires_in: 3600,
        token_type: 'bearer'
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTokenResponse)
      });

      const result = await refreshAccessToken('test_refresh_token', 'test_client_id');
      
      expect(fetch).toHaveBeenCalledWith(
        'https://www.reddit.com/api/v1/access_token',
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('grant_type=refresh_token')
        })
      );
      
      expect(result).toEqual(mockTokenResponse);
    });
  });

  describe('storeTokens', () => {
    test('should store tokens with correct expiry time', async () => {
      const tokenData = {
        access_token: 'test_token',
        refresh_token: 'test_refresh',
        expires_in: 3600,
        token_type: 'bearer'
      };

      const mockDate = new Date('2023-01-01T00:00:00Z');
      jest.spyOn(Date, 'now').mockReturnValue(mockDate.getTime());

      await storeTokens(tokenData);

      expect(chrome.storage.local.set).toHaveBeenCalledWith({
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        tokenExpiry: mockDate.getTime() + (3600 * 1000),
        tokenType: 'bearer'
      });
    });
  });

  describe('fetchUserInfo', () => {
    test('should fetch user information successfully', async () => {
      const mockUserInfo = {
        name: 'test_user',
        id: 'user123',
        created_utc: 1234567890
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockUserInfo)
      });

      const result = await fetchUserInfo('test_access_token');
      
      expect(fetch).toHaveBeenCalledWith(
        'https://oauth.reddit.com/api/v1/me',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test_access_token',
            'User-Agent': 'RedditMiniInbox/1.0.0'
          })
        })
      );
      
      expect(result).toEqual(mockUserInfo);
    });

    test('should throw error on failed user info fetch', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Unauthorized'
      });

      await expect(fetchUserInfo('invalid_token'))
        .rejects.toThrow('Failed to fetch user info: Unauthorized');
    });
  });

  describe('Authentication Flow Integration', () => {
    test('should handle complete authentication flow', async () => {
      // Mock successful OAuth flow
      chrome.identity.launchWebAuthFlow.mockResolvedValueOnce(
        'https://extension-id.chromiumapp.org/?code=test_auth_code&state=test_state'
      );

      // Mock token exchange
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          access_token: 'test_access_token',
          refresh_token: 'test_refresh_token',
          expires_in: 3600,
          token_type: 'bearer'
        })
      });

      // Mock user info fetch
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          name: 'test_user',
          id: 'user123'
        })
      });

      // Mock storage operations
      chrome.storage.local.set.mockResolvedValue();

      // Test the authentication handler
      const result = await handleAuthenticate();

      expect(result.success).toBe(true);
      expect(result.username).toBe('test_user');
      expect(chrome.storage.local.set).toHaveBeenCalledWith(
        expect.objectContaining({
          authenticated: true,
          username: 'test_user'
        })
      );
    });

    test('should handle OAuth error', async () => {
      chrome.identity.launchWebAuthFlow.mockResolvedValueOnce(
        'https://extension-id.chromiumapp.org/?error=access_denied&state=test_state'
      );

      const result = await handleAuthenticate();

      expect(result.success).toBe(false);
      expect(result.error).toContain('OAuth error: access_denied');
    });
  });

  describe('Token Management', () => {
    test('should detect expired tokens', () => {
      const expiredTime = Date.now() - 1000; // 1 second ago
      const validTime = Date.now() + 3600000; // 1 hour from now

      expect(isTokenExpired(expiredTime)).toBe(true);
      expect(isTokenExpired(validTime)).toBe(false);
    });

    test('should handle token refresh on API calls', async () => {
      // Mock expired token scenario
      chrome.storage.local.get.mockResolvedValueOnce({
        accessToken: 'expired_token',
        refreshToken: 'valid_refresh_token',
        tokenExpiry: Date.now() - 1000 // Expired
      });

      // Mock successful token refresh
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          access_token: 'new_access_token',
          expires_in: 3600
        })
      });

      // Mock successful API call with new token
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: { children: [] }
        })
      });

      const result = await handleGetNotifications({ force: false });

      expect(result.success).toBe(true);
      expect(fetch).toHaveBeenCalledTimes(2); // Token refresh + API call
    });
  });
});

// Fonctions utilitaires pour les tests (normalement dans le background script)
function buildAuthUrl(config) {
  const params = new URLSearchParams({
    client_id: config.clientId,
    response_type: config.responseType,
    redirect_uri: config.redirectUri,
    duration: config.duration,
    scope: config.scope,
    state: 'test_state'
  });
  
  return `https://www.reddit.com/api/v1/authorize?${params.toString()}`;
}

async function exchangeCodeForToken(authCode, clientId) {
  const response = await fetch('https://www.reddit.com/api/v1/access_token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${btoa(clientId + ':')}`
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code: authCode,
      redirect_uri: chrome.identity.getRedirectURL()
    })
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Token exchange failed: ${errorData.error_description || errorData.error}`);
  }
  
  return await response.json();
}

async function refreshAccessToken(refreshToken, clientId) {
  const response = await fetch('https://www.reddit.com/api/v1/access_token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${btoa(clientId + ':')}`
    },
    body: new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken
    })
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Token refresh failed: ${errorData.error_description || errorData.error}`);
  }
  
  return await response.json();
}

async function storeTokens(tokenData) {
  const expiryTime = Date.now() + (tokenData.expires_in * 1000);
  
  await chrome.storage.local.set({
    accessToken: tokenData.access_token,
    refreshToken: tokenData.refresh_token,
    tokenExpiry: expiryTime,
    tokenType: tokenData.token_type || 'bearer'
  });
}

async function fetchUserInfo(accessToken) {
  const response = await fetch('https://oauth.reddit.com/api/v1/me', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'User-Agent': 'RedditMiniInbox/1.0.0'
    }
  });
  
  if (!response.ok) {
    throw new Error(`Failed to fetch user info: ${response.statusText}`);
  }
  
  return await response.json();
}

function isTokenExpired(tokenExpiry) {
  return tokenExpiry <= Date.now();
}

async function handleAuthenticate() {
  try {
    const redirectUrl = await chrome.identity.launchWebAuthFlow({
      url: buildAuthUrl({
        clientId: 'test_client_id',
        redirectUri: chrome.identity.getRedirectURL(),
        scope: 'identity read privatemessages',
        responseType: 'code',
        duration: 'permanent'
      }),
      interactive: true
    });
    
    const urlParams = new URLSearchParams(new URL(redirectUrl).search);
    const authCode = urlParams.get('code');
    const error = urlParams.get('error');
    
    if (error) {
      return { success: false, error: `OAuth error: ${error}` };
    }
    
    if (!authCode) {
      return { success: false, error: 'No authorization code received' };
    }
    
    const tokenData = await exchangeCodeForToken(authCode, 'test_client_id');
    await storeTokens(tokenData);
    
    const userInfo = await fetchUserInfo(tokenData.access_token);
    
    await chrome.storage.local.set({
      authenticated: true,
      username: userInfo.name,
      authTime: Date.now()
    });
    
    return { success: true, username: userInfo.name };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function handleGetNotifications(request) {
  try {
    const authData = await chrome.storage.local.get(['accessToken', 'tokenExpiry', 'refreshToken']);
    
    if (!authData.accessToken) {
      return { success: false, error: 'User not authenticated' };
    }
    
    // Check if token is expired
    if (authData.tokenExpiry <= Date.now()) {
      if (authData.refreshToken) {
        const tokenData = await refreshAccessToken(authData.refreshToken, 'test_client_id');
        await storeTokens(tokenData);
        authData.accessToken = tokenData.access_token;
      } else {
        return { success: false, error: 'Token expired and no refresh token available' };
      }
    }
    
    // Fetch notifications
    const response = await fetch('https://oauth.reddit.com/message/inbox?limit=25', {
      headers: {
        'Authorization': `Bearer ${authData.accessToken}`,
        'User-Agent': 'RedditMiniInbox/1.0.0'
      }
    });
    
    if (!response.ok) {
      return { success: false, error: `Failed to fetch notifications: ${response.statusText}` };
    }
    
    const data = await response.json();
    return { success: true, notifications: data.data.children };
  } catch (error) {
    return { success: false, error: error.message };
  }
}