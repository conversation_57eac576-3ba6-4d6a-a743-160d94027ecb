// Tests unitaires pour les appels API Reddit

// Mock des APIs Chrome pour les tests
global.chrome = {
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn()
    }
  }
};

// Mock de fetch pour les tests
global.fetch = jest.fn();

// Configuration de test
const TEST_CONFIG = {
  accessToken: 'test_access_token',
  mockNotificationData: {
    data: {
      children: [
        {
          data: {
            id: 'test123',
            name: 't4_test123',
            author: 'test_user',
            subject: 'Test notification',
            body: 'This is a test notification body',
            created_utc: 1640995200,
            new: true,
            was_comment: true,
            subreddit: 'test',
            context: '/r/test/comments/abc123/_/test123/',
            score: 5
          }
        },
        {
          data: {
            id: 'test456',
            name: 't4_test456',
            author: 'another_user',
            subject: 'Private message',
            body: 'This is a private message',
            created_utc: 1640995100,
            new: false,
            was_comment: false
          }
        }
      ]
    }
  }
};

describe('Reddit API Calls', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset rate limit state
    global.RATE_LIMIT = {
      maxRequests: 60,
      windowMs: 60 * 1000,
      requests: [],
      backoffMultiplier: 2,
      maxBackoffMs: 30000
    };
  });

  describe('fetchNotifications', () => {
    test('should fetch notifications successfully', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(TEST_CONFIG.mockNotificationData)
      });

      const notifications = await fetchNotifications(TEST_CONFIG.accessToken);

      expect(fetch).toHaveBeenCalledWith(
        'https://oauth.reddit.com/message/inbox?limit=25&mark=false',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': `Bearer ${TEST_CONFIG.accessToken}`,
            'User-Agent': 'RedditMiniInbox/1.0.0'
          })
        })
      );

      expect(notifications).toHaveLength(2);
      expect(notifications[0]).toMatchObject({
        id: 'test123',
        author: 'test_user',
        subject: 'Test notification',
        new: true,
        type: 'comment_reply'
      });
    });

    test('should fetch unread notifications only when specified', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(TEST_CONFIG.mockNotificationData)
      });

      await fetchNotifications(TEST_CONFIG.accessToken, { unreadOnly: true });

      expect(fetch).toHaveBeenCalledWith(
        'https://oauth.reddit.com/message/unread?limit=25&mark=false',
        expect.any(Object)
      );
    });

    test('should handle rate limiting with retry', async () => {
      // First call returns rate limit error
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        headers: new Map([['Retry-After', '60']]),
        statusText: 'Too Many Requests'
      });

      // Second call succeeds
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(TEST_CONFIG.mockNotificationData)
      });

      // Mock sleep function to avoid actual delays in tests
      const originalSleep = global.sleep;
      global.sleep = jest.fn().mockResolvedValue();

      const notifications = await fetchNotifications(TEST_CONFIG.accessToken);

      expect(fetch).toHaveBeenCalledTimes(2);
      expect(notifications).toHaveLength(2);
      expect(global.sleep).toHaveBeenCalledWith(expect.any(Number));

      global.sleep = originalSleep;
    });

    test('should handle authentication errors', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      await expect(fetchNotifications(TEST_CONFIG.accessToken))
        .rejects.toThrow('Authentication failed - token may be expired');
    });

    test('should handle server errors with retry', async () => {
      // First call returns server error
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      // Second call succeeds
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(TEST_CONFIG.mockNotificationData)
      });

      const originalSleep = global.sleep;
      global.sleep = jest.fn().mockResolvedValue();

      const notifications = await fetchNotifications(TEST_CONFIG.accessToken);

      expect(fetch).toHaveBeenCalledTimes(2);
      expect(notifications).toHaveLength(2);

      global.sleep = originalSleep;
    });

    test('should handle invalid response structure', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve({ invalid: 'structure' })
      });

      await expect(fetchNotifications(TEST_CONFIG.accessToken))
        .rejects.toThrow('Invalid response structure from Reddit API');
    });
  });

  describe('parseNotifications', () => {
    test('should parse comment reply notifications correctly', () => {
      const mockData = [{
        data: {
          id: 'test123',
          author: 'test_user',
          subject: 'Comment reply',
          body: 'This is a reply',
          created_utc: 1640995200,
          new: true,
          was_comment: true,
          subreddit: 'test',
          context: '/r/test/comments/abc/_/test123/',
          parent_id: 't1_parent123',
          link_id: 't3_link123',
          link_title: 'Original Post Title'
        }
      }];

      const notifications = parseNotifications(mockData);

      expect(notifications).toHaveLength(1);
      expect(notifications[0]).toMatchObject({
        id: 'test123',
        type: 'comment_reply',
        author: 'test_user',
        subject: 'Comment reply',
        body: 'This is a reply',
        new: true,
        parent_id: 't1_parent123',
        link_id: 't3_link123',
        link_title: 'Original Post Title'
      });
    });

    test('should parse private message notifications correctly', () => {
      const mockData = [{
        data: {
          id: 'pm123',
          author: 'sender_user',
          subject: 'Private Message',
          body: 'Hello there!',
          created_utc: 1640995200,
          new: false,
          was_comment: false,
          dest: 'recipient_user'
        }
      }];

      const notifications = parseNotifications(mockData);

      expect(notifications).toHaveLength(1);
      expect(notifications[0]).toMatchObject({
        id: 'pm123',
        type: 'private_message',
        author: 'sender_user',
        subject: 'Private Message',
        body: 'Hello there!',
        new: false,
        dest: 'recipient_user'
      });
    });

    test('should parse username mention notifications correctly', () => {
      const mockData = [{
        data: {
          id: 'mention123',
          author: 'mentioning_user',
          subject: 'Username mention',
          body: 'Hey u/test_user, check this out!',
          created_utc: 1640995200,
          new: true,
          was_comment: true,
          type: 'username_mention',
          subreddit: 'test',
          context: '/r/test/comments/abc/_/mention123/'
        }
      }];

      const notifications = parseNotifications(mockData);

      expect(notifications).toHaveLength(1);
      expect(notifications[0]).toMatchObject({
        id: 'mention123',
        type: 'username_mention',
        author: 'mentioning_user',
        subject: 'Username mention',
        new: true
      });
    });

    test('should handle malformed notification data gracefully', () => {
      const mockData = [
        { data: { id: 'valid123', author: 'user', subject: 'Valid', body: 'OK' } },
        { data: null }, // Invalid structure
        { invalid: 'structure' }, // Missing data field
        { data: { author: 'user' } } // Missing required id field
      ];

      const notifications = parseNotifications(mockData);

      expect(notifications).toHaveLength(1);
      expect(notifications[0].id).toBe('valid123');
    });

    test('should sanitize HTML content properly', () => {
      const mockData = [{
        data: {
          id: 'html123',
          author: 'test_user',
          subject: 'HTML &amp; Content',
          body: 'This has &lt;script&gt;alert("xss")&lt;/script&gt; and &quot;quotes&quot;',
          created_utc: 1640995200,
          new: true
        }
      }];

      const notifications = parseNotifications(mockData);

      expect(notifications[0].subject).toBe('HTML & Content');
      expect(notifications[0].body).toBe('This has alert("xss") and "quotes"');
    });
  });

  describe('markNotificationAsRead', () => {
    test('should mark notification as read successfully', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200
      });

      await markNotificationAsRead(TEST_CONFIG.accessToken, 'test123');

      expect(fetch).toHaveBeenCalledWith(
        'https://oauth.reddit.com/api/read_message',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${TEST_CONFIG.accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'RedditMiniInbox/1.0.0'
          }),
          body: expect.stringContaining('id=test123')
        })
      );
    });

    test('should handle mark as read API errors', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      });

      await expect(markNotificationAsRead(TEST_CONFIG.accessToken, 'test123'))
        .rejects.toThrow('Failed to mark as read: Forbidden');
    });
  });

  describe('Rate Limiting', () => {
    test('should track API requests for rate limiting', async () => {
      fetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(TEST_CONFIG.mockNotificationData)
      });

      // Make multiple requests
      await makeRedditAPIRequest('https://test.com');
      await makeRedditAPIRequest('https://test.com');
      await makeRedditAPIRequest('https://test.com');

      expect(global.RATE_LIMIT.requests).toHaveLength(3);
    });

    test('should clean up old rate limit entries', async () => {
      const now = Date.now();
      global.RATE_LIMIT.requests = [
        now - 70000, // 70 seconds ago (should be cleaned)
        now - 30000, // 30 seconds ago (should remain)
        now - 10000  // 10 seconds ago (should remain)
      ];

      await checkRateLimit();

      expect(global.RATE_LIMIT.requests).toHaveLength(2);
    });

    test('should calculate exponential backoff correctly', () => {
      expect(calculateBackoff(0)).toBe(1000);
      expect(calculateBackoff(1)).toBe(2000);
      expect(calculateBackoff(2)).toBe(4000);
      expect(calculateBackoff(10)).toBe(30000); // Should cap at maxBackoffMs
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(fetchNotifications(TEST_CONFIG.accessToken))
        .rejects.toThrow('Network error');
    });

    test('should handle JSON parsing errors', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.reject(new Error('Invalid JSON'))
      });

      await expect(fetchNotifications(TEST_CONFIG.accessToken))
        .rejects.toThrow('Invalid JSON');
    });

    test('should handle missing authorization token', async () => {
      await expect(fetchNotifications(null))
        .rejects.toThrow();
    });
  });

  describe('Cache Integration', () => {
    test('should update cache after successful fetch', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(TEST_CONFIG.mockNotificationData)
      });

      const notifications = await fetchNotifications(TEST_CONFIG.accessToken);
      
      // Simulate cache update (this would normally happen in handleGetNotifications)
      const mockCache = {
        data: notifications,
        lastFetch: Date.now(),
        expiry: 5 * 60 * 1000
      };

      expect(mockCache.data).toHaveLength(2);
      expect(mockCache.lastFetch).toBeGreaterThan(0);
    });

    test('should count unread notifications correctly', () => {
      const notifications = [
        { id: '1', new: true },
        { id: '2', new: false },
        { id: '3', new: true },
        { id: '4', new: false }
      ];

      const unreadCount = countUnreadNotifications(notifications);
      expect(unreadCount).toBe(2);
    });

    test('should update notification status in cache', () => {
      const notifications = [
        { id: 'test123', new: true },
        { id: 'test456', new: false }
      ];

      const mockCache = { data: notifications };
      
      // Simulate updating cache
      const notification = mockCache.data.find(n => n.id === 'test123');
      if (notification) {
        notification.new = false;
      }

      expect(mockCache.data[0].new).toBe(false);
      expect(mockCache.data[1].new).toBe(false);
    });
  });
});

// Helper functions for tests (normally in background script)
function parseNotifications(children) {
  return children
    .map(item => {
      try {
        return parseNotification(item);
      } catch (error) {
        console.warn('Failed to parse notification:', error);
        return null;
      }
    })
    .filter(notification => notification !== null);
}

function parseNotification(item) {
  if (!item || !item.data) {
    throw new Error('Invalid notification item structure');
  }
  
  const data = item.data;
  
  if (!data.id) {
    throw new Error('Notification missing required id field');
  }
  
  const type = determineNotificationType(data);
  
  const notification = {
    id: data.id,
    fullname: data.name || `t4_${data.id}`,
    type: type,
    author: sanitizeString(data.author) || '[deleted]',
    subject: sanitizeString(data.subject) || 'No subject',
    body: sanitizeString(data.body) || sanitizeString(data.body_html) || '',
    created_utc: data.created_utc || 0,
    new: Boolean(data.new),
    permalink: buildPermalink(data),
    subreddit: sanitizeString(data.subreddit),
    context: sanitizeString(data.context),
    score: data.score || 0,
    distinguished: data.distinguished || null,
    was_comment: Boolean(data.was_comment)
  };
  
  addTypeSpecificData(notification, data, type);
  
  return notification;
}

function determineNotificationType(data) {
  if (data.was_comment) {
    if (data.type === 'username_mention') {
      return 'username_mention';
    }
    return 'comment_reply';
  }
  
  if (data.type) {
    return data.type;
  }
  
  if (data.subreddit) {
    return 'post_reply';
  }
  
  return 'private_message';
}

function buildPermalink(data) {
  if (data.context) {
    return data.context;
  }
  
  if (data.permalink) {
    return data.permalink;
  }
  
  if (data.subreddit && data.link_id) {
    const linkId = data.link_id.replace('t3_', '');
    return `/r/${data.subreddit}/comments/${linkId}/_/${data.id}/`;
  }
  
  return null;
}

function addTypeSpecificData(notification, data, type) {
  switch (type) {
    case 'comment_reply':
    case 'username_mention':
      notification.parent_id = data.parent_id;
      notification.link_id = data.link_id;
      notification.link_title = sanitizeString(data.link_title);
      break;
    
    case 'post_reply':
      notification.link_id = data.link_id;
      notification.link_title = sanitizeString(data.link_title);
      break;
    
    case 'private_message':
      notification.dest = sanitizeString(data.dest);
      break;
  }
}

function sanitizeString(str) {
  if (typeof str !== 'string') {
    return null;
  }
  
  const decoded = str
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'")
    .replace(/&#x2F;/g, '/');
  
  const cleaned = decoded
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '');
  
  return cleaned.trim();
}

async function makeRedditAPIRequest(url, options = {}) {
  const response = await fetch(url, options);
  
  global.RATE_LIMIT.requests.push(Date.now());
  
  const cutoff = Date.now() - global.RATE_LIMIT.windowMs;
  global.RATE_LIMIT.requests = global.RATE_LIMIT.requests.filter(time => time > cutoff);
  
  if (response.status === 429) {
    const retryAfter = response.headers.get('Retry-After');
    const error = new Error('Rate limit exceeded');
    error.name = 'RateLimitError';
    error.retryAfter = retryAfter ? parseInt(retryAfter) * 1000 : 60000;
    throw error;
  }
  
  if (!response.ok) {
    const error = new Error(`Reddit API error: ${response.statusText}`);
    error.status = response.status;
    error.response = response;
    throw error;
  }
  
  return response;
}

async function markNotificationAsRead(accessToken, notificationId) {
  const response = await fetch('https://oauth.reddit.com/api/read_message', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': 'RedditMiniInbox/1.0.0'
    },
    body: new URLSearchParams({
      id: notificationId
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to mark as read: ${response.statusText}`);
  }
}

async function checkRateLimit() {
  const now = Date.now();
  const cutoff = now - global.RATE_LIMIT.windowMs;
  
  global.RATE_LIMIT.requests = global.RATE_LIMIT.requests.filter(time => time > cutoff);
  
  if (global.RATE_LIMIT.requests.length >= global.RATE_LIMIT.maxRequests) {
    const oldestRequest = Math.min(...global.RATE_LIMIT.requests);
    const waitTime = global.RATE_LIMIT.windowMs - (now - oldestRequest);
    
    if (waitTime > 0) {
      console.log(`Rate limit reached, waiting ${waitTime}ms...`);
      await sleep(waitTime);
    }
  }
}

function calculateBackoff(retryCount) {
  const baseDelay = 1000;
  const delay = baseDelay * Math.pow(global.RATE_LIMIT.backoffMultiplier, retryCount);
  return Math.min(delay, global.RATE_LIMIT.maxBackoffMs);
}

function countUnreadNotifications(notifications) {
  return notifications.filter(n => n.new).length;
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}