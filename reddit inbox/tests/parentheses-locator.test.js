// Test pour localiser précisément la parenthèse manquante
console.log('🔍 LOCALISATION PRÉCISE DE LA PARENTHÈSE MANQUANTE');
console.log('='.repeat(60));

const fs = require('fs');

function locateExactParenthesesIssue() {
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    let runningBalance = 0;
    let criticalLines = [];
    let functionStack = [];
    let currentFunction = null;
    
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      const trimmedLine = line.trim();
      
      // Détecter les débuts de fonctions
      const functionMatch = trimmedLine.match(/function\s+(\w+)/);
      if (functionMatch) {
        currentFunction = functionMatch[1];
        functionStack.push({ name: currentFunction, line: lineNum, balance: runningBalance });
      }
      
      // Compter les parenthèses
      const openParens = (line.match(/\(/g) || []).length;
      const closeParens = (line.match(/\)/g) || []).length;
      const lineBalance = openParens - closeParens;
      
      runningBalance += lineBalance;
      
      // Enregistrer les lignes critiques
      if (lineBalance !== 0 || runningBalance < 0) {
        criticalLines.push({
          line: lineNum,
          content: trimmedLine,
          lineBalance: lineBalance,
          runningBalance: runningBalance,
          function: currentFunction,
          openParens: openParens,
          closeParens: closeParens
        });
      }
      
      // Détecter la fin de fonction (approximatif)
      if (trimmedLine === '}' && functionStack.length > 0) {
        const func = functionStack.pop();
        if (runningBalance !== func.balance) {
          console.log(`⚠️  Fonction ${func.name} (ligne ${func.line}): déséquilibre détecté`);
        }
        currentFunction = functionStack.length > 0 ? functionStack[functionStack.length - 1].name : null;
      }
    });
    
    console.log(`📊 Balance finale: ${runningBalance}`);
    console.log(`🚨 ${criticalLines.length} lignes critiques détectées\n`);
    
    // Afficher les lignes les plus problématiques
    console.log('🔍 LIGNES LES PLUS PROBLÉMATIQUES:');
    
    // Trouver où la balance devient négative
    const negativeBalanceLines = criticalLines.filter(l => l.runningBalance < 0);
    if (negativeBalanceLines.length > 0) {
      console.log('\n❌ LIGNES OÙ LA BALANCE DEVIENT NÉGATIVE:');
      negativeBalanceLines.slice(0, 5).forEach(line => {
        console.log(`   Ligne ${line.line}: balance ${line.runningBalance} (${line.lineBalance > 0 ? '+' : ''}${line.lineBalance})`);
        console.log(`      Fonction: ${line.function || 'global'}`);
        console.log(`      Code: ${line.content.substring(0, 80)}...`);
        console.log(`      Parenthèses: ${line.openParens} ouvertes, ${line.closeParens} fermées\n`);
      });
    }
    
    // Trouver les lignes avec le plus grand déséquilibre
    const biggestImbalances = criticalLines
      .filter(l => Math.abs(l.lineBalance) > 1)
      .sort((a, b) => Math.abs(b.lineBalance) - Math.abs(a.lineBalance));
    
    if (biggestImbalances.length > 0) {
      console.log('\n⚠️  LIGNES AVEC LE PLUS GRAND DÉSÉQUILIBRE:');
      biggestImbalances.slice(0, 5).forEach(line => {
        console.log(`   Ligne ${line.line}: ${line.lineBalance > 0 ? '+' : ''}${line.lineBalance}`);
        console.log(`      Fonction: ${line.function || 'global'}`);
        console.log(`      Code: ${line.content.substring(0, 80)}...`);
        console.log(`      Parenthèses: ${line.openParens} ouvertes, ${line.closeParens} fermées\n`);
      });
    }
    
    // Analyser par fonction
    console.log('\n📋 ANALYSE PAR FONCTION:');
    const functionBalances = {};
    
    criticalLines.forEach(line => {
      const func = line.function || 'global';
      if (!functionBalances[func]) {
        functionBalances[func] = { balance: 0, lines: [] };
      }
      functionBalances[func].balance += line.lineBalance;
      functionBalances[func].lines.push(line.line);
    });
    
    Object.entries(functionBalances).forEach(([funcName, data]) => {
      if (data.balance !== 0) {
        console.log(`   ${funcName}: balance ${data.balance > 0 ? '+' : ''}${data.balance} (lignes: ${data.lines.slice(0, 3).join(', ')}${data.lines.length > 3 ? '...' : ''})`);
      }
    });
    
    return {
      finalBalance: runningBalance,
      criticalLines: criticalLines,
      negativeLines: negativeBalanceLines,
      functionBalances: functionBalances
    };
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return null;
  }
}

// Test pour identifier les caractères Unicode problématiques avec contexte
function analyzeUnicodeWithContext() {
  console.log('\n🔤 ANALYSE DÉTAILLÉE DES CARACTÈRES UNICODE:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    const unicodeChars = [
      { char: '📬', replacement: '[MAILBOX]', context: 'HTML template' },
      { char: '↻', replacement: '↻', context: 'Refresh button' },
      { char: '×', replacement: '×', context: 'Close button' },
      { char: '🧪', replacement: '[TEST]', context: 'Test indicator' },
      { char: '🔐', replacement: '[LOCK]', context: 'Auth required' }
    ];
    
    unicodeChars.forEach(({ char, replacement, context }) => {
      console.log(`\n🔍 Analyse de ${char} (${context}):`);
      
      lines.forEach((line, index) => {
        if (line.includes(char)) {
          const lineNum = index + 1;
          console.log(`   Ligne ${lineNum}: ${line.trim()}`);
          
          // Vérifier si c'est dans un template literal
          const beforeChar = line.substring(0, line.indexOf(char));
          const afterChar = line.substring(line.indexOf(char) + 1);
          
          const backticksBefore = (beforeChar.match(/`/g) || []).length;
          const backticksAfter = (afterChar.match(/`/g) || []).length;
          
          if (backticksBefore % 2 === 1) {
            console.log(`      ✅ Dans un template literal`);
          } else {
            console.log(`      ⚠️  Pas dans un template literal`);
          }
          
          console.log(`      💡 Remplacement suggéré: ${replacement}`);
        }
      });
    });
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
  }
}

// Test pour vérifier les template literals en détail
function analyzeTemplateLiteralsDetailed() {
  console.log('\n📝 ANALYSE DÉTAILLÉE DES TEMPLATE LITERALS:');
  
  try {
    const contentScript = fs.readFileSync('./scripts/content-new.js', 'utf8');
    const lines = contentScript.split('\n');
    
    let inTemplate = false;
    let templateStart = null;
    let templates = [];
    
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      const backticks = (line.match(/`/g) || []).length;
      
      if (backticks > 0) {
        console.log(`Ligne ${lineNum}: ${backticks} backtick(s) - ${inTemplate ? 'DANS' : 'HORS'} template`);
        console.log(`   ${line.trim().substring(0, 60)}...`);
        
        if (backticks % 2 === 1) {
          if (!inTemplate) {
            // Début de template
            inTemplate = true;
            templateStart = lineNum;
            console.log(`   🟢 DÉBUT de template`);
          } else {
            // Fin de template
            inTemplate = false;
            templates.push({
              start: templateStart,
              end: lineNum,
              length: lineNum - templateStart + 1
            });
            console.log(`   🔴 FIN de template (lignes ${templateStart}-${lineNum})`);
            templateStart = null;
          }
        }
      }
    });
    
    if (inTemplate) {
      console.log(`\n⚠️  Template literal non fermé depuis la ligne ${templateStart}`);
    }
    
    console.log(`\n📊 ${templates.length} template(s) literal(s) détecté(s):`);
    templates.forEach((template, index) => {
      console.log(`   Template ${index + 1}: lignes ${template.start}-${template.end} (${template.length} lignes)`);
    });
    
    return { templates, unclosed: inTemplate ? templateStart : null };
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    return { templates: [], unclosed: null };
  }
}

// Exécuter toutes les analyses
console.log('🚀 DÉMARRAGE DE L\'ANALYSE COMPLÈTE...\n');

const parenResult = locateExactParenthesesIssue();
analyzeUnicodeWithContext();
const templateResult = analyzeTemplateLiteralsDetailed();

console.log('\n' + '='.repeat(60));
console.log('🎯 RÉSUMÉ FINAL DU BUG:');
console.log('='.repeat(60));

if (parenResult) {
  console.log(`🔧 PARENTHÈSES: Balance finale ${parenResult.finalBalance}`);
  if (parenResult.negativeLines.length > 0) {
    console.log(`   ❌ ${parenResult.negativeLines.length} ligne(s) avec balance négative`);
    console.log(`   🎯 Première ligne problématique: ${parenResult.negativeLines[0].line}`);
  }
}

if (templateResult.unclosed) {
  console.log(`📝 TEMPLATE LITERAL: Non fermé depuis la ligne ${templateResult.unclosed}`);
} else {
  console.log(`📝 TEMPLATE LITERALS: ${templateResult.templates.length} correctement fermés`);
}

console.log('\n💡 CONCLUSION:');
console.log('   Le bug principal semble être:');
if (parenResult && parenResult.finalBalance !== 0) {
  console.log(`   1. 🔧 Parenthèse manquante (balance: ${parenResult.finalBalance})`);
}
console.log('   2. 🔤 Caractères Unicode qui peuvent causer des problèmes d\'encodage');
if (templateResult.unclosed) {
  console.log('   3. 📝 Template literal non fermé');
}

console.log('\n🚨 CES BUGS EMPÊCHENT L\'EXTENSION DE FONCTIONNER CORRECTEMENT!');