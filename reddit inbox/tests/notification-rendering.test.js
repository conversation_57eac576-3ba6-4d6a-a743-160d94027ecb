// Test du rendu des notifications
const fs = require('fs');

console.log('Test du rendu des notifications...');

// Simuler un environnement DOM
global.document = {
  createElement: function(tag) {
    return {
      tagName: tag.toUpperCase(),
      id: '',
      className: '',
      innerHTML: '',
      style: {},
      appendChild: function() {},
      remove: function() {},
      querySelector: function(selector) {
        console.log(`querySelector appelé avec: ${selector}`);
        return {
          innerHTML: '',
          textContent: '',
          style: {},
          classList: {
            add: function() {},
            remove: function() {}
          }
        };
      },
      querySelectorAll: function(selector) {
        console.log(`querySelectorAll appelé avec: ${selector}`);
        return [];
      },
      classList: {
        add: function(className) {
          console.log(`Classe ajoutée: ${className}`);
        },
        remove: function(className) {
          console.log(`Classe supprimée: ${className}`);
        },
        contains: function() { return false; }
      },
      getAttribute: function() { return null; },
      setAttribute: function() {},
      addEventListener: function() {}
    };
  },
  body: {
    appendChild: function() {}
  }
};

// Simuler les données de notification
const mockNotifications = [
  {
    id: 'test1',
    type: 'comment_reply',
    author: 'test_user',
    subject: 'Test notification',
    body: 'This is a test notification body',
    created_utc: Math.floor(Date.now() / 1000) - 3600,
    new: true,
    permalink: '/test/link',
    subreddit: 'test'
  },
  {
    id: 'test2',
    type: 'username_mention',
    author: 'another_user',
    subject: 'Mention test',
    body: 'You were mentioned in a comment',
    created_utc: Math.floor(Date.now() / 1000) - 7200,
    new: false,
    permalink: '/test/mention'
  }
];

// Tester les fonctions de formatage
function testFormatting() {
  console.log('Test des fonctions de formatage...');
  
  // Fonction escapeHtml simulée
  function escapeHtml(text) {
    return text.replace(/&/g, '&amp;')
               .replace(/</g, '&lt;')
               .replace(/>/g, '&gt;')
               .replace(/"/g, '&quot;')
               .replace(/'/g, '&#39;');
  }
  
  // Fonction formatTime simulée
  function formatTime(timestamp) {
    const date = new Date(timestamp * 1000);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
    return `${Math.floor(diff / 86400000)}d`;
  }
  
  // Tester le formatage
  mockNotifications.forEach((notification, index) => {
    console.log(`\nNotification ${index + 1}:`);
    console.log(`  ID: ${notification.id}`);
    console.log(`  Auteur: ${escapeHtml(notification.author)}`);
    console.log(`  Sujet: ${escapeHtml(notification.subject)}`);
    console.log(`  Corps: ${escapeHtml(notification.body)}`);
    console.log(`  Temps: ${formatTime(notification.created_utc)}`);
    console.log(`  Non lu: ${notification.new}`);
    console.log(`  Type: ${notification.type}`);
  });
}

// Tester la génération HTML
function testHTMLGeneration() {
  console.log('\nTest de génération HTML...');
  
  function escapeHtml(text) {
    return text.replace(/&/g, '&amp;')
               .replace(/</g, '&lt;')
               .replace(/>/g, '&gt;')
               .replace(/"/g, '&quot;')
               .replace(/'/g, '&#39;');
  }
  
  function formatTime(timestamp) {
    const date = new Date(timestamp * 1000);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
    return `${Math.floor(diff / 86400000)}d`;
  }
  
  function formatNotificationBody(body) {
    if (!body) return '';
    
    let cleanBody = escapeHtml(body);
    cleanBody = cleanBody.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
    cleanBody = cleanBody.replace(/\/?u\/([a-zA-Z0-9_-]+)/g, '<a href="/user/$1" target="_blank">u/$1</a>');
    cleanBody = cleanBody.replace(/\/?r\/([a-zA-Z0-9_-]+)/g, '<a href="/r/$1" target="_blank">r/$1</a>');
    cleanBody = cleanBody.replace(/\n/g, '<br>');
    
    return cleanBody;
  }
  
  function getNotificationActions(notification) {
    if (!notification) return '';
    
    let actions = [];
    
    if (notification.type === 'comment_reply' || notification.type === 'username_mention' || notification.type === 'private_message') {
      actions.push(`<a href="#" class="notification-action" data-action="reply" data-id="${notification.id}">reply</a>`);
    }
    
    if (notification.permalink) {
      const contextUrl = notification.context || notification.permalink;
      actions.push(`<a href="${escapeHtml(contextUrl)}" class="notification-action" target="_blank">context</a>`);
    }
    
    if (notification.new) {
      actions.push(`<a href="#" class="notification-action" data-action="mark-read" data-id="${notification.id}">mark as read</a>`);
    } else {
      actions.push(`<a href="#" class="notification-action" data-action="mark-unread" data-id="${notification.id}">mark as unread</a>`);
    }
    
    return actions.join(' • ');
  }
  
  // Générer le HTML pour chaque notification
  const notificationsHtml = mockNotifications.map(notification => {
    const typeClass = notification.type ? notification.type.replace('_', '-') : 'message';
    const contextText = notification.subreddit ? `in <a href="/r/${escapeHtml(notification.subreddit)}" class="notification-subreddit" target="_blank">r/${escapeHtml(notification.subreddit)}</a>` : '';
    const actionsHtml = getNotificationActions(notification);
    
    return `
      <div class="notification-item ${notification.new ? 'unread' : 'read'} ${typeClass}" 
           data-id="${notification.id}" 
           data-type="${notification.type || 'message'}"
           tabindex="0"
           role="button"
           aria-label="Notification from ${escapeHtml(notification.author)}">
        <div class="notification-header">
          <a href="/user/${escapeHtml(notification.author)}" class="notification-author" target="_blank">
            ${escapeHtml(notification.author)}
          </a>
          <span class="notification-time" title="${new Date(notification.created_utc * 1000).toLocaleString()}">
            ${formatTime(notification.created_utc)}
          </span>
        </div>
        <div class="notification-subject">${escapeHtml(notification.subject)}</div>
        ${contextText ? `<div class="notification-context">${contextText}</div>` : ''}
        <div class="notification-body">${formatNotificationBody(notification.body)}</div>
        <div class="notification-actions">
          ${actionsHtml}
        </div>
      </div>
    `;
  }).join('');
  
  console.log('HTML généré:');
  console.log(notificationsHtml);
  
  // Vérifier la structure
  const lines = notificationsHtml.split('\n').filter(line => line.trim());
  console.log(`\nNombre de lignes HTML: ${lines.length}`);
  
  // Compter les éléments
  const divCount = (notificationsHtml.match(/<div/g) || []).length;
  const classCount = (notificationsHtml.match(/class="/g) || []).length;
  
  console.log(`Nombre de divs: ${divCount}`);
  console.log(`Nombre de classes: ${classCount}`);
  
  return notificationsHtml;
}

// Tester la structure CSS
function testCSSStructure() {
  console.log('\nTest de la structure CSS...');
  
  try {
    const cssContent = fs.readFileSync('./styles/mini-inbox.css', 'utf8');
    
    // Vérifier les classes importantes
    const importantClasses = [
      '.notification-item',
      '.notification-header',
      '.notification-subject',
      '.notification-body',
      '.notification-actions',
      '.notification-author',
      '.notification-time'
    ];
    
    let foundClasses = 0;
    importantClasses.forEach(className => {
      if (cssContent.includes(className)) {
        console.log(`✅ Classe trouvée: ${className}`);
        foundClasses++;
      } else {
        console.log(`❌ Classe manquante: ${className}`);
      }
    });
    
    console.log(`\nClasses CSS trouvées: ${foundClasses}/${importantClasses.length}`);
    
    // Vérifier les propriétés de layout
    const layoutProperties = ['display', 'flex', 'grid', 'float', 'position'];
    let layoutIssues = [];
    
    layoutProperties.forEach(prop => {
      const regex = new RegExp(`${prop}\\s*:\\s*([^;]+)`, 'g');
      const matches = cssContent.match(regex);
      if (matches) {
        console.log(`${prop}: ${matches.length} occurrences`);
        matches.forEach(match => {
          if (match.includes('grid') || match.includes('column')) {
            layoutIssues.push(match);
          }
        });
      }
    });
    
    if (layoutIssues.length > 0) {
      console.log('\n⚠️  Propriétés de layout potentiellement problématiques:');
      layoutIssues.forEach(issue => console.log(`  ${issue}`));
    }
    
  } catch (error) {
    console.log('❌ Erreur lors de la lecture du CSS:', error.message);
  }
}

// Exécuter tous les tests
testFormatting();
testHTMLGeneration();
testCSSStructure();

console.log('\nTest de rendu terminé.');