# Document d'Exigences - Extension Reddit Mini Inbox

## Introduction

Cette extension Chrome vise à restaurer la fonctionnalité de mini-fenêtre des notifications Reddit qui a été supprimée en mars 2025. L'extension interceptera les clics sur l'icône de notification (cloche) de Reddit et affichera le contenu des notifications dans une petite fenêtre contextuelle au lieu de rediriger vers la page inbox complète. Cela permettra aux utilisateurs de consulter leurs notifications sans perdre leur position actuelle sur Reddit.

## Exigences

### Exigence 1

**User Story:** En tant qu'utilisateur de Reddit, je veux voir mes notifications dans une mini-fenêtre quand je clique sur l'icône de notification, afin de ne pas perdre ma position actuelle sur la page.

#### Critères d'Acceptation

1. QUAND l'utilisateur clique sur l'icône de notification (cloche) sur Reddit ALORS l'extension DOIT intercepter ce clic
2. QUAND le clic est intercepté ALORS l'extension DOIT empêcher la redirection vers la page inbox complète
3. QUAND l'interception réussit ALORS l'extension DOIT afficher une mini-fenêtre avec le contenu des notifications
4. QUAND la mini-fenêtre s'ouvre ALORS elle DOIT être positionnée près de l'icône de notification

### Exigence 2

**User Story:** En tant qu'utilisateur, je veux que la mini-fenêtre des notifications ressemble à l'ancienne interface Reddit, afin d'avoir une expérience familière.

#### Critères d'Acceptation

1. QUAND la mini-fenêtre s'affiche ALORS elle DOIT avoir un style similaire à l'ancienne mini-inbox de Reddit
2. QUAND les notifications sont chargées ALORS elles DOIVENT être formatées comme dans l'interface Reddit standard
3. QUAND l'utilisateur survole une notification ALORS elle DOIT être mise en surbrillance
4. QUAND l'utilisateur clique sur une notification ALORS elle DOIT s'ouvrir dans un nouvel onglet

### Exigence 3

**User Story:** En tant qu'utilisateur, je veux pouvoir fermer la mini-fenêtre facilement, afin de continuer ma navigation sans encombrement.

#### Critères d'Acceptation

1. QUAND la mini-fenêtre est ouverte ALORS elle DOIT avoir un bouton de fermeture visible
2. QUAND l'utilisateur clique en dehors de la mini-fenêtre ALORS elle DOIT se fermer automatiquement
3. QUAND l'utilisateur appuie sur la touche Échap ALORS la mini-fenêtre DOIT se fermer
4. QUAND l'utilisateur clique à nouveau sur l'icône de notification ALORS la mini-fenêtre DOIT se fermer si elle est déjà ouverte

### Exigence 4

**User Story:** En tant qu'utilisateur, je veux que l'extension fonctionne sur les versions desktop et mobile web de Reddit, afin d'avoir une expérience cohérente.

#### Critères d'Acceptation

1. QUAND l'extension est installée ALORS elle DOIT fonctionner sur old.reddit.com
2. QUAND l'extension est installée ALORS elle DOIT fonctionner sur www.reddit.com (nouveau design)
3. QUAND l'extension est utilisée sur mobile web ALORS la mini-fenêtre DOIT s'adapter à la taille d'écran
4. QUAND l'utilisateur navigue entre différentes pages Reddit ALORS l'extension DOIT continuer à fonctionner

### Exigence 5

**User Story:** En tant qu'utilisateur, je veux que l'extension charge les notifications rapidement, afin de ne pas avoir d'attente frustrante.

#### Critères d'Acceptation

1. QUAND l'utilisateur clique sur l'icône de notification ALORS les notifications DOIVENT commencer à se charger immédiatement
2. QUAND les notifications se chargent ALORS un indicateur de chargement DOIT être affiché
3. SI le chargement échoue ALORS un message d'erreur approprié DOIT être affiché
4. QUAND les notifications sont en cache ALORS elles DOIVENT s'afficher instantanément

### Exigence 6

**User Story:** En tant qu'utilisateur, je veux pouvoir marquer les notifications comme lues depuis la mini-fenêtre, afin de gérer mes notifications efficacement.

#### Critères d'Acceptation

1. QUAND une notification non lue est affichée ALORS elle DOIT être visuellement distincte des notifications lues
2. QUAND l'utilisateur clique sur une notification ALORS elle DOIT être marquée comme lue automatiquement
3. QUAND l'utilisateur marque une notification comme lue ALORS le compteur de notifications DOIT se mettre à jour
4. QUAND toutes les notifications sont lues ALORS l'icône de notification DOIT refléter cet état