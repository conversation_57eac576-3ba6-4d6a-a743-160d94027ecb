# Document de Conception - Extension Reddit Mini Inbox

## Vue d'Ensemble

L'extension Reddit Mini Inbox est une extension Chrome qui restaure la fonctionnalité de mini-fenêtre des notifications Reddit. Elle intercepte les clics sur l'icône de notification, récupère les données via l'API Reddit, et affiche une interface utilisateur personnalisée qui imite l'ancienne mini-inbox de Reddit.

## Architecture

L'extension suit l'architecture standard des extensions Chrome avec trois composants principaux :

```mermaid
graph TB
    A[Content Script] --> B[Background Script]
    B --> C[Reddit API]
    A --> D[Mini Inbox UI]
    D --> A
    B --> A
```

### Composants Principaux

1. **Content Script** : Injecté dans les pages Reddit pour intercepter les interactions utilisateur
2. **Background Script** : Service worker pour gérer les requêtes API et le cache
3. **Mini Inbox UI** : Interface utilisateur overlay pour afficher les notifications
4. **Popup Configuration** : Interface de configuration de l'extension

## Composants et Interfaces

### 1. Content Script (`content.js`)

**Responsabilités :**
- Détecter et intercepter les clics sur l'icône de notification Reddit
- Créer et gérer l'overlay de la mini-inbox
- Communiquer avec le background script pour récupérer les données
- Gérer les événements utilisateur (fermeture, navigation)

**Interfaces :**
```javascript
// Communication avec background script
chrome.runtime.sendMessage({
  action: 'getNotifications',
  force: boolean
})

// Gestion des événements DOM
document.addEventListener('click', handleNotificationClick)
document.addEventListener('keydown', handleKeyPress)
```

### 2. Background Script (`background.js`)

**Responsabilités :**
- Gérer l'authentification Reddit OAuth2
- Effectuer les requêtes API Reddit
- Mettre en cache les notifications
- Gérer les permissions et la configuration

**Interfaces :**
```javascript
// API Reddit endpoints
GET /message/inbox.json
GET /api/v1/me

// Cache management
chrome.storage.local.set/get
```

### 3. Mini Inbox UI (`mini-inbox.js` + `mini-inbox.css`)

**Responsabilités :**
- Afficher les notifications dans un overlay stylisé
- Gérer les interactions utilisateur (clic, hover, scroll)
- Maintenir l'état visuel (lu/non-lu)
- Responsive design pour mobile et desktop

**Structure DOM :**
```html
<div id="reddit-mini-inbox-overlay">
  <div class="mini-inbox-container">
    <div class="mini-inbox-header">
      <span>Notifications</span>
      <button class="close-btn">×</button>
    </div>
    <div class="mini-inbox-content">
      <div class="notification-item">...</div>
    </div>
  </div>
</div>
```

### 4. Configuration Popup (`popup.html` + `popup.js`)

**Responsabilités :**
- Interface de configuration de l'extension
- Gestion de l'authentification utilisateur
- Options d'affichage et de comportement

## Modèles de Données

### Notification Object
```javascript
{
  id: string,
  type: 'comment' | 'post_reply' | 'username_mention' | 'private_message',
  author: string,
  subject: string,
  body: string,
  created_utc: number,
  new: boolean,
  permalink: string,
  context?: string
}
```

### User Settings
```javascript
{
  authenticated: boolean,
  username: string,
  refreshToken: string,
  showUnreadOnly: boolean,
  maxNotifications: number,
  position: 'top-right' | 'top-left',
  theme: 'light' | 'dark' | 'auto'
}
```

### Cache Structure
```javascript
{
  notifications: Notification[],
  lastFetch: number,
  unreadCount: number,
  cacheExpiry: number
}
```

## Gestion des Erreurs

### Authentification
- **Erreur OAuth** : Rediriger vers la page de configuration
- **Token expiré** : Rafraîchir automatiquement le token
- **Permissions insuffisantes** : Afficher un message d'erreur explicite

### API Reddit
- **Rate limiting** : Implémenter un backoff exponentiel
- **Erreurs réseau** : Afficher les données en cache avec un indicateur
- **Erreurs 403/401** : Déclencher une ré-authentification

### Interface Utilisateur
- **Échec de chargement** : Afficher un message d'erreur avec option de retry
- **Contenu vide** : Afficher un message "Aucune notification"
- **Erreurs de rendu** : Fallback vers l'interface Reddit standard

## Stratégie de Test

### Tests Unitaires
- **Content Script** : Simulation des événements DOM et des réponses API
- **Background Script** : Tests des fonctions de cache et d'API
- **UI Components** : Tests de rendu et d'interaction

### Tests d'Intégration
- **Flux complet** : De l'interception du clic à l'affichage des notifications
- **Authentification** : Processus OAuth complet
- **Gestion d'erreur** : Scénarios de panne réseau et API

### Tests de Compatibilité
- **Versions Reddit** : old.reddit.com et www.reddit.com
- **Navigateurs** : Chrome, Edge, Firefox (si extension WebExtension)
- **Responsive** : Desktop et mobile web

### Tests de Performance
- **Temps de chargement** : < 500ms pour l'affichage initial
- **Mémoire** : Surveillance des fuites mémoire
- **Cache** : Efficacité du système de mise en cache

## Sécurité et Permissions

### Permissions Requises
```json
{
  "permissions": [
    "storage",
    "activeTab",
    "https://www.reddit.com/*",
    "https://old.reddit.com/*",
    "https://oauth.reddit.com/*"
  ]
}
```

### Sécurité
- **Tokens** : Stockage sécurisé avec chrome.storage.local
- **CSP** : Content Security Policy stricte
- **Sanitization** : Nettoyage du contenu HTML des notifications
- **HTTPS** : Toutes les communications via HTTPS uniquement

## Déploiement et Distribution

### Chrome Web Store
- Manifest V3 compliance
- Description multilingue (français/anglais)
- Screenshots et documentation
- Politique de confidentialité

### Mise à jour
- Auto-update via Chrome Web Store
- Migration des données utilisateur entre versions
- Backward compatibility pour les anciennes configurations