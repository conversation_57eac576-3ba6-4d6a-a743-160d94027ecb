/* Styles pour le popup de configuration */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 350px;
  min-height: 400px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #ffffff;
  color: #1a1a1b;
}

.popup-container {
  padding: 0;
}

.header {
  background-color: #ff4500;
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 32px;
  height: 32px;
  border-radius: 4px;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.content {
  padding: 16px;
}

.section {
  margin-bottom: 24px;
}

.section:last-child {
  margin-bottom: 0;
}

.section h2 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #1a1a1b;
}

.description {
  font-size: 14px;
  color: #7c7c83;
  line-height: 1.4;
  margin-bottom: 12px;
}

.primary-btn {
  background-color: #0079d3;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;
}

.primary-btn:hover {
  background-color: #0066b3;
}

.primary-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.secondary-btn {
  background-color: #edeff1;
  color: #1a1a1b;
  border: 1px solid #ccc;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}

.secondary-btn:hover {
  background-color: #e0e2e4;
}

.danger-btn {
  background-color: #d93025;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}

.danger-btn:hover {
  background-color: #b71c1c;
}

.btn-icon {
  font-size: 14px;
}

.status {
  margin-top: 8px;
  font-size: 12px;
  padding: 8px;
  border-radius: 4px;
  display: none;
}

.status.success {
  background-color: #e8f5e8;
  color: #2d5a2d;
  border: 1px solid #c3e6c3;
}

.status.error {
  background-color: #ffeaea;
  color: #d93025;
  border: 1px solid #ffcdd2;
}

.status.info {
  background-color: #e3f2fd;
  color: #1565c0;
  border: 1px solid #bbdefb;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #1a1a1b;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.setting-item select {
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 14px;
  color: #1a1a1b;
  cursor: pointer;
  margin-left: auto;
}

.setting-item select:focus {
  outline: none;
  border-color: #0079d3;
  box-shadow: 0 0 0 2px rgba(0, 121, 211, 0.2);
}

.version {
  font-size: 12px;
  color: #878a8c;
  margin-top: 8px;
}

/* Animation de chargement */
.loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0079d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}/* Sty
les pour l'authentification */
.auth-state {
  margin-top: 12px;
}

.auth-info {
  margin-bottom: 16px;
}

.auth-note {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  font-size: 13px;
  line-height: 1.4;
  color: #495057;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #0079d3;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.avatar-placeholder {
  font-size: 20px;
}

.user-details {
  flex: 1;
}

.username {
  font-weight: 600;
  font-size: 14px;
  color: #1a1a1b;
  margin-bottom: 2px;
}

.auth-time {
  font-size: 12px;
  color: #7c7c83;
}

.auth-actions {
  display: flex;
  gap: 8px;
}

.auth-actions .secondary-btn,
.auth-actions .danger-btn {
  flex: 1;
  font-size: 13px;
  padding: 8px 12px;
}

.loading-container {
  text-align: center;
  padding: 20px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0079d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1b;
  margin-bottom: 4px;
}

.loading-subtext {
  font-size: 12px;
  color: #7c7c83;
}

/* Amélioration du bouton principal avec icône */
.primary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.primary-btn .btn-icon {
  font-size: 16px;
}

/* États de transition */
.auth-state {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.auth-state.fade-out {
  opacity: 0;
  transform: translateY(-10px);
}

.auth-state.fade-in {
  opacity: 1;
  transform: translateY(0);
}