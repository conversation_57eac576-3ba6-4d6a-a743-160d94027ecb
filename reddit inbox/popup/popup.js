// Script pour le popup de configuration

document.addEventListener('DOMContentLoaded', async () => {
  console.log('Popup loaded');
  
  // Éléments du DOM
  const authBtn = document.getElementById('auth-btn');
  const authStatus = document.getElementById('auth-status');
  const authSection = document.getElementById('auth-section');
  const settingsSection = document.getElementById('settings-section');
  const saveSettingsBtn = document.getElementById('save-settings');
  
  // États d'authentification
  const authUnauthenticated = document.getElementById('auth-unauthenticated');
  const authAuthenticated = document.getElementById('auth-authenticated');
  const authLoading = document.getElementById('auth-loading');
  const usernameDisplay = document.getElementById('username-display');
  const authTimeDisplay = document.getElementById('auth-time');
  const refreshAuthBtn = document.getElementById('refresh-auth');
  const logoutBtn = document.getElementById('logout-btn');
  
  // Paramètres
  const showUnreadOnlyCheckbox = document.getElementById('show-unread-only');
  const maxNotificationsSelect = document.getElementById('max-notifications');
  const themeSelect = document.getElementById('theme');
  
  // Charger l'état d'authentification et les paramètres
  await loadAuthState();
  await loadSettings();
  
  // Event listeners
  authBtn.addEventListener('click', handleAuth);
  refreshAuthBtn.addEventListener('click', handleRefreshAuth);
  logoutBtn.addEventListener('click', handleLogout);
  saveSettingsBtn.addEventListener('click', saveSettings);
  
  // Charger l'état d'authentification
  async function loadAuthState() {
    try {
      const result = await chrome.storage.local.get(['authenticated', 'username', 'authTime']);
      
      if (result.authenticated) {
        showAuthenticatedState(result.username, result.authTime);
      } else {
        showUnauthenticatedState();
      }
    } catch (error) {
      console.error('Error loading auth state:', error);
      showUnauthenticatedState();
    }
  }
  
  // Charger les paramètres
  async function loadSettings() {
    try {
      const result = await chrome.storage.local.get([
        'showUnreadOnly',
        'maxNotifications',
        'theme'
      ]);
      
      showUnreadOnlyCheckbox.checked = result.showUnreadOnly || false;
      maxNotificationsSelect.value = result.maxNotifications || '25';
      themeSelect.value = result.theme || 'auto';
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }
  
  // Afficher l'état authentifié
  function showAuthenticatedState(username, authTime) {
    hideAllAuthStates();
    authAuthenticated.style.display = 'block';
    usernameDisplay.textContent = `u/${username}`;
    authTimeDisplay.textContent = formatAuthTime(authTime);
    settingsSection.style.display = 'block';
  }
  
  // Afficher l'état non authentifié
  function showUnauthenticatedState() {
    hideAllAuthStates();
    authUnauthenticated.style.display = 'block';
    settingsSection.style.display = 'none';
  }
  
  // Afficher l'état de chargement
  function showLoadingState() {
    hideAllAuthStates();
    authLoading.style.display = 'block';
  }
  
  // Masquer tous les états d'authentification
  function hideAllAuthStates() {
    authUnauthenticated.style.display = 'none';
    authAuthenticated.style.display = 'none';
    authLoading.style.display = 'none';
  }
  
  // Gestionnaire d'authentification
  async function handleAuth() {
    try {
      showLoadingState();
      
      // Lancer l'authentification OAuth2 via le background script
      const response = await chrome.runtime.sendMessage({
        action: 'authenticate'
      });
      
      if (response.success) {
        const authTime = Date.now();
        showAuthenticatedState(response.username, authTime);
        showStatus('Connexion réussie !', 'success');
        setTimeout(hideStatus, 3000);
      } else {
        showUnauthenticatedState();
        showStatus(`Erreur d'authentification: ${response.error}`, 'error');
      }
    } catch (error) {
      console.error('Auth error:', error);
      showUnauthenticatedState();
      showStatus('Erreur d\'authentification', 'error');
    }
  }
  
  // Gestionnaire de rafraîchissement d'authentification
  async function handleRefreshAuth() {
    try {
      refreshAuthBtn.disabled = true;
      refreshAuthBtn.innerHTML = '<span class="spinner"></span> Actualisation...';
      
      // Rafraîchir le token via le background script
      const response = await chrome.runtime.sendMessage({
        action: 'refreshToken'
      });
      
      if (response.success) {
        const result = await chrome.storage.local.get(['username']);
        const authTime = Date.now();
        
        await chrome.storage.local.set({
          authTime: authTime
        });
        
        showAuthenticatedState(result.username, authTime);
        showStatus('Authentification actualisée', 'success');
        setTimeout(hideStatus, 2000);
      } else {
        showStatus(`Erreur lors de l'actualisation: ${response.error}`, 'error');
      }
    } catch (error) {
      console.error('Refresh error:', error);
      showStatus('Erreur lors de l\'actualisation', 'error');
    } finally {
      refreshAuthBtn.disabled = false;
      refreshAuthBtn.innerHTML = '<span class="btn-icon">🔄</span> Actualiser';
    }
  }
  
  // Gestionnaire de déconnexion
  async function handleLogout() {
    try {
      logoutBtn.disabled = true;
      logoutBtn.innerHTML = '<span class="spinner"></span> Déconnexion...';
      
      // Effacer toutes les données d'authentification
      await chrome.storage.local.remove([
        'authenticated',
        'username',
        'authTime',
        'accessToken',
        'refreshToken'
      ]);
      
      showUnauthenticatedState();
      showStatus('Déconnecté avec succès', 'info');
      setTimeout(hideStatus, 2000);
    } catch (error) {
      console.error('Logout error:', error);
      showStatus('Erreur lors de la déconnexion', 'error');
    } finally {
      logoutBtn.disabled = false;
      logoutBtn.innerHTML = '<span class="btn-icon">🚪</span> Se déconnecter';
    }
  }
  
  // Sauvegarder les paramètres
  async function saveSettings() {
    try {
      await chrome.storage.local.set({
        showUnreadOnly: showUnreadOnlyCheckbox.checked,
        maxNotifications: maxNotificationsSelect.value,
        theme: themeSelect.value
      });
      
      showStatus('Paramètres sauvegardés', 'success');
      
      // Masquer le message après 2 secondes
      setTimeout(() => {
        hideStatus();
      }, 2000);
    } catch (error) {
      console.error('Error saving settings:', error);
      showStatus('Erreur lors de la sauvegarde', 'error');
    }
  }
  
  // Afficher un message de statut
  function showStatus(message, type) {
    authStatus.textContent = message;
    authStatus.className = `status ${type}`;
    authStatus.style.display = 'block';
  }
  
  // Masquer le message de statut
  function hideStatus() {
    authStatus.style.display = 'none';
  }
  
  // Fonction utilitaire pour formater le temps d'authentification
  function formatAuthTime(timestamp) {
    if (!timestamp) return 'Connecté récemment';
    
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) return 'Connecté à l\'instant';
    if (minutes < 60) return `Connecté il y a ${minutes} min`;
    if (hours < 24) return `Connecté il y a ${hours}h`;
    if (days < 7) return `Connecté il y a ${days} jour${days > 1 ? 's' : ''}`;
    
    return 'Connecté il y a longtemps';
  }
});