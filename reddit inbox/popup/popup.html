<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reddit Mini Inbox</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <div class="header">
      <img src="../assets/icon48.png" alt="Reddit Mini Inbox" class="logo">
      <h1>Reddit Mini Inbox</h1>
    </div>
    
    <div class="content">
      <div id="auth-section" class="section">
        <h2>Authentification</h2>
        <p class="description">
          Connectez-vous à Reddit pour accéder à vos notifications dans la mini-inbox.
        </p>
        
        <div id="auth-unauthenticated" class="auth-state">
          <div class="auth-info">
            <p class="auth-note">
              <strong>Note :</strong> Cette extension nécessite l'accès à votre compte Reddit 
              pour récupérer vos notifications. Vos données restent privées et ne sont 
              jamais partagées.
            </p>
          </div>
          <button id="auth-btn" class="primary-btn">
            <span class="btn-icon">🔐</span>
            Se connecter à Reddit
          </button>
        </div>
        
        <div id="auth-authenticated" class="auth-state" style="display: none;">
          <div class="user-info">
            <div class="user-avatar">
              <span class="avatar-placeholder">👤</span>
            </div>
            <div class="user-details">
              <div class="username" id="username-display">Chargement...</div>
              <div class="auth-time" id="auth-time">Connecté récemment</div>
            </div>
          </div>
          <div class="auth-actions">
            <button id="refresh-auth" class="secondary-btn">
              <span class="btn-icon">🔄</span>
              Actualiser
            </button>
            <button id="logout-btn" class="danger-btn">
              <span class="btn-icon">🚪</span>
              Se déconnecter
            </button>
          </div>
        </div>
        
        <div id="auth-loading" class="auth-state" style="display: none;">
          <div class="loading-container">
            <div class="spinner"></div>
            <p class="loading-text">Connexion en cours...</p>
            <p class="loading-subtext">Vous allez être redirigé vers Reddit</p>
          </div>
        </div>
        
        <div id="auth-status" class="status"></div>
      </div>
      
      <div id="settings-section" class="section" style="display: none;">
        <h2>Paramètres</h2>
        
        <div class="setting-item">
          <label for="show-unread-only">
            <input type="checkbox" id="show-unread-only">
            Afficher uniquement les notifications non lues
          </label>
        </div>
        
        <div class="setting-item">
          <label for="max-notifications">
            Nombre maximum de notifications:
            <select id="max-notifications">
              <option value="10">10</option>
              <option value="25" selected>25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </label>
        </div>
        
        <div class="setting-item">
          <label for="theme">
            Thème:
            <select id="theme">
              <option value="auto" selected>Automatique</option>
              <option value="light">Clair</option>
              <option value="dark">Sombre</option>
            </select>
          </label>
        </div>
        
        <button id="save-settings" class="secondary-btn">
          Sauvegarder
        </button>
      </div>
      
      <div id="info-section" class="section">
        <h2>À propos</h2>
        <p class="description">
          Cette extension restaure la fonctionnalité de mini-fenêtre des notifications Reddit 
          qui a été supprimée en mars 2025.
        </p>
        <p class="version">Version 1.0.0</p>
      </div>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>