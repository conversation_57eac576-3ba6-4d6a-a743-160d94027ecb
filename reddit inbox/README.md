# 📬 Reddit Mini Inbox Extension

Une extension Chrome qui restaure la fonctionnalité de mini-fenêtre des notifications Reddit supprimée en mars 2025.

## ✨ Fonctionnalités

- 🎯 **Interception des clics** sur l'icône de notification Reddit
- 📬 **Mini-inbox élégante** avec design fidèle à l'ancienne interface Reddit
- 🔐 **Authentification OAuth2** sécurisée avec Reddit
- 📱 **Design responsive** qui s'adapte à toutes les tailles d'écran
- 🌙 **Thème automatique** (clair/sombre) selon les préférences Reddit
- ⚡ **Cache intelligent** pour des performances optimales
- 🔔 **Badge de notifications** avec compteur de messages non lus

## 🚀 Installation

### Mode Développeur (Recommandé pour les tests)

1. **Téléchargez** ou clonez ce repository
2. **Ouvrez Chrome** et allez sur `chrome://extensions/`
3. **Activez** le "Mode développeur" (coin supérieur droit)
4. **Cliquez** sur "Charger l'extension non empaquetée"
5. **Sélectionnez** le dossier de l'extension

### Configuration OAuth2 Reddit (Optionnel)

Pour utiliser vos vraies notifications Reddit :

1. **Suivez** le guide dans `REDDIT_OAUTH_SETUP.md`
2. **Configurez** votre application Reddit
3. **Mettez à jour** les credentials dans `scripts/background.js`

> **Note** : Sans configuration OAuth2, l'extension fonctionne avec des notifications de test.

## 🎮 Utilisation

### Première utilisation

1. **Allez sur Reddit** (www.reddit.com ou old.reddit.com)
2. **Cliquez** sur l'icône de notification Reddit (🔔)
3. **La mini-inbox s'ouvre** avec vos notifications

### Authentification (Optionnel)

1. **Cliquez** sur l'icône de l'extension dans la barre d'outils
2. **Cliquez** sur "Se connecter à Reddit"
3. **Autorisez** l'application sur Reddit
4. **Profitez** de vos vraies notifications !

### Fonctionnalités

- ✅ **Cliquer sur une notification** → Marque comme lue
- ✅ **Bouton ×** → Ferme la mini-inbox
- ✅ **Touche Échap** → Ferme la mini-inbox
- ✅ **Clic en dehors** → Ferme la mini-inbox
- ✅ **Responsive** → Fonctionne sur mobile et desktop

## 🛠️ Développement

### Structure du projet

```
reddit-mini-inbox-extension/
├── manifest.json              # Configuration de l'extension
├── scripts/
│   ├── background.js         # Service worker (API, auth, cache)
│   └── content-clean.js      # Script injecté dans Reddit
├── popup/
│   ├── popup.html           # Interface de configuration
│   ├── popup.js             # Logique du popup
│   └── popup.css            # Styles du popup
├── styles/
│   └── mini-inbox.css       # Styles de la mini-inbox
├── assets/                  # Icônes et ressources
└── tests/                   # Tests automatisés
```

### Technologies utilisées

- **Manifest V3** - Dernière version des extensions Chrome
- **OAuth2** - Authentification sécurisée Reddit
- **Chrome Storage API** - Cache et persistance
- **Chrome Identity API** - Gestion OAuth2
- **Vanilla JavaScript** - Pas de dépendances externes
- **CSS3** - Animations et responsive design

## 🧪 Tests

L'extension inclut des tests automatisés :

```bash
# Tests unitaires
npm test

# Tests d'intégration
npm run test:integration

# Tests end-to-end
npm run test:e2e
```

## 📋 Compatibilité

- ✅ **Chrome** 88+ (Manifest V3)
- ✅ **Edge** 88+ (Chromium)
- ✅ **www.reddit.com** (nouveau design)
- ✅ **old.reddit.com** (ancien design)
- ✅ **Mobile** et **Desktop**

## 🔒 Sécurité et Confidentialité

- 🔐 **Authentification sécurisée** via OAuth2 Reddit officiel
- 🚫 **Aucune donnée** n'est envoyée à des serveurs tiers
- 💾 **Stockage local** uniquement (Chrome Storage API)
- 🔑 **Permissions minimales** (lecture seule des messages)
- 🧹 **Nettoyage automatique** des tokens expirés

## 🐛 Dépannage

### L'extension ne se charge pas
- Vérifiez que le mode développeur est activé
- Rechargez l'extension depuis `chrome://extensions/`

### Les notifications ne s'affichent pas
- Vérifiez votre connexion internet
- Essayez de vous reconnecter à Reddit
- Consultez la console Chrome (F12) pour les erreurs

### L'authentification échoue
- Vérifiez votre configuration OAuth2 dans `REDDIT_OAUTH_SETUP.md`
- Assurez-vous que l'URL de redirection est correcte
- Vérifiez que votre application Reddit est active

## 📝 Changelog

### v1.0.0 (2025-01-19)
- 🎉 Version initiale
- ✅ Interception des clics sur l'icône de notification
- ✅ Mini-inbox avec design Reddit authentique
- ✅ Authentification OAuth2 Reddit
- ✅ Cache intelligent des notifications
- ✅ Support thème clair/sombre
- ✅ Design responsive
- ✅ Tests automatisés complets

## 🤝 Contribution

Les contributions sont les bienvenues ! Voici comment contribuer :

1. **Fork** le projet
2. **Créez** une branche pour votre fonctionnalité
3. **Committez** vos changements
4. **Poussez** vers la branche
5. **Ouvrez** une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🙏 Remerciements

- **Reddit** pour l'API et l'inspiration du design original
- **La communauté Chrome Extensions** pour la documentation
- **Tous les testeurs** qui ont aidé à améliorer l'extension

## 📞 Support

- 🐛 **Bugs** : Ouvrez une issue sur GitHub
- 💡 **Suggestions** : Discussions GitHub
- 📧 **Contact** : [Votre email]

---

**Fait avec ❤️ pour restaurer la mini-inbox Reddit que nous aimons tous !**