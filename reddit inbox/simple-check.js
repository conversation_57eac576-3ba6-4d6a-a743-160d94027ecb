const fs = require('fs');

try {
  const content = fs.readFileSync('scripts/content-new.js', 'utf8');
  
  // Test de compilation
  new Function(content);
  console.log('✅ Compilation réussie');
  
  // Comptage simple des parenthèses
  let open = 0;
  let close = 0;
  
  for (let char of content) {
    if (char === '(') open++;
    if (char === ')') close++;
  }
  
  console.log(`Parenthèses ouvrantes: ${open}`);
  console.log(`Parenthèses fermantes: ${close}`);
  console.log(`Différence: ${open - close}`);
  
  if (open === close) {
    console.log('✅ Parenthèses équilibrées');
  } else {
    console.log('❌ Parenthèses déséquilibrées');
  }
  
} catch (error) {
  console.error('❌ Erreur:', error.message);
}