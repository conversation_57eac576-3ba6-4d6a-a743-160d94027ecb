/* Styles pour la mini-inbox Reddit */

#reddit-mini-inbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10000;
  pointer-events: auto;
  opacity: 0;
  transition: opacity 0.2s ease-out;
}

#reddit-mini-inbox-overlay.show {
  opacity: 1;
}

#reddit-mini-inbox-overlay.positioned {
  background-color: transparent;
  pointer-events: none;
}

.mini-inbox-container {
  background-color: #ffffff;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  width: 400px !important;
  min-width: 400px !important;
  max-width: 90vw !important;
  max-height: 500px !important;
  overflow: hidden !important;
  font-family: Verdana, Arial, sans-serif !important;
  font-size: 11px !important;
  position: absolute !important;
  pointer-events: auto !important;
  transform: scale(0.95) !important;
  transition: transform 0.15s ease-out, opacity 0.15s ease-out !important;
  z-index: 999999 !important;
  display: flex !important;
  flex-direction: column !important;
}

.mini-inbox-container.show {
  transform: scale(1);
  opacity: 1;
}

.mini-inbox-header {
  background: linear-gradient(to bottom, #ff6600, #ff4500);
  color: white;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 12px;
  border-bottom: 1px solid #cc3300;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.mini-inbox-title {
  display: flex;
  align-items: center;
  gap: 6px;
}

.mini-inbox-icon {
  width: 16px;
  height: 16px;
  opacity: 0.9;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.mini-inbox-content {
  max-height: 400px !important;
  overflow-y: auto !important;
  background-color: #ffffff;
  border-top: 1px solid #e5e5e5 !important;
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}

.mini-inbox-stats {
  background-color: #f5f5f5;
  padding: 6px 12px;
  border-bottom: 1px solid #e5e5e5;
  font-size: 11px;
  color: #666;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unread-count {
  font-weight: bold;
  color: #ff4500;
}

.loading, .error, .no-notifications {
  padding: 30px 20px;
  text-align: center;
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ff4500;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  color: #d93025;
  background-color: #ffeaea;
  border: 1px solid #ffcdd2;
  border-radius: 3px;
  margin: 10px;
  padding: 15px;
}

.no-notifications {
  color: #999;
  font-style: italic;
}

.notification-item {
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
  padding: 12px !important;
  border-bottom: 1px solid #e5e5e5 !important;
  background: white;
  box-sizing: border-box !important;
  cursor: pointer !important;
  transition: background-color 0.15s ease !important;
  position: relative !important;
  font-size: 11px !important;
}

.notification-item:hover {
  background-color: #f0f0f0;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background-color: #fff8dc;
  border-left: 3px solid #ff4500;
  font-weight: bold;
}

.notification-item.read {
  background-color: #ffffff;
  opacity: 0.8;
}

/* Types de notifications */
.notification-item.comment-reply::before {
  content: "💬";
  position: absolute;
  left: 4px;
  top: 8px;
  font-size: 10px;
}

.notification-item.username-mention::before {
  content: "@";
  position: absolute;
  left: 6px;
  top: 8px;
  font-size: 10px;
  font-weight: bold;
  color: #ff4500;
}

.notification-item.private-message::before {
  content: "✉️";
  position: absolute;
  left: 4px;
  top: 8px;
  font-size: 10px;
}

.notification-item.post-reply::before {
  content: "📝";
  position: absolute;
  left: 4px;
  top: 8px;
  font-size: 10px;
}

.notification-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 4px !important;
  width: 100% !important;
  flex-wrap: nowrap !important;
}

.notification-author {
  font-weight: bold !important;
  color: #0079d3 !important;
  text-decoration: none !important;
  font-size: 11px;
}

.notification-author:hover {
  text-decoration: underline;
}

.notification-time {
  color: #999 !important;
  font-size: 10px !important;
  white-space: nowrap;
}

.notification-subject {
  font-weight: bold !important;
  margin-bottom: 4px !important;
  color: #1a1a1b !important;
  line-height: 1.3 !important;
  font-size: 11px;
}

.notification-context {
  color: #666;
  font-size: 10px;
  margin-bottom: 3px;
  padding-left: 16px;
  font-style: italic;
}

.notification-body {
  color: #666 !important;
  line-height: 1.4 !important;
  margin-bottom: 8px !important;
  word-wrap: break-word !important;
  font-size: 11px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-actions {
  display: flex !important;
  gap: 8px !important;
  margin-top: 8px !important;
  flex-wrap: wrap !important;
  align-items: center !important;
}

.notification-action {
  display: inline-block !important;
  padding: 2px 6px !important;
  font-size: 11px !important;
  text-decoration: none !important;
  color: #0079d3 !important;
  border-radius: 2px !important;
  white-space: nowrap !important;
  cursor: pointer !important;
  border: none !important;
  background: none !important;
  transition: background-color 0.15s ease !important;
}

.notification-action:hover {
  background: #f0f8ff !important;
  text-decoration: underline !important;
}

.notification-subreddit {
  color: #369;
  font-size: 10px;
  font-weight: bold;
  text-decoration: none;
}

.notification-subreddit:hover {
  text-decoration: underline;
}

/* Responsive pour mobile */
@media (max-width: 480px) {
  #reddit-mini-inbox-overlay {
    padding-top: 20px;
  }
  
  .mini-inbox-container {
    width: 95vw;
    max-height: 85vh;
  }
  
  .mini-inbox-content {
    max-height: 60vh;
  }
}

/* Animation d'entrée */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mini-inbox-container {
  animation: slideIn 0.2s ease-out;
}

/* Scrollbar personnalisée */
.mini-inbox-content::-webkit-scrollbar {
  width: 6px;
}

.mini-inbox-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.mini-inbox-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.mini-inbox-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}/* Th
ème sombre */
.mini-inbox-container.dark-theme {
  background-color: #1a1a1b !important;
  border-color: #343536;
  color: #d7dadc;
}

.mini-inbox-container.dark-theme .mini-inbox-header {
  background: linear-gradient(to bottom, #ff4500, #cc3300);
  border-bottom-color: #aa2200;
}

.mini-inbox-container.dark-theme .mini-inbox-content {
  background-color: #1a1a1b !important;
  border-top-color: #343536;
}

.mini-inbox-container.dark-theme .mini-inbox-stats {
  background-color: #272729;
  border-bottom-color: #343536;
  color: #818384;
}

.mini-inbox-container.dark-theme .notification-item {
  border-bottom-color: #343536;
  background: #1a1a1b !important;
}

.mini-inbox-container.dark-theme .notification-item:hover {
  background-color: #272729 !important;
}

.mini-inbox-container.dark-theme .notification-item.unread {
  background-color: #2d1b00;
  border-left-color: #ff4500;
}

.mini-inbox-container.dark-theme .notification-author {
  color: #4fbcff;
}

.mini-inbox-container.dark-theme .notification-subject {
  color: #d7dadc;
}

.mini-inbox-container.dark-theme .notification-body {
  color: #818384;
}

.mini-inbox-container.dark-theme .notification-time {
  color: #818384;
}

.mini-inbox-container.dark-theme .notification-context {
  color: #818384;
}

.mini-inbox-container.dark-theme .notification-action {
  color: #4fbcff;
}

.mini-inbox-container.dark-theme .notification-subreddit {
  color: #4fbcff;
}

.mini-inbox-container.dark-theme .loading,
.mini-inbox-container.dark-theme .no-notifications {
  color: #818384;
}

.mini-inbox-container.dark-theme .error {
  background-color: #3a1f1f;
  border-color: #5a2d2d;
  color: #ff6b6b;
}

/* Animations d'ouverture et fermeture */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fadeOutScale {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
}

.mini-inbox-container.opening {
  animation: fadeInScale 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.mini-inbox-container.closing {
  animation: fadeOutScale 0.15s cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
}

/* Responsive amélioré */
@media (max-width: 768px) {
  .mini-inbox-container {
    width: 95vw;
    max-width: 350px;
    max-height: 70vh;
  }
  
  .mini-inbox-content {
    max-height: 50vh;
  }
  
  .notification-item {
    padding: 10px 12px;
  }
  
  .notification-body {
    -webkit-line-clamp: 2;
  }
}

@media (max-width: 480px) {
  .mini-inbox-container {
    width: 98vw;
    max-width: none;
    max-height: 80vh;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .mini-inbox-header {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .mini-inbox-content {
    max-height: 65vh;
  }
  
  .notification-item {
    padding: 12px;
    font-size: 12px;
  }
  
  .notification-header {
    padding-left: 18px;
  }
  
  .notification-subject,
  .notification-context,
  .notification-body,
  .notification-actions {
    padding-left: 18px;
  }
  
  .notification-body {
    font-size: 12px;
    -webkit-line-clamp: 2;
  }
}

/* États de chargement améliorés */
.mini-inbox-content.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-text {
  color: #666;
  font-size: 12px;
}

/* Barre de progression pour le chargement */
.loading-progress {
  width: 200px;
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e5e5;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4500, #ff6600);
  width: 0%;
  transition: width 2s ease-out;
  border-radius: 2px;
}

.mini-inbox-container.dark-theme .progress-bar {
  background-color: #343536;
}

/* Indicateur de rafraîchissement dans le header */
.refresh-indicator {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 8px;
  display: none;
}

/* États d'erreur améliorés */
.mini-inbox-content.error-state .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 30px 20px;
  text-align: center;
}

.error-icon {
  font-size: 32px;
  opacity: 0.7;
}

.error-title {
  font-size: 14px;
  font-weight: bold;
  color: #d93025;
  margin-bottom: 4px;
}

.error-details {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16px;
}

.error-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.retry-button,
.close-error-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button {
  background: #ff4500;
  color: white;
}

.retry-button:hover {
  background: #cc3300;
  transform: translateY(-1px);
}

.close-error-button {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.close-error-button:hover {
  background: #e5e5e5;
  color: #333;
}

.error-timestamp {
  font-size: 10px;
  color: #999;
  margin-top: 8px;
  font-style: italic;
}

/* Thème sombre pour les erreurs */
.mini-inbox-container.dark-theme .error-title {
  color: #ff6b6b;
}

.mini-inbox-container.dark-theme .error-details {
  color: #818384;
}

.mini-inbox-container.dark-theme .close-error-button {
  background: #272729;
  color: #818384;
  border-color: #343536;
}

.mini-inbox-container.dark-theme .close-error-button:hover {
  background: #343536;
  color: #d7dadc;
}

.mini-inbox-container.dark-theme .error-timestamp {
  color: #666;
}

/* Message "Aucune notification" amélioré */
.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 40px 20px;
  text-align: center;
}

.no-notifications-icon {
  font-size: 48px;
  opacity: 0.6;
}

.no-notifications-title {
  font-size: 16px;
  font-weight: bold;
  color: #666;
  margin-bottom: 4px;
}

.no-notifications-subtitle {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  margin-bottom: 16px;
  max-width: 280px;
}

.refresh-notifications-button {
  padding: 10px 20px;
  background: #ff4500;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-notifications-button:hover {
  background: #cc3300;
  transform: translateY(-1px);
}

/* Thème sombre pour "Aucune notification" */
.mini-inbox-container.dark-theme .no-notifications-title {
  color: #818384;
}

.mini-inbox-container.dark-theme .no-notifications-subtitle {
  color: #666;
}

/* Badge de notification sur l'icône Reddit */
.reddit-mini-inbox-badge {
  position: absolute !important;
  top: -8px !important;
  right: -8px !important;
  background: #ff4500 !important;
  color: white !important;
  border-radius: 50% !important;
  width: 16px !important;
  height: 16px !important;
  font-size: 10px !important;
  font-weight: bold !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 10002 !important;
  border: 2px solid white !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3) !important;
  font-family: Arial, sans-serif !important;
  line-height: 1 !important;
}

/* Animation pour le badge */
@keyframes badgePulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.reddit-mini-inbox-badge.new {
  animation: badgePulse 0.6s ease-out;
}

/* États de marquage comme lu */
.notification-item.marking-read {
  opacity: 0.6;
  transition: opacity 0.3s ease;
  position: relative;
}

.notification-item.marking-read::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid #ff4500;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 10001;
}

/* Indicateurs visuels */
.notification-item.new-notification {
  animation: highlightNew 2s ease-out;
}

@keyframes highlightNew {
  0% {
    background-color: #ffffcc;
    transform: translateX(-5px);
  }
  100% {
    background-color: inherit;
    transform: translateX(0);
  }
}

.notification-item.marking-read {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

/* Amélioration de l'accessibilité */
.mini-inbox-container:focus-within {
  outline: 2px solid #0079d3;
  outline-offset: 2px;
}

.close-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.notification-item:focus {
  outline: 2px solid #0079d3;
  outline-offset: -2px;
}

/* Scrollbar pour thème sombre */
.mini-inbox-container.dark-theme .mini-inbox-content::-webkit-scrollbar-track {
  background: #272729;
}

.mini-inbox-container.dark-theme .mini-inbox-content::-webkit-scrollbar-thumb {
  background: #4a4a4b;
}

.mini-inbox-container.dark-theme .mini-inbox-content::-webkit-scrollbar-thumb:hover {
  background: #5a5a5b;
}

/* Styles pour les liens et actions */
.notification-item a {
  color: inherit;
  text-decoration: none;
}

.notification-item a:hover {
  text-decoration: underline;
}

.mini-inbox-container.dark-theme .notification-item a {
  color: #4fbcff;
}

/* Indicateur de position */
.mini-inbox-container.positioned-top::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #ccc;
}

.mini-inbox-container.positioned-top::after {
  content: '';
  position: absolute;
  top: -7px;
  left: 21px;
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ffffff;
}

.mini-inbox-container.dark-theme.positioned-top::after {
  border-bottom-color: #1a1a1b;
}

.mini-inbox-container.positioned-bottom::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #ccc;
}

.mini-inbox-container.positioned-bottom::after {
  content: '';
  position: absolute;
  bottom: -7px;
  left: 21px;
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 7px solid #ffffff;
}

.mini-inbox-container.dark-theme.positioned-bottom::after {
  border-top-color: #1a1a1b;
}/*
 Séparateur de notifications */
.notifications-separator {
  background-color: #f5f5f5;
  color: #666;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  padding: 6px 12px;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
  margin: 0;
  text-align: center;
  letter-spacing: 0.5px;
}

.mini-inbox-container.dark-theme .notifications-separator {
  background-color: #272729;
  color: #818384;
  border-color: #343536;
}

/* Amélioration des titres de posts */
.post-title {
  font-style: italic;
  color: #666;
  font-size: 10px;
}

.mini-inbox-container.dark-theme .post-title {
  color: #818384;
}

/* Styles pour les différents types d'icônes */
.notification-item.comment-reply::before,
.notification-item.username-mention::before,
.notification-item.private-message::before,
.notification-item.post-reply::before {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.notification-item:hover::before {
  opacity: 1;
}

/* Amélioration des animations */
.notification-item.new-notification {
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* États de chargement pour les actions */
.notification-action.loading {
  opacity: 0.5;
  pointer-events: none;
}

.notification-action.loading::after {
  content: '...';
  animation: dots 1s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Amélioration de la lisibilité */
.notification-body {
  text-align: justify;
  hyphens: auto;
}

/* Styles pour l'authentification requise */
.auth-required {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px 20px;
  text-align: center;
}

.auth-required-icon {
  font-size: 48px;
  opacity: 0.7;
}

.auth-required-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.auth-required-subtitle {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16px;
  max-width: 280px;
}

.auth-required-button {
  padding: 12px 24px;
  background: #ff4500;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.auth-required-button:hover {
  background: #cc3300;
  transform: translateY(-1px);
}

/* Indicateur de données de test */
.mock-data-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 10px;
  color: #856404;
  margin-left: 8px;
}

.mock-indicator-icon {
  font-size: 12px;
}

.mock-indicator-text {
  font-weight: 500;
}

/* Thème sombre pour les nouveaux éléments */
.mini-inbox-container.dark-theme .auth-required-title {
  color: #d7dadc;
}

.mini-inbox-container.dark-theme .auth-required-subtitle {
  color: #818384;
}

.mini-inbox-container.dark-theme .mock-data-indicator {
  background: rgba(255, 193, 7, 0.15);
  border-color: rgba(255, 193, 7, 0.4);
  color: #ffc107;
}

/* Styles pour l'interface de réponse rapide */
.quick-reply-interface {
  background: #f8f9fa;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  margin: 8px 0;
  padding: 0;
  font-size: 11px;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reply-header {
  background: #ff4500;
  color: white;
  padding: 6px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px 4px 0 0;
}

.reply-title {
  font-weight: bold;
  font-size: 11px;
}

.reply-close {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  transition: background-color 0.2s;
}

.reply-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.reply-content {
  padding: 10px;
}

.reply-textarea {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-family: inherit;
  font-size: 11px;
  resize: vertical;
  box-sizing: border-box;
  margin-bottom: 8px;
}

.reply-textarea:focus {
  outline: none;
  border-color: #ff4500;
  box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.2);
}

.reply-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.reply-send,
.reply-cancel,
.reply-full {
  padding: 6px 12px;
  border: none;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reply-send {
  background: #ff4500;
  color: white;
}

.reply-send:hover:not(:disabled) {
  background: #cc3300;
}

.reply-send:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.reply-cancel {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.reply-cancel:hover {
  background: #e5e5e5;
}

.reply-full {
  background: #0079d3;
  color: white;
}

.reply-full:hover {
  background: #0066b3;
}

.reply-success {
  padding: 15px;
  text-align: center;
  background: #f0f8ff;
  border-radius: 4px;
}

.success-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.success-message {
  color: #0079d3;
  font-weight: bold;
  margin-bottom: 10px;
}

.success-close {
  background: #0079d3;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
}

.success-close:hover {
  background: #0066b3;
}

.reply-error {
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 3px;
  padding: 8px;
  margin-top: 8px;
}

.error-message {
  color: #d32f2f;
  font-size: 10px;
  font-weight: bold;
}

/* Thème sombre pour l'interface de réponse */
.mini-inbox-container.dark-theme .quick-reply-interface {
  background: #2d2d2d;
  border-color: #444;
}

.mini-inbox-container.dark-theme .reply-textarea {
  background: #1a1a1a;
  border-color: #444;
  color: #d7dadc;
}

.mini-inbox-container.dark-theme .reply-textarea:focus {
  border-color: #ff4500;
}

.mini-inbox-container.dark-theme .reply-cancel {
  background: #444;
  color: #d7dadc;
  border-color: #666;
}

.mini-inbox-container.dark-theme .reply-cancel:hover {
  background: #555;
}

.mini-inbox-container.dark-theme .reply-success {
  background: #1a2332;
}

.mini-inbox-container.dark-theme .reply-error {
  background: #3a1f1f;
  border-color: #5a2d2d;
}

/* Responsive pour l'interface de réponse */
@media (max-width: 480px) {
  .reply-actions {
    flex-direction: column;
  }
  
  .reply-send,
  .reply-cancel,
  .reply-full {
    width: 100%;
    margin-bottom: 4px;
  }
  
  .reply-textarea {
    min-height: 80px;
  }
}

.notification-body a {
  color: #369;
  text-decoration: underline;
}

.mini-inbox-container.dark-theme .notification-body a {
  color: #4fbcff;
}

/* Indicateurs visuels pour les types de notifications */
.notification-item.comment-reply {
  border-left-color: #00a86b;
}

.notification-item.username-mention {
  border-left-color: #ff4500;
}

.notification-item.private-message {
  border-left-color: #7289da;
}

.notification-item.post-reply {
  border-left-color: #ff6b35;
}

/* Amélioration du focus pour l'accessibilité */
.notification-item:focus {
  outline: 2px solid #0079d3;
  outline-offset: -2px;
  background-color: #f0f8ff;
}

.mini-inbox-container.dark-theme .notification-item:focus {
  background-color: #2d1b00;
}

/* Styles pour les notifications groupées */
.notification-group {
  border-bottom: 2px solid #e5e5e5;
  margin-bottom: 8px;
}

.notification-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.mini-inbox-container.dark-theme .notification-group {
  border-bottom-color: #343536;
}

/* Amélioration des tooltips */
.notification-time[title]:hover::after {
  content: attr(title);
  position: absolute;
  background: #333;
  color: white;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 10002;
  top: 100%;
  right: 0;
  margin-top: 4px;
}

/* Responsive pour les très petits écrans */
@media (max-width: 320px) {
  .mini-inbox-container {
    font-size: 10px;
  }
  
  .notification-header {
    padding-left: 14px;
  }
  
  .notification-subject,
  .notification-context,
  .notification-body,
  .notification-actions {
    padding-left: 14px;
  }
  
  .notification-actions {
    font-size: 9px;
  }
}

/* Amélioration de la performance avec will-change */
.mini-inbox-container {
  will-change: transform, opacity;
}

.notification-item {
  will-change: background-color;
}

/* Styles pour les états de connexion */
.mini-inbox-container.offline {
  opacity: 0.7;
}

.mini-inbox-container.offline::after {
  content: 'Mode hors ligne';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: #ff9800;
  color: white;
  text-align: center;
  padding: 4px;
  font-size: 10px;
  font-weight: bold;
}

/* Amélioration des contrastes pour l'accessibilité */
@media (prefers-contrast: high) {
  .notification-item {
    border: 2px solid #000;
  }
  
  .notification-author {
    color: #000;
    font-weight: bold;
  }
  
  .notification-action {
    color: #000;
    text-decoration: underline;
  }
}/*
 Styles pour les interactions utilisateur avancées */

/* Indicateurs de scroll */
.scroll-indicator {
  position: absolute;
  right: 8px;
  width: 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #666;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10002;
  user-select: none;
}

.scroll-indicator:hover {
  background: rgba(0, 0, 0, 0.2);
}

.scroll-indicator-top {
  top: 50px;
}

.scroll-indicator-bottom {
  bottom: 10px;
}

.mini-inbox-container.dark-theme .scroll-indicator {
  background: rgba(255, 255, 255, 0.1);
  color: #ccc;
}

.mini-inbox-container.dark-theme .scroll-indicator:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Sélection au clavier */
.notification-item.keyboard-selected {
  outline: 2px solid #0079d3;
  outline-offset: -2px;
  background-color: #e3f2fd !important;
}

.mini-inbox-container.dark-theme .notification-item.keyboard-selected {
  background-color: #1a237e !important;
}

/* Sélection multiple */
.notification-item.multi-selected {
  background-color: #e8f5e8 !important;
  border-left: 4px solid #4caf50;
}

.mini-inbox-container.dark-theme .notification-item.multi-selected {
  background-color: #1b5e20 !important;
  border-left-color: #66bb6a;
}

.selection-counter {
  background: #4caf50;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  margin-left: 8px;
}

/* Drag and drop */
.notification-item.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
  z-index: 10003;
}

.notification-item[draggable="true"] {
  cursor: move;
}

.notification-item[draggable="true"]:hover::after {
  content: '⋮⋮';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
  font-size: 12px;
  line-height: 0.8;
}

/* États de notification */
.notification-item.viewed {
  opacity: 0.9;
}

.notification-item.viewed::after {
  content: '👁';
  position: absolute;
  right: 4px;
  bottom: 4px;
  font-size: 8px;
  opacity: 0.5;
}

/* Bouton de rafraîchissement */
.refresh-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
  font-size: 12px;
}

.refresh-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.refresh-button.loading {
  animation: spin 1s linear infinite;
  opacity: 0.6;
}

.refresh-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Animations pour les interactions */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.notification-item:active {
  animation: pulse 0.1s ease-out;
}

/* Feedback tactile pour mobile */
@media (hover: none) and (pointer: coarse) {
  .notification-item:active {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(0.98);
    transition: all 0.1s ease;
  }
  
  .mini-inbox-container.dark-theme .notification-item:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Indicateurs de swipe pour mobile */
.swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.swipe-indicator-left {
  left: 20px;
}

.swipe-indicator-right {
  right: 20px;
}

.mini-inbox-container.swiping .swipe-indicator {
  opacity: 0.5;
}

/* Amélioration de l'accessibilité */
.notification-item[tabindex="0"]:focus {
  outline: 2px solid #0079d3;
  outline-offset: -2px;
}

.notification-item[aria-selected="true"] {
  background-color: #e3f2fd;
}

.mini-inbox-container.dark-theme .notification-item[aria-selected="true"] {
  background-color: #1a237e;
}

/* Styles pour les raccourcis clavier */
.keyboard-shortcuts-hint {
  position: absolute;
  bottom: 4px;
  left: 4px;
  font-size: 8px;
  color: #999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mini-inbox-container:hover .keyboard-shortcuts-hint {
  opacity: 1;
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
  .notification-item.keyboard-selected {
    outline: 3px solid #000;
    background-color: #ffff00 !important;
    color: #000;
  }
  
  .notification-item.multi-selected {
    background-color: #00ff00 !important;
    color: #000;
    border-left-color: #000;
  }
}

/* Réduction de mouvement pour l'accessibilité */
@media (prefers-reduced-motion: reduce) {
  .notification-item,
  .scroll-indicator,
  .mini-inbox-container {
    transition: none;
    animation: none;
  }
  
  .mini-inbox-content {
    scroll-behavior: auto;
  }
}

/* Styles pour les très petits écrans */
@media (max-width: 280px) {
  .scroll-indicator {
    width: 16px;
    height: 16px;
    font-size: 8px;
  }
  
  .selection-counter {
    font-size: 8px;
    padding: 1px 6px;
  }
  
  .refresh-button {
    font-size: 10px;
    padding: 2px;
  }
}

/* Mode plein écran pour très petits écrans */
@media (max-width: 320px) and (max-height: 568px) {
  .mini-inbox-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
    border-radius: 0;
    border: none;
  }
  
  .mini-inbox-content {
    max-height: calc(100vh - 100px);
  }
}

/* Amélioration des performances */
.notification-item {
  contain: layout style paint;
}

.mini-inbox-content {
  contain: layout style paint;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* États de connexion */
.mini-inbox-container.loading-more::after {
  content: 'Chargement...';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: center;
  padding: 8px;
  font-size: 11px;
}

/* Indicateurs de statut */
.notification-item.sending::after {
  content: '⏳';
  position: absolute;
  right: 8px;
  top: 8px;
  font-size: 12px;
}

.notification-item.sent::after {
  content: '✓';
  position: absolute;
  right: 8px;
  top: 8px;
  color: #4caf50;
  font-size: 12px;
}

.notification-item.error::after {
  content: '⚠️';
  position: absolute;
  right: 8px;
  top: 8px;
  font-size: 12px;
}

/* Amélioration de la lisibilité */
.notification-item:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

.mini-inbox-container.dark-theme .notification-item:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.02);
}

/* Styles pour les notifications groupées */
.notification-group-header {
  background: linear-gradient(to right, #f5f5f5, #e5e5e5);
  padding: 4px 12px;
  font-size: 10px;
  font-weight: bold;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 10001;
}

.mini-inbox-container.dark-theme .notification-group-header {
  background: linear-gradient(to right, #2a2a2a, #1a1a1a);
  color: #999;
  border-bottom-color: #444;
}/* S
tyles pour les actions de la mini-inbox */
.mini-inbox-header .mini-inbox-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.mini-inbox-header .refresh-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
  font-size: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mini-inbox-header .refresh-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mini-inbox-header .refresh-button.loading {
  animation: spin 1s linear infinite;
  opacity: 0.6;
}

.mini-inbox-header .refresh-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Amélioration des styles d'erreur pour les actions */
.error-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 16px;
}

/* Styles pour les indicateurs de progression avancés */
.loading-progress {
  width: 200px;
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e5e5;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4500, #ff6600);
  width: 0%;
  transition: width 2s ease-out;
  border-radius: 2px;
}

.mini-inbox-container.dark-theme .progress-bar {
  background-color: #343536;
}

/* Amélioration des animations de chargement */
@keyframes loadingPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading-content {
  animation: loadingPulse 2s ease-in-out infinite;
}

/* Styles pour les notifications en cours de marquage */
.notification-item.marking-read::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid #ff4500;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 10001;
  background: rgba(255, 255, 255, 0.9);
}

.mini-inbox-container.dark-theme .notification-item.marking-read::before {
  background: rgba(26, 26, 27, 0.9);
}

/* Amélioration des styles de badge */
.reddit-mini-inbox-badge {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif !important;
  text-align: center !important;
  user-select: none !important;
}

/* Animation d'apparition du badge */
@keyframes badgeAppear {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.reddit-mini-inbox-badge.appear {
  animation: badgeAppear 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Styles pour les états de connexion */
.mini-inbox-container.offline {
  opacity: 0.7;
}

.mini-inbox-container.offline::after {
  content: 'Mode hors ligne';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: #ff9800;
  color: white;
  text-align: center;
  padding: 4px;
  font-size: 10px;
  font-weight: bold;
  z-index: 10003;
}

/* Amélioration de l'accessibilité pour les états de chargement */
.loading-content[aria-busy="true"] {
  outline: none;
}

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Styles pour les messages de statut */
.status-message {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 10px;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10002;
}

.status-message.show {
  opacity: 1;
}

.mini-inbox-container.dark-theme .status-message {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

/* Amélioration des performances avec contain */
.loading-content,
.error,
.no-notifications {
  contain: layout style paint;
}

/* Styles pour les très petits écrans */
@media (max-width: 320px) {
  .mini-inbox-header .mini-inbox-actions {
    gap: 2px;
  }
  
  .mini-inbox-header .refresh-button,
  .mini-inbox-header .close-btn {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
  
  .error-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .retry-button,
  .close-error-button {
    width: 100%;
    max-width: 200px;
  }
}