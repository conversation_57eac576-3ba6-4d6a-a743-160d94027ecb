const fs = require('fs');

const content = fs.readFileSync('scripts/content-new.js', 'utf8');
const lines = content.split('\n');
const line628 = lines[627]; // Index 627 pour ligne 628

console.log('Ligne 628:');
console.log(line628);
console.log('');

console.log('Analyse caractère par caractère:');
let openCount = 0;
let closeCount = 0;

for (let i = 0; i < line628.length; i++) {
  const char = line628[i];
  if (char === '(') {
    openCount++;
    console.log(`Position ${i}: '${char}' - Ouvrante #${openCount}`);
  } else if (char === ')') {
    closeCount++;
    console.log(`Position ${i}: '${char}' - Fermante #${closeCount}`);
  }
}

console.log('');
console.log(`Total ouvrantes: ${openCount}`);
console.log(`Total fermantes: ${closeCount}`);
console.log(`Balance: ${openCount - closeCount}`);