const fs = require('fs');

const content = fs.readFileSync('scripts/content-new.js', 'utf8');
const lines = content.split('\n');

let balance = 0;
let openCount = 0;
let closeCount = 0;
let problemLines = [];

lines.forEach((line, index) => {
  let lineBalance = 0;
  let openInLine = 0;
  let closeInLine = 0;
  
  for (let char of line) {
    if (char === '(') {
      balance++;
      lineBalance++;
      openCount++;
      openInLine++;
    }
    if (char === ')') {
      balance--;
      lineBalance--;
      closeCount++;
      closeInLine++;
    }
  }
  
  // Stocker toutes les lignes avec des parenthèses
  if (openInLine > 0 || closeInLine > 0) {
    problemLines.push({
      lineNum: index + 1,
      line: line.trim(),
      openInLine,
      closeInLine,
      lineBalance,
      totalBalance: balance
    });
  }
});

console.log('Toutes les lignes avec des parenthèses:');
problemLines.forEach(item => {
  console.log(`Ligne ${item.lineNum}: open=${item.openInLine}, close=${item.closeInLine}, balance=${item.lineBalance}, total=${item.totalBalance}`);
  console.log(`  ${item.line}`);
  console.log('');
});

console.log(`\nRésumé final:`);
console.log(`Parenthèses ouvrantes: ${openCount}`);
console.log(`Parenthèses fermantes: ${closeCount}`);
console.log(`Balance finale: ${balance}`);