# 🚀 Guide de Déploiement - Chrome Web Store

Ce guide vous explique comment publier l'extension Reddit Mini Inbox sur le Chrome Web Store.

## 📋 Prérequis

- ✅ Extension complètement testée et fonctionnelle
- ✅ Compte développeur Chrome Web Store (5$ d'inscription unique)
- ✅ Assets graphiques (icônes, captures d'écran)
- ✅ Description et métadonnées préparées

## 🎨 Préparation des Assets

### Icônes requises

Créez les icônes suivantes (déjà présentes dans `/assets/`) :
- `icon16.png` - 16x16px (barre d'outils)
- `icon32.png` - 32x32px (gestion des extensions)
- `icon48.png` - 48x48px (page des extensions)
- `icon128.png` - 128x128px (Chrome Web Store)

### Captures d'écran

Préparez 1-5 captures d'écran :
- **Taille** : 1280x800px ou 640x400px
- **Format** : PNG ou JPEG
- **Contenu** : Montrez l'extension en action sur Reddit

### Exemples de captures recommandées :
1. **Mini-inbox ouverte** avec notifications
2. **Interface d'authentification** du popup
3. **Comparaison avant/après** (avec/sans extension)
4. **Version mobile** responsive
5. **Thème sombre** en action

## 📝 Métadonnées du Store

### Titre
```
Reddit Mini Inbox - Restore Notification Popup
```

### Description courte (132 caractères max)
```
Restore Reddit's mini inbox popup for notifications. Click the bell icon to see messages without leaving your page.
```

### Description détaillée

```
🔔 Restore Reddit's beloved mini inbox popup that was removed in March 2025!

FEATURES:
✅ Click notification bell → Mini inbox opens instantly
✅ View messages without leaving your current page
✅ Authentic Reddit design (light/dark theme support)
✅ Secure OAuth2 authentication
✅ Mark messages as read with one click
✅ Responsive design for all screen sizes
✅ Works on both www.reddit.com and old.reddit.com

PRIVACY & SECURITY:
🔒 No data sent to third parties
🔒 Minimal permissions (read messages only)
🔒 Local storage only
🔒 Official Reddit OAuth2 authentication

SETUP:
1. Install extension
2. Go to Reddit
3. Click the notification bell icon
4. Enjoy your restored mini inbox!

Optional: Connect your Reddit account for real notifications (see setup guide)

Perfect for Reddit power users who miss the convenient notification popup!

Keywords: Reddit, notifications, inbox, popup, mini inbox, messages, bell icon
```

### Catégorie
- **Principale** : Productivity
- **Secondaire** : Social & Communication

### Langue
- Anglais (English)

## 📦 Préparation du Package

### 1. Nettoyage du code

```bash
# Supprimer les fichiers de développement
rm -rf tests/
rm -rf node_modules/
rm .gitignore
rm package-lock.json

# Garder seulement les fichiers essentiels
```

### 2. Vérification du manifest.json

```json
{
  "manifest_version": 3,
  "name": "Reddit Mini Inbox",
  "version": "1.0.0",
  "description": "Restore Reddit's mini inbox popup for notifications",
  
  "permissions": [
    "storage",
    "activeTab",
    "identity"
  ],
  
  "host_permissions": [
    "https://www.reddit.com/*",
    "https://old.reddit.com/*",
    "https://oauth.reddit.com/*"
  ],
  
  "icons": {
    "16": "assets/icon16.png",
    "32": "assets/icon32.png",
    "48": "assets/icon48.png",
    "128": "assets/icon128.png"
  },
  
  "background": {
    "service_worker": "scripts/background.js"
  },
  
  "content_scripts": [
    {
      "matches": [
        "https://www.reddit.com/*",
        "https://old.reddit.com/*"
      ],
      "js": ["scripts/content-clean.js"],
      "css": ["styles/mini-inbox.css"],
      "run_at": "document_end"
    }
  ],
  
  "action": {
    "default_popup": "popup/popup.html",
    "default_title": "Reddit Mini Inbox",
    "default_icon": {
      "16": "assets/icon16.png",
      "32": "assets/icon32.png",
      "48": "assets/icon48.png",
      "128": "assets/icon128.png"
    }
  },
  
  "web_accessible_resources": [
    {
      "resources": ["styles/mini-inbox.css"],
      "matches": ["https://www.reddit.com/*", "https://old.reddit.com/*"]
    }
  ]
}
```

### 3. Création du ZIP

```bash
# Créer l'archive pour le Chrome Web Store
zip -r reddit-mini-inbox-v1.0.0.zip . -x "*.git*" "node_modules/*" "tests/*" "*.md" "DEPLOYMENT.md"
```

## 🌐 Publication sur Chrome Web Store

### 1. Accès au Developer Dashboard

1. Allez sur : https://chrome.google.com/webstore/devconsole
2. Connectez-vous avec votre compte Google
3. Payez les 5$ d'inscription si ce n'est pas fait

### 2. Création de l'extension

1. **Cliquez** sur "New Item"
2. **Uploadez** le fichier ZIP
3. **Remplissez** tous les champs requis :
   - Titre
   - Description
   - Catégorie
   - Langue
   - Captures d'écran
   - Icône du store (128x128)

### 3. Configuration avancée

- **Audience** : Tous âges
- **Régions** : Monde entier
- **Prix** : Gratuit
- **Permissions** : Expliquez pourquoi chaque permission est nécessaire

### 4. Soumission pour révision

1. **Vérifiez** tous les champs
2. **Testez** l'extension une dernière fois
3. **Cliquez** sur "Submit for Review"

## ⏱️ Processus de Révision

- **Durée** : 1-7 jours ouvrables
- **Statut** : Visible dans le dashboard
- **Notifications** : Par email

### Raisons de rejet communes :
- Permissions excessives
- Description peu claire
- Captures d'écran de mauvaise qualité
- Fonctionnalité non conforme aux politiques

## 🔄 Mises à jour

Pour publier une mise à jour :

1. **Modifiez** la version dans `manifest.json`
2. **Créez** un nouveau ZIP
3. **Uploadez** dans le dashboard
4. **Soumettez** pour révision

## 📊 Après Publication

### Analytics
- Consultez les statistiques d'installation
- Surveillez les avis utilisateurs
- Répondez aux commentaires

### Support
- Surveillez les rapports de bugs
- Maintenez la compatibilité Reddit
- Publiez des mises à jour régulières

## 🎯 Conseils pour le Succès

1. **Description claire** : Expliquez clairement la valeur ajoutée
2. **Captures de qualité** : Montrez l'extension en action
3. **Mots-clés pertinents** : Facilitez la découverte
4. **Support réactif** : Répondez aux utilisateurs
5. **Mises à jour régulières** : Maintenez la compatibilité

## 🔒 Politique de Confidentialité

Créez une politique de confidentialité simple :

```
Cette extension ne collecte aucune donnée personnelle.
Toutes les données sont stockées localement sur votre appareil.
L'authentification utilise l'OAuth2 officiel de Reddit.
Aucune donnée n'est envoyée à des serveurs tiers.
```

---

**Bonne chance pour la publication ! 🚀**