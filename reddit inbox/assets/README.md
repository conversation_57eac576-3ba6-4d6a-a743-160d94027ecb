# Assets

Ce dossier contient les ressources de l'extension Reddit Mini Inbox.

## Icônes requises

Les fichiers d'icônes suivants sont nécessaires pour l'extension :

- `icon16.png` - Icône 16x16 pixels
- `icon32.png` - Icône 32x32 pixels  
- `icon48.png` - Icône 48x48 pixels
- `icon128.png` - Icône 128x128 pixels

## Note

Pour l'instant, ces fichiers d'icônes ne sont pas inclus. Vous devrez créer ou obtenir des icônes appropriées pour l'extension avant de la publier sur le Chrome Web Store.

Les icônes devraient représenter visuellement l'extension Reddit Mini Inbox, par exemple avec un symbole de notification ou une enveloppe combiné avec les couleurs de Reddit (#FF4500).